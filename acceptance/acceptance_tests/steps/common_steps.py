import os
import time

from mailosaur.models import MailosaurException

from acceptance.acceptance_tests.helpers.api_helper.general import get_email_via_API, get_email_using_email_body_via_API
from acceptance.acceptance_tests.helpers.api_helper.login import login_and_provide_admin_access_via_API
from acceptance.acceptance_tests.helpers.api_helper.signup import buy_now_via_API, enter_customer_info_via_API, \
    submit_payment_details_to_stripe_via_API, submit_payment_details_to_cybersmart_via_API
from acceptance.acceptance_tests.helpers.api_helper.utils import add_response_cookies_to_driver
from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.helpers.wait_statements import wait_until_logout_displayed
from acceptance.acceptance_tests.pages.non_admin.customer_info import CustomerInfoPage
from acceptance.acceptance_tests.pages.non_admin.device_registeration import DeviceRegistrationPage
from acceptance.acceptance_tests.pages.non_admin.login import LoginPage
from acceptance.acceptance_tests.pages.non_admin.org_dashboard import IndividualOrgDashboardPage
from acceptance.acceptance_tests.pages.non_admin.payment import PaymentPage
from acceptance.acceptance_tests.pages.non_admin.plans import PlansPage
from acceptance.properties_manager import get_auto_test_org_prefix


class CommonSteps:
    def __init__(self, driver):
        self.driver = driver

    def login(self, base_url, username, password):
        login_page = LoginPage(self.driver)
        login_page.login(base_url, username, password)
        wait_until_logout_displayed(self.driver)

    def enter_username_and_password_on_login_page_and_submit(self, base_url, username, password):
        login_page = LoginPage(self.driver)
        login_page.login(base_url, username, password)

    def go_to_home_page(self, base_url):
        self.driver.get(base_url)

    def activate_learn_lite_via_url(self, org_dashboard_url):
        self.driver.get(os.path.join(org_dashboard_url, 'training/journey/complete/'))

    def get_current_url(self):
        return self.driver.current_url

    def login_and_provide_admin_access_using_API(self, base_url, username, password):
        """
        This function is only used for tests that require access to /admin UI
         e.g. https://develop.cybersmart.co.uk/admin/
        Using the API allows us to apply the appropriate credentials to access /admin
        """
        login_page = LoginPage(self.driver)
        login_page.go_to_page(base_url)
        login_and_provide_admin_access_via_API(self.driver, base_url, username, password)
        self.go_to_home_page(base_url)

    def go_to_learn_lite_page(self):
        individual_org_dashboard_pg = IndividualOrgDashboardPage(self.driver)
        individual_org_dashboard_pg.navigate_to_learn_lite()

    def go_to_plans_page(self, base_url):
        self.driver.delete_all_cookies()
        self.driver.get(base_url)
        login_page = LoginPage(self.driver)
        login_page.click_signup()
        plans_page = PlansPage(self.driver)
        plans_page.wait_until_page_loads()
        return plans_page

    def signup_and_make_payment(self, base_url, plan, email_domain, coupon=None):
        step(f'Given a user signs up{" with a coupon " + coupon + " " if coupon else " "}and makes a payment')
        user_email = ''
        company_name = ''

        def enter_customer_info_and_make_payment():
            self.go_to_plans_page(base_url)

            api_response = buy_now_via_API(self.driver, base_url, plan)
            add_response_cookies_to_driver(api_response, self.driver)
            customer_info_pg = CustomerInfoPage(self.driver)
            customer_info_pg.go_to_page(base_url)
            customer_info_pg.wait_until_page_displayed()
            nonlocal company_name
            company_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
            nonlocal user_email
            user_email = f'test{get_transformed_date()}@{email_domain}'
            api_response = enter_customer_info_via_API(self.driver, base_url, user_email, company_name)

            add_response_cookies_to_driver(api_response, self.driver)
            payment_pg = PaymentPage(self.driver)
            payment_pg.go_to_page(base_url)
            payment_pg.wait_until_page_is_displayed()
            api_key = payment_pg.get_stripe_api_key()
            payment_method_id = submit_payment_details_to_stripe_via_API(user_email, api_key)
            csrf_token = payment_pg.get_csrftoken()
            return submit_payment_details_to_cybersmart_via_API(
                self.driver, base_url, csrf_token, payment_method_id, plan, coupon)

        response = ''
        count = 1
        while count <= 10:
            response = enter_customer_info_and_make_payment()
            if count == 10:
                raise Exception(f'Payment details were not submitted. The API response was {response.json()}')
            if response.json().get('status') == 'Paid':
                break
            else:
                time.sleep(1)
                count = count + 1
                self.driver.delete_all_cookies()

        step('And navigates to the org dashboard')
        add_response_cookies_to_driver(response, self.driver)
        self.go_to_home_page(base_url)

        step('Then the check your inbox message is displayed')
        org_dashboard = IndividualOrgDashboardPage(self.driver)
        org_dashboard.wait_until_check_your_inbox_msg_is_displayed()

        customer_id = response.json().get('customer_id')
        return {'user_email': user_email, 'customer_id': customer_id, 'org_name': company_name}

    @staticmethod
    def check_email(user_email, subject, received_after_time=None):
        count = 0
        max_tries = 5
        email = ''
        while count <= max_tries:
            try:
                if received_after_time:
                    email = get_email_via_API(user_email, subject, received_after_time)
                else:
                    email = get_email_via_API(user_email, subject)
                break
            except MailosaurException as e:
                if count == max_tries:
                    raise e
                else:
                    count = count + 1
                    time.sleep(1)
        return email

    @staticmethod
    def check_email_using_email_body(user_email, subject, body):
        count = 0
        max_tries = 5
        email = ''
        while count <= max_tries:
            try:
                email = get_email_using_email_body_via_API(user_email, subject, body)
                break
            except MailosaurException as e:
                if count == max_tries:
                    raise e
                else:
                    count = count + 1
                    time.sleep(1)
        return email

    def register_device(self, base_url, app_user_uuid, encoded_details):
        self.driver.execute_script(f"window.localStorage.setItem('app-user-uuid', '{app_user_uuid}', '{base_url}');")
        self.driver.get(os.path.join(base_url, f'device/register/?data={encoded_details}'))
        device_reg_page = DeviceRegistrationPage(self.driver)
        device_reg_page.wait_until_activation_success_msg_is_displayed()

    def go_to_page_via_email_link(self, email_link, base_url):
        self.driver.get(email_link)
        if 'localhost' in base_url:
            path = self.driver.current_url.split('.co.uk/')[1]
            self.driver.get(os.path.join(base_url, path))

    @staticmethod
    def get_link_from_email(email, link_text):
        email_links = email.html.links
        required_link = [link for link in email_links if link_text in link.text]
        return required_link[0].href
