import concurrent.futures
import os
import uuid

from acceptance.acceptance_tests.components.intune_instructions import IntuneInstructionsComponent
from acceptance.acceptance_tests.components.mdm_instructions import MDMInstructionsComponent
from acceptance.acceptance_tests.helpers.api_helper.device_mgmt import mobile_app_checkin_via_API
from acceptance.acceptance_tests.helpers.date_util import get_current_utc_time_iso, get_transformed_date
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.helpers.wait_statements import wait_until_cmd_executed
from acceptance.acceptance_tests.pages.non_admin.org_dashboard import BulkOrgDashboardPage
from acceptance.acceptance_tests.steps.common_steps import CommonSteps
from acceptance.properties_manager import get_username, get_webhook_body, get_trustd_device_id, \
    get_trustd_customer_id, get_trustd_client_id, get_additional_trustd_device_ids, \
    get_org_uuid

TRUSTD_CLIENT_SECRET = os.getenv('TRUSTD_CLIENT_SECRET')


def get_webhook_payload(base_url, org_type):
    webhook_body = get_webhook_body()
    webhook_body['data']['customer_id'] = get_trustd_customer_id(base_url, org_type)
    webhook_body['data']['id'] = get_trustd_device_id(base_url)
    webhook_body['data']['email'] = get_username('trustd_mobile_user', base_url)
    webhook_body['data']['name'] = f'Host{get_transformed_date()}'
    webhook_body['data']['last_seen'] = get_current_utc_time_iso()
    webhook_body['client_id'] = get_trustd_client_id(base_url)
    webhook_body['client_secret'] = TRUSTD_CLIENT_SECRET
    return webhook_body


def prepare_mdm_webhook_payload(base_url):
    webhook_payload = get_webhook_payload(base_url, org_type='bulk')
    external_device_id = f'device_{get_transformed_date()}'
    external_user_id = f'user_{get_transformed_date()}'
    external_verified_on = "2024-01-01T00:00:00Z"
    webhook_payload['data']['external_device_id'] = external_device_id
    webhook_payload['data']['external_user_id'] = external_user_id
    webhook_payload['data']['external_verified_on'] = external_verified_on
    return webhook_payload


def check_device_on_device_detail_page(device_detail_page, base_url, appuser_uuid, device_uuid):
    org_uuid = get_org_uuid(org_type='trustd_bulk_org', base_url=base_url)

    def verify_device_is_displayed():
        device_detail_page.go_to_mobile_device_detail_page(base_url, org_uuid, appuser_uuid, device_uuid)
        device_detail_page.device_is_displayed(device_uuid)

    max_tries = 3
    polling_seconds = 1
    wait_until_cmd_executed(verify_device_is_displayed, max_tries, polling_seconds)


def test_webhook_response_is_as_expected_CUSTEAM_T811(base_url):
    step('Given a webhook request is made for mobile app checkin')
    response = mobile_app_checkin_via_API(base_url, get_webhook_payload(base_url, org_type='individual'))

    step('Then the top level response structure is as expected')
    response_json = response.json()
    assert isinstance(response_json, dict), "Expected the top level to be a dictionary"

    step('And the data key and structure are as expected')
    assert 'data' in response_json, "Expected 'data' key in the response"
    data = response_json['data']
    assert isinstance(data, dict), "Expected 'data' to be a dictionary"

    required_keys = {
        'id': str,
        'customer_id': str,
        'deleted': bool,
        'created': str,
        'email': str,
        'emails_sent': int,
        'enrolled_on': str,
        'last_seen': str,
        'managed_by': str,
        'name': str,
        'platform': str,
        'device_model': str,
        'device_make': str,
        'device_os_version': str,
        'test_data': bool,
        'app_version': str,
        'status': str,
        'device_type': str,
        'external_device_id': str,
        'external_user_id': str,
        'external_verified_on': str
    }

    for key, expected_type in required_keys.items():
        assert key in data, f"Expected key '{key}' in data"
        assert isinstance(data[key], expected_type), \
            f"Expected '{key}' to be of type {expected_type.__name__}, but got {type(data[key]).__name__}"

    step('And the risk_rating structure is as expected')
    risk_rating = data['risk_rating']
    assert isinstance(risk_rating, dict), "Expected 'risk_rating' to be a dictionary"
    risk_rating_keys = {
        'rating': str,
        'high_risk_count': int,
        'low_risk_count': int,
        'medium_risk_count': int,
        'calculation_source': str,
        'updated': str
    }
    for key, expected_type in risk_rating_keys.items():
        assert key in risk_rating, f"Expected key '{key}' in risk_rating"
        assert isinstance(risk_rating[key], expected_type), \
            f"Expected '{key}' in risk_rating to be of type {expected_type.__name__}," \
            f" but got {type(risk_rating[key]).__name__}"

    step('And the device_risk_state structure is as expected')
    device_risk_state = data['device_risk_state']
    assert isinstance(device_risk_state, dict), "Expected 'device_risk_state' to be a dictionary"
    device_risk_state_keys = {
        'risk_rating': str,
        'calculated_risk_rating': str,
        'risks': list
    }
    for key, expected_type in device_risk_state_keys.items():
        assert key in device_risk_state, f"Expected key '{key}' in device_risk_state"
        assert isinstance(device_risk_state[key], expected_type), \
            f"Expected '{key}' in device_risk_state to be of type {expected_type.__name__}," \
            f" but got {type(device_risk_state[key]).__name__}"

    step('And the structure of each risk item is as expected')
    for risk in device_risk_state['risks']:
        assert isinstance(risk, dict), "Expected each item in 'risks' to be a dictionary"
        risk_keys = {
            'indicator': str,
            'risk_rating': str,
            'result_code': int,
            'created_local': str,
            'category': str,
            'created_utc': str,
            'calculated_risk_rating': str
        }
        for key, expected_type in risk_keys.items():
            assert key in risk, f"Expected key '{key}' in risk item"
            assert isinstance(risk[key], expected_type), \
                f"Expected '{key}' in risk item to be of type {expected_type.__name__}," \
                f" but got {type(risk[key]).__name__}"


def test_risk_rating_and_category_values_are_as_expected_CUSTEAM_T812(base_url):
    step('Given a webhook request is made for mobile app checkin')
    webhook_payload = get_webhook_payload(base_url, org_type='individual')
    response = mobile_app_checkin_via_API(base_url, webhook_payload)

    step('Then risk_rating, category and calculated_risk_rating for each indicator are as expected')
    response_json = response.json()
    request_risks = webhook_payload['data']['device_risk_state']['risks']
    response_risks = response_json['data']['device_risk_state']['risks']

    assert len(request_risks) == len(response_risks), \
        "The number of indicators in the request and response does not match"

    for response_risk in response_risks:
        matching_data = [request_risk for request_risk in request_risks if
                         request_risk['indicator'] == response_risk['indicator']]
        assert len(matching_data) > 0, f"Indicator '{response_risk['indicator']}' in response not found in request"

        matching_request_risk = matching_data[0]
        assert matching_request_risk['risk_rating'] == response_risk['risk_rating'], \
            f"Risk rating for '{response_risk['indicator']}' does not match request"

        assert matching_request_risk['category'] == response_risk['category'], \
            f"Category for '{response_risk['indicator']}' does not match request"

        assert matching_request_risk['calculated_risk_rating'] == response_risk['calculated_risk_rating'], \
            f"Calculated risk rating for '{response_risk['indicator']}' does not match request"


def test_webhook_request_errors_on_incorrect_client_id_CUSTEAM_T813(base_url):
    step('Given a webhook request is made for mobile app checkin with an incorrect client id')
    webhook_payload = get_webhook_payload(base_url, org_type='individual')
    webhook_payload['client_id'] = 'incorrect client id'
    response = mobile_app_checkin_via_API(base_url, webhook_payload)

    step('Then the response status code is a 403 error')
    assert response.status_code == 403


def test_webhook_request_errors_on_incorrect_client_secret_CUSTEAM_T814(base_url):
    step('Given a webhook request is made for mobile app checkin with an incorrect client secret')
    webhook_payload = get_webhook_payload(base_url, org_type='individual')
    webhook_payload['client_secret'] = 'incorrect client secret'
    response = mobile_app_checkin_via_API(base_url, webhook_payload)

    step('Then the response status code is a 403 error')
    assert response.status_code == 403


def test_webhook_request_errors_on_non_existent_device_CUSTEAM_T815(base_url):
    step('Given a webhook request is made for mobile app checkin with a non-existent device id')
    webhook_payload = get_webhook_payload(base_url, org_type='individual')
    webhook_payload['data']['id'] = 'non existent device id'
    response = mobile_app_checkin_via_API(base_url, webhook_payload)

    step('Then the response status code is a 400 error')
    assert response.status_code == 400


def test_parallel_mobile_app_checkins_do_not_cause_errors_CUSTEAM_T816(base_url):
    step('Given multiple webhook requests are made for mobile app checkin, each with a unique device ID')
    device_ids = get_additional_trustd_device_ids(base_url)

    def update_payload_with_device_id(device_id):
        webhook_payload = get_webhook_payload(base_url, org_type='individual')
        webhook_payload['data']['id'] = device_id
        webhook_payload['data']['device_model'] = f'Device_model_{get_transformed_date()}'
        return webhook_payload

    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = [executor.submit(mobile_app_checkin_via_API, base_url, update_payload_with_device_id(device_id)) for
                   device_id in device_ids]

    step('Then all requests should be successfully return a 201 response')
    for future in concurrent.futures.as_completed(futures):
        response = future.result()
        assert response.status_code == 201, f"Expected status code 201, but got {response.status_code}"


# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_device_detail_page_is_updated_correctly_after_mobile_app_checkin_CUSTEAM_T817(driver, base_url, password):
#     step('Given a user is on the device detail page')
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, get_username('beta_partner_user', base_url), password)
#     org_dashboard_pg = IndividualOrgDashboardPage(driver)
#     org_dashboard_pg.go_to_page(base_url, get_org_uuid('trustd_individual_org', base_url))
#     org_dashboard_pg.wait_until_page_is_displayed()
#
#     appuser_uuid = get_appuser_uuid(user_type='trustd_individual_org', base_url=base_url)
#     org_uuid = get_org_uuid(org_type='trustd_individual_org', base_url=base_url)
#     device_detail_pg = DeviceDetailPage(driver)
#     device_detail_pg.go_to_mobile_device_detail_page(base_url, org_uuid, appuser_uuid, get_trustd_device_id(base_url))
#
#     check_name = 'Supported Operating System'
#     step(f'And views the result of {check_name} check')
#     is_check_successful = device_detail_pg.is_check_passing(check_name)
#
#     step('When an app checkin occurs with the opposite result of that check')
#     risk_indicator = 'OS supported by Android'
#     indicator_found = False
#     risk_rating = 'HIGH' if is_check_successful else 'INFO'
#     webhook_payload = get_webhook_payload(base_url, org_type='individual')
#     for risk in webhook_payload["data"]["device_risk_state"]["risks"]:
#         if risk["indicator"] == risk_indicator:
#             risk["risk_rating"] = risk_rating
#             risk["calculated_risk_rating"] = risk_rating
#             indicator_found = True
#             break
#     if not indicator_found:
#         raise ValueError(f'Indicator ${risk_indicator} not found in webhook_body')
#     response = mobile_app_checkin_via_API(base_url, webhook_payload)
#     assert response.status_code == 201
#
#     step('And the device detail page is refreshed')
#     step(f'Then the result of {check_name} check is as expected')
#
#     def assert_results():
#         device_detail_pg.refresh()
#         if is_check_successful:
#             assert not device_detail_pg.is_check_passing(check_name)
#         else:
#             assert device_detail_pg.is_check_passing(check_name)
#
#     # This wait statement is used because the mobile app checkin is not always instant.
#     # Therefore, this command will wait for a maximum of 60 seconds to allow for any delay in app checkin
#     wait_until_command_executed(
#         assert_results, max_tries=60, polling_seconds=1, specific_exception=(AssertionError, TimeoutException))

# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_webhook_mdm_device_creation_with_EMM_but_without_email_CUSTEAM_T830(driver, base_url, password):
#     step('Given a webhook request is prepared for mobile app checkin with the new mdm fields')
#     webhook_payload = prepare_mdm_webhook_payload(base_url)
#     device_uuid = str(uuid.uuid4())
#     webhook_payload['data']['id'] = device_uuid
#
#     step('And the email in the request blank')
#     webhook_payload['data']['email'] = ''
#
#     step('And the managed_by value in the request is EMM')
#     webhook_payload['data']['managed_by'] = 'EMM'
#
#     step('When the request is made')
#     response = mobile_app_checkin_via_API(base_url, webhook_payload)
#
#     step('Then the request is successful')
#     assert response.status_code == 201
#
#     step('And the response contains the new mdm fields')
#     response_json = response.json()
#     assert response_json['data']['external_device_id'] == webhook_payload['data']['external_device_id']
#     assert response_json['data']['external_user_id'] == webhook_payload['data']['external_user_id']
#     assert response_json['data']['external_verified_on'] == webhook_payload['data']['external_verified_on']
#
#     step('And a new device is created')
#     step('And the device detail page for the checked in device can be accessed')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(
#         base_url, get_username('beta_partner_user', base_url), password)
#     appuser_uuid = get_appuser_uuid(user_type='trustd_bulk_org', base_url=base_url)
#     device_detail_page = DeviceDetailPage(driver)
#     check_device_on_device_detail_page(device_detail_page, base_url, appuser_uuid, device_uuid)
#
#     step('And the last user field is populated correctly')
#     def action():
#         device_detail_page.refresh()
#         assert 'unassigned' in device_detail_page.get_last_user()
#
#     wait_until_command_executed(action, max_tries=6, polling_seconds=1, specific_exception=AssertionError)
#
#     step('And the app install is linked to the bulk deployment app user')
#     app_install_admin_pg = AppInstallAdminPage(driver)
#     assert 'bulk_deploy' in app_install_admin_pg.get_app_user(base_url, device_uuid)


# @pytest.mark.parametrize('managed_by', ['CONTROL', 'EMM'])
# def test_webhook_mdm_device_creation_with_CONTROL_or_EMM_and_with_email_CUSTEAM_T938(
#         driver, base_url, password, managed_by):
#     step('Given a webhook request is prepared for mobile app checkin with the new mdm fields')
#     webhook_payload = prepare_mdm_webhook_payload(base_url)
#     device_uuid = str(uuid.uuid4())
#     webhook_payload['data']['id'] = device_uuid
#
#     step('And the email in the request is populated')
#     email_part = f'email{get_transformed_date()}'
#     full_email = f'{email_part}@example.com'
#     webhook_payload['data']['email'] = full_email
#
#     step(f'And the managed_by value in the request is {managed_by}')
#     webhook_payload['data']['managed_by'] = managed_by
#
#     step('When the request is made')
#     response = mobile_app_checkin_via_API(base_url, webhook_payload)
#
#     step('Then the request is successful')
#     assert response.status_code == 201
#
#     step('And the response contains the new mdm fields')
#     response_json = response.json()
#     assert response_json['data']['external_device_id'] == webhook_payload['data']['external_device_id']
#     assert response_json['data']['external_user_id'] == webhook_payload['data']['external_user_id']
#     assert response_json['data']['external_verified_on'] == webhook_payload['data']['external_verified_on']
#
#     step('And a new device is created')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(
#         base_url, get_username('beta_partner_user', base_url), password)
#     app_install_admin_pg = AppInstallAdminPage(driver)
#
#     def action():
#         app_install_admin_pg.get_app_user_uuid(base_url, device_uuid)
#
#     wait_until_command_executed(action, max_tries=5, polling_seconds=1, specific_exception=NoSuchElementException)
#
#     step('And the device detail page for the checked in device can be accessed')
#     appuser_uuid = app_install_admin_pg.get_app_user_uuid(base_url, device_uuid)
#     device_detail_page = DeviceDetailPage(driver)
#     check_device_on_device_detail_page(device_detail_page, base_url, appuser_uuid, device_uuid)
#
#     step('And the last user field is populated correctly')
#     def action():
#         device_detail_page.refresh()
#         assert email_part in device_detail_page.get_last_user()
#
#     wait_until_command_executed(action, max_tries=6, polling_seconds=1, specific_exception=AssertionError)
#
#
#     step('And the app install is linked to the email provided in the request')
#     app_install_admin_pg = AppInstallAdminPage(driver)
#     assert full_email in app_install_admin_pg.get_app_user(base_url, device_uuid)


def test_webhook_mdm_device_creation_with_control_and_without_email_CUSTEAM_T939(driver, base_url):
    step('Given a webhook request is prepared for mobile app checkin with the new mdm fields')
    webhook_payload = prepare_mdm_webhook_payload(base_url)
    device_uuid = str(uuid.uuid4())
    webhook_payload['data']['id'] = device_uuid

    step('And the email in the request blank')
    webhook_payload['data']['email'] = ''

    step('And the managed_by value in the request is CONTROL')
    webhook_payload['data']['managed_by'] = 'CONTROL'

    step('When the request is made')
    response = mobile_app_checkin_via_API(base_url, webhook_payload)

    step('Then the request fails with 400 error')
    assert response.status_code == 400

    step('And the response specifies that email is required')
    response_json = response.json()
    assert 'Email is required' in response_json['data']['non_field_errors'][0]


# def test_get_and_update_policies_for_trustd_CUSTEAM_T839(driver, base_url, username, password):
#     step(f'Given a user {username} is logged in')
#     soup = login_and_get_API_credentials(driver, base_url, username, password)
#
#     step('And there is a bulk non-UBA org with 1 appuser with 1 appinstall')
#     step('And the policy is currently neither read nor agreed')
#     trustd_device_id = get_trustd_policy_device_id(base_url)
#     policy_name = get_policy_name_for_trustd()
#     delete_policy_agreements(driver, base_url, soup, policy_name)
#
#     step('And a GET request is made to the policies endpoint api/v3/app-user-policies')
#     client_id = get_trustd_client_id(base_url)
#     encoded_bytes = f'{client_id}:{TRUSTD_CLIENT_SECRET}'.encode('utf-8')
#     auth_token = base64.b64encode(encoded_bytes).decode('utf-8')
#     response = get_policies_for_trustd_via_API_beta(base_url, trustd_device_id, auth_token)
#
#     step('And the request is successful')
#     assert response.status_code == 200
#
#     step('And the policy version uuid is returned')
#     response_json = response.json()[0]
#     policy_version_uuid = response_json.get('uuid')
#     assert policy_version_uuid
#
#     step('And the policy status is neither read nor agreed')
#     assert not response_json["read"]
#     assert not response_json["agreed"]
#
#     step('When a webhook request is made to update the policy to read and agreed')
#     webhook_body = get_webhook_body()
#     webhook_body['data']['customer_id'] = get_trustd_policy_customer_id(base_url)
#     webhook_body['data']['id'] = trustd_device_id
#     webhook_body['data']['email'] = get_username('beta_partner_user', base_url)
#     webhook_body['data']['name'] = get_username('beta_partner_user', base_url)
#     webhook_body['data']['last_seen'] = get_current_utc_time_iso()
#     webhook_body['client_id'] = client_id
#     webhook_body['client_secret'] = TRUSTD_CLIENT_SECRET
#     read_and_agreed_policy_indicator = {
#         "indicator": policy_version_uuid,
#         "risk_rating": 'INFO',
#         "calculated_risk_rating": 'INFO',
#         "result_code": 2220,
#         "created_local": '2024-01-11T12:30:51.364+0000',
#         "category": "POLICY_DOCUMENT",
#         "created_utc": '2024-01-11T12:30:51.364+0000',
#         "read_datetime": '2024-01-10T12:30:51.364+0000'
#     }
#     webhook_body["data"]["device_risk_state"]["risks"].append(read_and_agreed_policy_indicator)
#     response = mobile_app_checkin_via_API(base_url, webhook_body)
#
#     step('Then the request is successful')
#     assert response.status_code == 201
#
#     step('And the policy status is read and agreed')
#
#     def assert_results():
#         _response = get_policies_for_trustd_via_API_beta(base_url, trustd_device_id, auth_token)
#         _response_json = _response.json()[0]
#         assert _response_json["read"]
#         assert _response_json["agreed"]
#
#     wait_until_command_executed(
#         assert_results, max_tries=60, polling_seconds=2, specific_exception=AssertionError)


def test_mdm_instructions_are_correctly_displayed_via_org_dashboard_CUSTEAM_T941(driver, base_url, password):
    step('Given a user is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, get_username('beta_partner_user', base_url), password)

    step('And is on the bulk org dashboard page')
    org_dashboard_pg = BulkOrgDashboardPage(driver)
    org_uuid = get_org_uuid(org_type='trustd_bulk_org', base_url=base_url)
    org_dashboard_pg.go_to_page(base_url, org_uuid)

    step('And the user views the MDM instructions')
    mdm_instructions_component = MDMInstructionsComponent(driver)

    step('And the enrolment key row is displayed correctly')
    mdm_instructions_component.enrolment_key_row_is_displayed_correctly()


def test_intune_instructions_are_correctly_displayed_via_org_dashboard_T942(driver, base_url, password):
    step('Given a user is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, get_username('beta_partner_user', base_url), password)

    step('And is on the bulk org dashboard page')
    org_dashboard_pg = BulkOrgDashboardPage(driver)
    org_uuid = get_org_uuid(org_type='trustd_bulk_org', base_url=base_url)
    org_dashboard_pg.go_to_page(base_url, org_uuid)

    step('And the user views the Intune instructions')
    intune_instructions_component = IntuneInstructionsComponent(driver)

    step('Then the intune modal title is correctly displayed')
    assert 'microsoft intune' in intune_instructions_component.get_intune_modal_title().lower()
