from acceptance.acceptance_tests.helpers.api_helper.device_mgmt import get_rules_via_API, \
    simulate_vulnerabilities_via_v5_API
from acceptance.acceptance_tests.helpers.common_functions import login_and_get_API_credentials
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.properties_manager import get_appinstall_4


def device_check_is_as_expected(question_id, question_title, rules):
    question = list(filter(lambda x: x['question']['id'] == question_id, rules))
    return question[0]['question']['title'].lower() == question_title


def get_rules(driver, base_url, soup, device_type):
    appinstall = get_appinstall_4(base_url, device_type)
    appinstall.update({
        "app_version": "4.10.1"
    })
    response = get_rules_via_API(driver, base_url, appinstall, soup)
    return response.json().get('rules')


def assert_common_desktop_checks_are_as_expected(rules):
    assert device_check_is_as_expected(8, 'firewall enabled', rules)
    assert device_check_is_as_expected(15, 'password', rules)
    assert device_check_is_as_expected(18, 'anti-malware installed and running', rules)
    assert device_check_is_as_expected(28, 'native full disk encryption enabled', rules)
    assert device_check_is_as_expected(45, 'operating system is supported', rules)


def assert_common_mobile_checks_are_as_expected(rules):
    assert device_check_is_as_expected(30, 'pin enabled', rules)
    assert device_check_is_as_expected(33, 'phone operating system supported', rules)
    assert device_check_is_as_expected(34, 'mobile operating system secure', rules)


# # Test data for this test on develop needs to be reviewed
# @skip_unless_staging()
# def test_app_checkin_updates_front_end_and_back_end_correctly_CUSTEAM_T727(driver, base_url, username, password):
#     step('Given a user is logged in')
#     soup = login_and_get_API_credentials(driver, base_url, username, password)
#
#     step('And is on the device details page')
#     bulk_org_dashboard_pg = BulkOrgDashboardPage(driver)
#     bulk_org_dashboard_pg.go_to_page(base_url, get_org_uuid('bulk_deployment_org', base_url))
#     app_install = get_appinstall_1(base_url)
#     bulk_org_dashboard_pg.go_to_device_detail_page(app_install.get('device_name'))
#
#     step('And views the result of automatic OS updating enabled check')
#     device_detail_pg = DeviceDetailPage(driver)
#     check_name = 'Automatic Operating System updating is enabled'
#     is_auto_os_updating_check_successful = device_detail_pg.is_check_passing(check_name)
#
#     step('When an app checkin occurs with the opposite result of that check')
#     unique_id = get_transformed_date()
#     response = app_checkin_via_API(
#         driver, base_url, soup, app_install, unique_id, False if is_auto_os_updating_check_successful else True)
#     assert response.status_code == 201
#     assert 'Your Response is Sent' in response.text
#
#     step('And the page is refreshed')
#     step('Then the new results are displayed')
#
#     def assert_results():
#         device_detail_pg.refresh()
#         if is_auto_os_updating_check_successful:
#             assert not device_detail_pg.is_check_passing(check_name)
#         else:
#             assert device_detail_pg.is_check_passing(check_name)
#
#     # This wait statement is used because the app checkin is not always instant.
#     # Therefore, this command will wait for a maximum of 60 seconds to allow for any delay in app checkin
#     wait_until_command_executed(
#         assert_results, max_tries=60, polling_seconds=1, specific_exception=(AssertionError, TimeoutException))
#
#     step('And the check in app results has been sent correctly to the back end')
#     checkin_app_results_pg = CheckinAppResultsPage(driver)
#     checkin_app_results_pg.search(base_url, app_install.get('device_id'))
#     assert checkin_app_results_pg.get_product_name() == f'Avast Security {unique_id}'
#
#     step('And the network interfaces has been sent correctly to the back end')
#     network_interfaces_pg = NetworkInterfacesPage(driver)
#     network_interfaces_pg.search(base_url, app_install.get('device_id'))
#     assert network_interfaces_pg.get_mac_address() == f'5c:52:30:9c:de:{unique_id}'


def test_correct_checks_are_returned_for_windows_devices_CUSTEAM_T734(driver, base_url, username, password):
    step('Given a user is logged in')
    soup = login_and_get_API_credentials(driver, base_url, username, password)

    device_type = 'windows'
    step(f'Then the correct checks are returned for a {device_type} device')
    rules = get_rules(driver, base_url, soup, device_type)
    assert device_check_is_as_expected(5, 'autoplay disabled', rules)
    assert_common_desktop_checks_are_as_expected(rules)


def test_correct_checks_are_returned_for_mac_devices_CUSTEAM_T735(driver, base_url, username, password):
    step('Given a user is logged in')
    soup = login_and_get_API_credentials(driver, base_url, username, password)

    device_type = 'mac'
    step(f'Then the correct checks are returned for a {device_type} device')
    rules = get_rules(driver, base_url, soup, device_type)
    assert device_check_is_as_expected(2, 'gatekeeper enabled', rules)
    assert device_check_is_as_expected(11, 'stealth mode enabled', rules)
    assert device_check_is_as_expected(23, 'automatic app updates enabled', rules)
    assert_common_desktop_checks_are_as_expected(rules)


def test_correct_checks_are_returned_for_android_devices_CUSTEAM_T736(driver, base_url, username, password):
    step('Given a user is logged in')
    soup = login_and_get_API_credentials(driver, base_url, username, password)

    device_type = 'android'
    step(f'Then the correct checks are returned for an {device_type} device')
    rules = get_rules(driver, base_url, soup, device_type)
    assert device_check_is_as_expected(31, 'device is not rooted', rules)
    assert_common_mobile_checks_are_as_expected(rules)


def test_correct_checks_are_returned_for_ios_devices_CUSTEAM_T737(driver, base_url, username, password):
    step('Given a user is logged in')
    soup = login_and_get_API_credentials(driver, base_url, username, password)

    device_type = 'ios'
    step(f'Then the correct checks are returned for an {device_type} device')
    rules = get_rules(driver, base_url, soup, device_type)
    assert device_check_is_as_expected(32, 'device is not jailbroken', rules)
    assert_common_mobile_checks_are_as_expected(rules)


def test_v5_cap_vulnerable_checks_API_is_working_as_expected_CUSTEAM_T790(base_url):
    step('Given a POST request is made to the v5 vulnerabilities API')
    appinstall = get_appinstall_4(base_url, 'mac')
    appuser_uuid = appinstall.get('uuid')
    device_id = appinstall.get('device_id')
    serial_number = appinstall.get('serial_number')
    response = simulate_vulnerabilities_via_v5_API(base_url, appuser_uuid, device_id, serial_number)

    step('Then the response is as expected')
    response_json = response.json()
    assert isinstance(response_json, list), "Expected a list in the response"
    assert len(response_json) > 0, "Expected a non-empty response list"

    required_keys = {
        'vendor': str,
        'product': str,
        'version': str,
        'severity': str,
        'score': float
    }
    for item in response_json:
        assert isinstance(item, dict), f"Expected each item in the response to be a dictionary, got {type(item)}"

        for key, expected_type in required_keys.items():
            assert key in item, f"Expected key {key} in item {item}"
            assert isinstance(item[key],
                              expected_type), f"Expected {key} to be of type {expected_type}, but got {type(item[key])}"


def test_error_handling_in_v5_cap_vuln_checks_API_is_working_as_expected_CUSTEAM_T791(base_url):
    step('Given a POST request is made to the v5 vulnerabilities API with an invalid appuser-uuid')
    appinstall = get_appinstall_4(base_url, 'mac')
    device_id = appinstall.get('device_id')
    serial_number = appinstall.get('serial_number')
    response = simulate_vulnerabilities_via_v5_API(base_url, "invalid_uuid", device_id, serial_number)

    step('Then the response contains the correct error message and status code')
    status_code = response.status_code
    response_json = response.json()
    assert status_code == 403
    assert 'appuser-uuid is invalid' in response_json.get('detail').lower()
