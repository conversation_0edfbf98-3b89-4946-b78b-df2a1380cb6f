# def test_create_cybersmart_complete_bulk_org_CUSTEAM_T705(driver, base_url, username, password):
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#
#     step('And creates a CyberSmart Complete bulk deployment org')
#     create_org_page = CreateOrgPage(driver)
#     create_org_page.go_to_page(base_url)
#     company_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
#     create_org_page.fill_create_org_form_with_cs_complete_bulk(company_name)
#
#     step('When the org is clicked on')
#     partner_orgs_pg = PartnerOrgsPage(driver)
#     partner_orgs_pg.go_to_page(base_url)
#     partner_orgs_pg.search_for_org(base_url, company_name)
#     partner_orgs_pg.go_to_org_dashboard(company_name)
#
#     step('Then the devices installed page is displayed')
#     bulk_org_dashboard_pg = BulkOrgDashboardPage(driver)
#     bulk_org_dashboard_pg.devices_installed_label_is_displayed()


# def test_create_org_with_mismatching_domains_CUSTEAM_T723(driver, base_url, username, password):
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#
#     step('And is on the create org page')
#     create_org_page = CreateOrgPage(driver)
#     create_org_page.go_to_page(base_url)
#
#     step('And completes the form with different values for org website and approve domains')
#     create_org_page = CreateOrgPage(driver)
#     company_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
#     create_org_page.enter_org_name_and_industry(company_name)
#     create_org_page.enter_org_domain(company_name)
#     create_org_page.enter_org_size()
#     create_org_page.click_complete_btn()
#     create_org_page.click_bulk_deployment()
#     create_org_page.enter_approved_domain("mismatching_name.com")
#
#     step('When the form is submitted')
#     create_org_page.submit_form()
#
#     step('Then an error is displayed indicating the domains must match')
#     create_org_page.mismatched_domain_err_is_displayed()


# @pytest.mark.parametrize('billing', ['monthly'])
# def test_create_cybersmart_complete_individual_deployment_org_CUSTEAM_T724(
#         driver, base_url, username, password, billing):
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#
#     step(f'When a CyberSmart Complete individual deployment org with {billing} billing is created')
#     create_org_page = CreateOrgPage(driver)
#     create_org_page.go_to_page(base_url)
#     company_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
#     create_org_page.fill_create_org_form_with_cs_complete_individual(company_name, billing)
#
#     step('Then the new org is displayed on orgs list page')
#     partner_orgs_pg = PartnerOrgsPage(driver)
#     partner_orgs_pg.go_to_page(base_url)
#     partner_orgs_pg.wait_until_org_displayed(company_name)
#
#     step('And the certifications section on the orgs list page is as expected')
#     cert_container_text = partner_orgs_pg.get_cert_container_text(company_name)
#     assert 'Cyber Essentials 2025: Not started' in cert_container_text
#     assert 'Add CE+ module' in cert_container_text
