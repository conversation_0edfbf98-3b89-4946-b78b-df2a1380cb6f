import pytest

from acceptance.acceptance_tests.helpers.api_helper.certos import start_survey_via_API, issue_certificate_via_API, \
    set_certificate_to_expired_via_API
from acceptance.acceptance_tests.helpers.api_helper.org_creation import create_custom_org_with_CE_only_via_API
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.pages.admin.organisation_certifications_admin import OrgCertificationsAdminPage
from acceptance.acceptance_tests.pages.admin.organisations_admin import OrgAdminPage
from acceptance.acceptance_tests.pages.non_admin.partner_dashboard import PartnerDashboardPage
from acceptance.acceptance_tests.steps.common_steps import CommonSteps


# def test_page_links_lead_to_correct_pages_CUSTEAM_T725(driver, base_url, username, password):
#     step(f'Given a user {username} is on the partner dashboard page')
#     common_steps = CommonSteps(driver)
#     common_steps.login(base_url, username, password)
#     partner_dashboard_pg = PartnerDashboardPage(driver)
#     partner_dashboard_pg.go_to_page(base_url)
#
#     step('Then the certifications link leads to certificates page')
#     partner_dashboard_pg.click_certs_link()
#     certs_page = CertificationsPage(driver)
#     certs_page.wait_until_page_is_loaded()
#
#     step('Then the certifications expiring link leads to certificates page')
#     partner_dashboard_pg.go_to_page(base_url)
#     partner_dashboard_pg.click_certs_expiring_link()
#     certs_page.wait_until_page_is_loaded()
#
#     step('Then the organisations link leads to the old partner dashboard page')
#     partner_dashboard_pg.go_to_page(base_url)
#     partner_dashboard_pg.click_orgs_link()
#     partner_orgs_pg = PartnerOrgsPage(driver)
#     partner_orgs_pg.wait_until_page_is_displayed()
#
#     step('Then the devices link leads to devices page')
#     partner_dashboard_pg.go_to_page(base_url)
#     partner_dashboard_pg.click_devices_link()
#     devices_pg = DevicesPage(driver)
#     devices_pg.wait_until_page_is_loaded()


@pytest.mark.flaky(reruns=1, reruns_delay=2)
def test_partner_dashboard_widgets_are_populated_CUSTEAM_T945(driver, base_url, username, password):
    step(f'Given a user {username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login_and_provide_admin_access_using_API(base_url, username, password)

    step('When a custom org with CE only is created')
    org_details = create_custom_org_with_CE_only_via_API(driver, base_url)

    step('And the survey is started')
    org_uuid = org_details.get('org_uuid')
    start_survey_via_API(driver, base_url, org_uuid)

    step('And and the CE certificate is issued')
    org_cert_admin_page = OrgCertificationsAdminPage(driver)
    org_name = org_details.get('org_name')
    org_cert_id = org_cert_admin_page.get_org_cert_id(base_url, org_name)
    issue_certificate_via_API(driver, base_url, org_cert_id)

    step('And set to expired')
    org_admin_page = OrgAdminPage(driver)
    org_id = org_admin_page.get_org_id(base_url, org_name)
    set_certificate_to_expired_via_API(driver, base_url, org_cert_id, org_id)

    step('And the user navigates to the partner dashboard page')
    partner_dashboard_pg = PartnerDashboardPage(driver)
    partner_dashboard_pg.go_to_page(base_url)

    step('Then the cert progress widget is populated')
    partner_dashboard_pg.cert_progress_widget_is_populated()

    step('And the cert expiration widget is populated')
    partner_dashboard_pg.cert_expiration_widget_is_populated()

    step('And the device issues widget is populated')
    partner_dashboard_pg.device_issues_widget_is_populated()
