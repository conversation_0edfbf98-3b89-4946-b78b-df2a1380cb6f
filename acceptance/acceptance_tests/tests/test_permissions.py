#
# import pytest
#
# from acceptance.acceptance_tests.helpers.api_helper.certos import add_CE_via_API
# from acceptance.acceptance_tests.helpers.api_helper.org_creation import (create_custom_org_with_CE_only_via_API,
#                                                                          create_custom_org_with_CE_and_CAP_via_API,
#                                                                          create_custom_CAP_only_org_via_API)
# from acceptance.acceptance_tests.helpers.api_helper.user_mgmt import create_role_via_API, \
#     add_dashboard_user_and_assign_role_via_API, enable_login_for_user_via_API, create_password_for_user_via_API, \
#     edit_role_via_API, edit_permission_access_level_via_API
# from acceptance.acceptance_tests.helpers.common_functions import logout
# from acceptance.acceptance_tests.helpers.custom_decorators import skip_unless_cs_direct_vss_off
# from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
# from acceptance.acceptance_tests.helpers.test_logger import step
# from acceptance.acceptance_tests.pages.admin.auth_users_admin import AuthUsersAdminPage
# from acceptance.acceptance_tests.pages.admin.role_admin import RoleAdminPage
# from acceptance.acceptance_tests.pages.non_admin.dashboard_access import DashboardAccessPage
# from acceptance.acceptance_tests.pages.non_admin.manage_users import ManageUsersPage
# from acceptance.acceptance_tests.pages.non_admin.org_certs import OrgCertsPage
# from acceptance.acceptance_tests.pages.non_admin.org_dashboard import BulkOrgDashboardPage, IndividualOrgDashboardPage
#
# from acceptance.acceptance_tests.steps.common_steps import CommonSteps
# from acceptance.acceptance_tests.steps.roles_and_permissions_steps import RolesAndPermissionsSteps
# from acceptance.properties_manager import get_username
#
#
# def test_devices_role_nav_bar_items_is_as_expected_uba_on_CUSTEAM_T799(driver, base_url, password):
#     permission_name = 'devices'
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     username_ = get_username('uba_on_user', base_url)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username_, password, permission_name, install_type='bulk', org_type='bundle')
#
#     step('Then the org dashboard page is displayed')
#     org_dashboard_pg = BulkOrgDashboardPage(driver)
#     org_dashboard_pg.wait_until_page_is_displayed()
#
#     step('And Learn Lite link is clickable')
#     org_dashboard_pg.nav_element_is_clickable('Learn')
#     r_and_p_steps.devices_common_assertions()
#
#     step('And no other nav bar links are visible')
#     assert org_dashboard_pg.get_number_of_nav_elements() == 6
#
#
# def test_devices_role_nav_bar_items_is_as_expected_uba_off_CUSTEAM_T800(driver, base_url, password):
#     permission_name = 'devices'
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     username_ = get_username('uba_off_user', base_url)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username_, password, permission_name, install_type='bulk', org_type='bundle')
#
#     step('Then the org dashboard page is displayed')
#     org_dashboard_pg = BulkOrgDashboardPage(driver)
#     org_dashboard_pg.wait_until_page_is_displayed()
#
#     r_and_p_steps.devices_common_assertions()
#     step('And no other nav bar links are visible')
#     assert org_dashboard_pg.get_number_of_nav_elements() == 5
#
#
# def test_devices_role_nav_bar_items_is_as_expected_individual_deployment_CUSTEAM_T801(
#         driver, base_url, username, password):
#     permission_name = 'devices'
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username, password, permission_name, install_type='individual', org_type='bundle')
#
#     step('Then the org dashboard page is displayed')
#     org_dashboard_pg = BulkOrgDashboardPage(driver)
#     org_dashboard_pg.wait_until_page_is_displayed()
#
#     step('And Learn Lite link is clickable')
#     org_dashboard_pg.nav_element_is_clickable('Learn')
#     r_and_p_steps.devices_common_assertions()
#
#     step('And no other nav bar links are visible')
#     assert org_dashboard_pg.get_number_of_nav_elements() == 6
#
#
# def test_certs_and_insurance_role_nav_bar_items_is_as_expected_IASME_CUSTEAM_T802(driver, base_url, password):
#     permission_name = 'certificates_and_insurance'
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     username_ = get_username('vss_off_iasme_user', base_url)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username_, password, permission_name, install_type='individual', org_type='bundle')
#
#     step('Then the certificates page is displayed')
#     org_certs_pg = OrgCertsPage(driver)
#     org_certs_pg.wait_until_page_displayed()
#
#     r_and_p_steps.certs_common_assertions()
#
#     step('And no other nav bar links are visible')
#     assert org_certs_pg.get_number_of_nav_elements() == 2
#
#
# def test_certs_and_insurance_role_nav_bar_items_is_as_expected_non_IASME_CUSTEAM_T803(
#         driver, base_url, password):
#     permission_name = 'certificates_and_insurance'
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     username_ = get_username('vss_off_non_iasme_user', base_url)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username_, password, permission_name, install_type='individual', org_type='bundle')
#
#     step('Then the certificates page is displayed')
#     org_certs_pg = OrgCertsPage(driver)
#     org_certs_pg.wait_until_page_displayed()
#
#     step('And Cyber Insurance link is clickable')
#     org_certs_pg.nav_element_is_clickable('Cyber insurance')
#     r_and_p_steps.certs_common_assertions()
#
#     step('And no other nav bar links are visible')
#     assert org_certs_pg.get_number_of_nav_elements() == 3
#
#
# @skip_unless_cs_direct_vss_off()
# def test_certs_and_insurance_role_nav_bar_items_is_as_expected_cs_direct_CUSTEAM_T804(
#         driver, base_url, username, password):
#     common_steps = CommonSteps(driver)
#     user_details = common_steps.signup_and_make_payment(
#         base_url, 'direct-customer-v4-ce-only-monthly', 'example.com')
#
#     permission_name = 'certificates_and_insurance'
#     step(f'And a {permission_name} role is created')
#     common_steps.go_to_home_page(base_url)
#     org_dashboard_url = common_steps.get_current_url()
#     org_uuid = org_dashboard_url.split('organisation/')[1].split("/")[0]
#     role_name = f'role{get_transformed_date()}'
#     create_role_via_API(driver, base_url, org_uuid, role_name, permission_name)
#
#     step('And the role ID is obtained')
#     logout(driver, base_url)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#     role_admin_page = RoleAdminPage(driver)
#     role_id = role_admin_page.get_role_id(base_url, role_name)
#     auth_users_admin_pg = AuthUsersAdminPage(driver)
#     user_email_1 = user_details.get('user_email')
#     user_id = auth_users_admin_pg.get_user_id(base_url, user_email_1)
#     enable_login_for_user_via_API(driver, base_url, user_email_1, user_id)
#     create_password_for_user_via_API(driver, base_url, user_id, user_email_1, password)
#     logout(driver, base_url)
#     common_steps.enter_username_and_password_on_login_page_and_submit(base_url, user_email_1, password)
#
#     step(f'And a dashboard user is created and assigned the {permission_name} role')
#     user_email_2 = f'test{get_transformed_date()}@example.com'
#     add_dashboard_user_and_assign_role_via_API(driver, base_url, user_email_2, org_uuid, role_id)
#     logout(driver, base_url)
#
#     step('When the new user logs in')
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#     auth_users_admin_pg = AuthUsersAdminPage(driver)
#     user_id = auth_users_admin_pg.get_user_id(base_url, user_email_2)
#     enable_login_for_user_via_API(driver, base_url, user_email_2, user_id)
#     create_password_for_user_via_API(driver, base_url, user_id, user_email_2, password)
#     logout(driver, base_url)
#     common_steps.login(base_url, user_email_2, password)
#     common_steps.go_to_home_page(base_url)
#
#     step('Then the certificates page is displayed')
#     org_certs_pg = OrgCertsPage(driver)
#     org_certs_pg.wait_until_page_displayed()
#
#     step('And Ransomware & Recovery Toolbox link is clickable')
#     org_certs_pg.nav_element_is_clickable('Ransomware & Recovery Toolbox')
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     r_and_p_steps.certs_common_assertions()
#
#     step('And no other nav bar links are visible')
#     assert org_certs_pg.get_number_of_nav_elements() == 3
#
#
# def test_people_and_org_role_nav_bar_items_is_as_expected_bulk_UBA_on_CUSTEAM_T794(driver, base_url, password):
#     permission_name = 'people_and_organisation'
#     username_ = get_username('uba_on_user', base_url)
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username_, password, permission_name, install_type='bulk', org_type='bundle')
#     r_and_p_steps.manage_users_assertion()
#     manage_users = ManageUsersPage(driver)
#
#     step('And no other nav bar links are visible')
#     assert manage_users.get_number_of_nav_elements() == 3
#
#
# def test_people_and_org_role_nav_bar_items_is_as_expected_bulk_UBA_off_CUSTEAM_T795(driver, base_url, password):
#     permission_name = 'people_and_organisation'
#     username_ = get_username('uba_off_user', base_url)
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username_, password, permission_name, install_type='bulk', org_type='bundle')
#     r_and_p_steps.dashboard_access_assertion()
#     manage_users = ManageUsersPage(driver)
#
#     step('And no other nav bar links are visible')
#     assert manage_users.get_number_of_nav_elements() == 3
#
#
# def test_people_and_org_role_nav_bar_items_is_as_expected_individual_deployment_CUSTEAM_T796(
#         driver, base_url, username, password):
#     permission_name = 'people_and_organisation'
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username, password, permission_name, install_type='individual', org_type='bundle')
#     r_and_p_steps.manage_users_assertion()
#     manage_users = ManageUsersPage(driver)
#
#     step('And no other nav bar links are visible')
#     assert manage_users.get_number_of_nav_elements() == 3
#
#
# def test_people_and_org_role_nav_bar_items_is_as_expected_cert_only_org_CUSTEAM_T797(
#         driver, base_url, username, password):
#     permission_name = 'people_and_organisation'
#     r_and_p_steps = RolesAndPermissionsSteps(driver)
#     r_and_p_steps.permissions_test_setup(
#         base_url, username, password, permission_name, install_type=None, org_type='no plan')
#     r_and_p_steps.dashboard_access_assertion()
#     manage_users = ManageUsersPage(driver)
#
#     step('And no other nav bar links are visible')
#     assert manage_users.get_number_of_nav_elements() == 3
#
#
# def test_role_of_dashboard_user_can_be_changed_to_another_CUSTEAM_T806(driver, base_url, username, password):
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And a custom org with CE only is created')
#     org_details = create_custom_org_with_CE_only_via_API(driver, base_url)
#     org_uuid = org_details['org_uuid']
#
#     step('And two roles are created')
#     permission_name_1 = 'devices'
#     role_name_1 = f'role{get_transformed_date()}'
#     create_role_via_API(driver, base_url, org_uuid, role_name_1, permission_name_1)
#
#     permission_name_2 = 'people_and_organisation'
#     role_name_2 = f'role{get_transformed_date()}'
#     create_role_via_API(driver, base_url, org_uuid, role_name_2, permission_name_2)
#
#     step('And the role IDs are obtained')
#     role_admin_page = RoleAdminPage(driver)
#     role_id_1 = role_admin_page.get_role_id(base_url, role_name_1)
#     role_id_2 = role_admin_page.get_role_id(base_url, role_name_2)
#
#     step('And a dashboard user is created and assigned the first role')
#     user_email = f'test{get_transformed_date()}@example.com'
#     response_json = add_dashboard_user_and_assign_role_via_API(driver, base_url, user_email, org_uuid, role_id_1)
#     assert response_json.get('roles')[0] == role_name_1
#     assert response_json.get('email') == user_email
#
#     step('Then the dashboard user can be assigned the second role')
#     auth_users_admin_pg = AuthUsersAdminPage(driver)
#     user_id = auth_users_admin_pg.get_user_id(base_url, user_email)
#     response_json = edit_role_via_API(driver, base_url, org_uuid, role_id_2, user_email, user_id)
#     assert response_json.get('roles')[0] == role_name_2
#     assert response_json.get('email') == user_email
#
#
# def test_permission_access_levels_can_be_edited_CUSTEAM_T805(driver, base_url, username, password):
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And a custom org with CE and CAP (bulk) is created')
#     org_details = create_custom_org_with_CE_and_CAP_via_API(driver, base_url, install_type='bulk')
#     org_uuid = org_details['org_uuid']
#
#     step('And a role is created')
#     role_name_1 = f'role{get_transformed_date()}'
#     permission_name_1 = 'certificates_and_insurance'
#     create_role_via_API(driver, base_url, org_uuid, role_name_1, permission_name_1)
#
#     step('And the role ID is obtained')
#     role_admin_page = RoleAdminPage(driver)
#     role_id = role_admin_page.get_role_id(base_url, role_name_1)
#
#     step('Then the permission access level of the role can be edited')
#     dashboard_access_pg = DashboardAccessPage(driver)
#     dashboard_access_pg.go_to_page(base_url, org_uuid)
#     role_name_2 = f'role{get_transformed_date()}'
#     permission_name_2 = 'devices'
#     response = edit_permission_access_level_via_API(driver, base_url, org_uuid, role_id, role_name_2, permission_name_2)
#     response_json = response.json()
#     assert response_json.get('name') == role_name_2
#     assert response_json.get('permissions')[0] == permission_name_2
#
#     step('And the correct radio buttons are ticked')
#     dashboard_access_pg.go_to_page(base_url, org_uuid)
#     assert dashboard_access_pg.permission_is_full_access(role_id, permission_name_2)
#     assert dashboard_access_pg.permission_is_no_access(role_id, permission_name_1)
#     assert dashboard_access_pg.permission_is_no_access(role_id, 'people_and_organisation')
#
#
# def test_full_dashboard_admin_after_new_org_creation_CUSTEAM_T798(driver, base_url, username, password):
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('When a custom org with CE only is created')
#     org_details = create_custom_org_with_CE_only_via_API(driver, base_url)
#     org_uuid = org_details['org_uuid']
#
#     step('Then the Full Dashboard Admin Role is created for the organisation')
#     dashboard_access_pg = DashboardAccessPage(driver)
#     dashboard_access_pg.go_to_page(base_url, org_uuid)
#     dashboard_access_pg.full_dashboard_admin_role_is_displayed()
#
# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_full_dashboard_admin_role_assigned_upon_completion_of_onboarding_CUSTEAM_T807(
#         driver, base_url):
#     common_steps = CommonSteps(driver)
#     user_details = common_steps.signup_and_make_payment(
#         base_url, 'direct-customer-v4-ce-only-monthly', 'example.com')
#
#     step('When the user who completed the onboarding navigates to the Dashboard access page')
#     org_dashboard_pg = IndividualOrgDashboardPage(driver)
#     org_uuid = org_dashboard_pg.get_org_uuid()
#     dashboard_access_pg = DashboardAccessPage(driver)
#     dashboard_access_pg.go_to_page(base_url, org_uuid)
#
#     step('Then they see that they have been assigned the Full Dashboard Admin Role for their organisation')
#     user_email = user_details.get('user_email')
#     assert user_email in dashboard_access_pg.get_dashboard_user_email()
#     assert 'Full Dashboard Admin' in dashboard_access_pg.get_dashboard_user_role()
#
#
# def test_role_and_permissions_shown_after_CAP_only_org_purchases_CE_CUSTEAM_T827(driver, base_url, username, password):
#     step(f'Given a user is logged in as {username}')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And creates a custom CAP only org (individual)')
#     org_details = create_custom_CAP_only_org_via_API(driver, base_url, install_type='individual')
#
#     step('And navigates to the dashboard access page')
#     dashboard_access_pg = DashboardAccessPage(driver)
#     org_uuid = org_details.get('org_uuid')
#     dashboard_access_pg.go_to_page(base_url, org_uuid)
#     dashboard_access_pg.wait_until_page_is_displayed()
#
#     step('And the roles and permissions section does not exist')
#     assert not dashboard_access_pg.is_roles_and_permissions_section_displayed()
#
#     step('When a CE subscriptions is purchased')
#     add_CE_via_API(driver, base_url, org_uuid)
#
#     step('And the user navigates to the dashboard access page')
#     dashboard_access_pg.go_to_page(base_url, org_uuid)
#     dashboard_access_pg.wait_until_page_is_displayed()
#
#     step('Then the roles and permissions section is displayed')
#     assert dashboard_access_pg.is_roles_and_permissions_section_displayed()
