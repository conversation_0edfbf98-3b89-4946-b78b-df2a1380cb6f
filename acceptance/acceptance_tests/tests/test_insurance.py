from acceptance.acceptance_tests.helpers.api_helper.certos import submit_survey_via_API, \
    get_declaration_signing_page_via_API, \
    go_back_survey_via_API
from acceptance.acceptance_tests.helpers.api_helper.org_creation import (create_custom_CAP_only_org_via_API)
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.pages.non_admin.cyber_insurance import CyberInsurancePage
from acceptance.acceptance_tests.steps.certos_steps import CertosSteps
from acceptance.acceptance_tests.steps.common_steps import CommonSteps
from acceptance.properties_manager import get_username

INSURANCE_DECLARATION_STMT = 'I declare that the statements and particulars in the IASME / Cyber Essentials ' \
                             'assessments are true and that no material facts have been misstated. A material ' \
                             'fact is one that would influence the acceptance or assessment of the risk. I also ' \
                             'declare to have read the applicable wording and fully understood its scope, ' \
                             'exclusions and limitations. I also agree that I will abide by the Branding ' \
                             'Agreement guidelines.'
NON_INSURANCE_DECLARATION_STMT = 'I declare that the statements and particulars in the IASME/Cyber Essentials ' \
                                 'assessment are true and that no material facts have been misstated. A material ' \
                                 'fact is one which would influence the acceptance or assessment of the risk. I ' \
                                 'also agree that I will abide by the Branding Agreement guidelines.'
not_eligible = 'NOT ELIGIBLE'
inactive = 'INACTIVE'
active = 'ACTIVE'
_25k_protection = 'Protection up to £25K'
_100k_protection = 'Protection up to £100k'
_250k_protection = 'Protection up to £250k'
ce_cert_name = 'Cyber Essentials'


def verify_insurance_card_content(driver, _25k_status, _100k_status):
    step('And the 25k insurance card is displayed with the correct status and button')
    cyber_insurance_pg = CyberInsurancePage(driver)
    insurance_cover_amt_25k = '25K'
    assert _25k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_25k)
    assert not_eligible in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_25k)
    assert 'Only available with an active Cyber Essentials certificate' \
           in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_25k)
    assert cyber_insurance_pg.find_out_more_25k_btn_is_displayed(insurance_cover_amt_25k)

    step('And the 100k insurance card is displayed with the correct status and button')
    insurance_cover_amt_100k = '100K'
    assert _100k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_100k)
    assert not_eligible in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_100k)
    assert '*A bundle plan with CyberSmart Active Protect is required for this insurance' in cyber_insurance_pg \
        .get_insurance_card_text(insurance_cover_amt_100k)
    assert cyber_insurance_pg.find_out_more_100k_btn_is_displayed(insurance_cover_amt_100k)

    step('And the need more cover section contains an active button for getting a quote')
    assert 'https://www.sutcliffeinsurance.co.uk/' in cyber_insurance_pg.get_url_from_quote_button_works()


def insurance_text_verification(driver, base_url, username, password, user_type):
    certos_steps = CertosSteps(driver)
    org_details = certos_steps.create_org_and_complete_survey(base_url, password, user_type)
    org_uuid = org_details.get('org_uuid')

    step('Then the insurance declaration text is displayed on the declaration page')
    soup = get_declaration_signing_page_via_API(driver, base_url, org_uuid)
    actual_declaration_text = soup.select_one('.not-approved i').text
    assert INSURANCE_DECLARATION_STMT in actual_declaration_text

    step('When the user navigates back to the insurance tab on the survey')
    go_back_survey_via_API(driver, base_url, org_uuid)

    step('And submits the survey with insurance opted out')
    submit_survey_via_API(driver, base_url, username, org_uuid, insurance_opt_in=False)

    step('Then the non insurance declaration text is displayed on the declaration page')
    soup = get_declaration_signing_page_via_API(driver, base_url, org_uuid)
    actual_declaration_text = soup.select_one('.not-approved i').text
    assert NON_INSURANCE_DECLARATION_STMT in actual_declaration_text


def test_cyber_insurance_for_v4_unbundled_CAP_only_org_partner_override_not_enabled_CUSTEAM_T787(
        driver, base_url, password):
    user_type = 'non_iasme_partner_user'
    username = get_username(user_type, base_url)
    step(f'Given a non-iasme partner user {username} is logged in')
    common_steps = CommonSteps(driver)
    common_steps.login_and_provide_admin_access_using_API(base_url, username, password)

    step('And a custom CAP only org (bulk) is created')
    org_details = create_custom_CAP_only_org_via_API(driver, base_url, install_type='bulk')

    step('And navigates to the cyber insurance page')
    cyber_insurance_pg = CyberInsurancePage(driver)
    org_uuid = org_details.get('org_uuid')
    cyber_insurance_pg.go_to_page(base_url, org_uuid)

    step('Then the CyberSmart bundle info text is displayed')
    cyber_insurance_pg.cybersmart_bundle_info_text_is_displayed()

    step('And the CE card is displayed with the correct status and button')
    assert cyber_insurance_pg.get_cert_status(ce_cert_name) == 'NOT CERTIFIED'
    assert 'Contact account manager' in cyber_insurance_pg.get_non_clickable_btn_text(ce_cert_name)

    verify_insurance_card_content(driver, not_eligible, not_eligible)


# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_insurance_optin_after_CE_certification_CUSTEAM_T846(driver, base_url, password):
#     user_type = 'non_iasme_partner_user'
#     username = get_username(user_type, base_url)
#     step(f'Given a non-iasme partner user {username} is logged in')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And a custom org with CE only is created')
#     org_details = create_custom_org_with_CE_only_via_API(driver, base_url)
#
#     step('When the CE survey is started')
#     org_uuid = org_details.get('org_uuid')
#     start_survey_via_API(driver, base_url, org_uuid)
#
#     step('And and the CE certificate is issued')
#     org_cert_admin_page = OrgCertificationsAdminPage(driver)
#     org_name = org_details.get('org_name')
#     org_cert_id = org_cert_admin_page.get_org_cert_id(base_url, org_name)
#     issue_certificate_via_API(driver, base_url, org_cert_id)
#
#     step('Then the insurance optins table is populated with the issued cert for that org')
#     insurance_optin_pg = InsuranceOptinAdminPage(driver)
#
#     def assert_results():
#         insurance_optin_pg.go_to_page(base_url)
#         insurance_optin_pg.cert_is_displayed(org_name)
#
#     # This wait statement is used because the insurance cards are not updated instantly.
#     wait_until_cmd_executed(assert_results, max_tries=10, polling_seconds=1)
#
#     step('And the email address field is populated')
#     assert insurance_optin_pg.email_address_is_populated(org_name)
#
#     expected_insurance_coverage = 'CE 25k'
#     step(f'And the insurance coverage is {expected_insurance_coverage}')
#     assert insurance_optin_pg.get_insurance_coverage(org_name) == expected_insurance_coverage


# def test_upgrade_to_250k_insurance_for_cybersmart_complete_org_CUSTEAM_T920(driver, base_url, password):
#     user_type = 'non_iasme_partner_user'
#     username = get_username(user_type, base_url)
#     step(f'Given a non-iasme partner user {username} is logged in')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And a Cybersmart Complete org with individual deployment is created')
#     org_details = create_cs_complete_org_via_API(driver, base_url, install_type='individual')
#
#     step('And the CE survey is started')
#     org_uuid = org_details.get('org_uuid')
#     start_survey_via_API(driver, base_url, org_uuid)
#
#     step('And and the CE certificate is issued')
#     org_cert_admin_page = OrgCertificationsAdminPage(driver)
#     org_name = org_details.get('org_name')
#     org_cert_id = org_cert_admin_page.get_org_cert_id(base_url, org_name)
#     issue_certificate_via_API(driver, base_url, org_cert_id)
#
#     step('And navigates to the cyber insurance page')
#     cyber_insurance_pg = CyberInsurancePage(driver)
#     org_uuid = org_details.get('org_uuid')
#
#     step('And the 250k insurance card is showing as inactive')
#     insurance_cover_amt_250k = '250K'
#
#     def assert_results():
#         cyber_insurance_pg.go_to_page(base_url, org_uuid)
#         assert _250k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)
#
#     # This wait statement is used because the insurance cards are not updated instantly.
#     wait_until_cmd_executed(assert_results, max_tries=10, polling_seconds=1)
#
#     assert inactive in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)
#
#     step('When the insurance is be upgraded to 250k')
#     response = upgrade_insurance_via_API(driver, base_url, org_uuid, '250k')
#     response_json = response.json()
#     assert 'Insurance successfully upgraded to £250000' in response_json['success'], \
#         f'Insurance upgrade was unsuccessful. Response was {response_json}'
#
#     step('And the cyber insurance page is refreshed')
#     step('Then the 250k insurance card is showing as active')
#     insurance_cover_amt_250k = '250K'
#
#     def assert_results():
#         cyber_insurance_pg.refresh()
#         assert _250k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)
#
#     # This wait statement is used because the insurance cards are not updated instantly.
#     wait_until_cmd_executed(assert_results, max_tries=10, polling_seconds=1)
#
#     assert inactive not in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)
#     assert active in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)


# def test_upgrade_to_100k_insurance_for_bundle_org_CUSTEAM_T943(driver, base_url, password):
#     user_type = 'non_iasme_partner_user'
#     username = get_username(user_type, base_url)
#     step(f'Given a non-iasme partner user {username} is logged in')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And a core org with individual deployment is created')
#     org_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
#     create_core_org_via_API(driver, base_url, org_name, 'individual', None, 'APRE')
#
#     step('When the CE survey is started')
#     org_uuid = get_org_uuid(driver, base_url, org_name)
#     start_survey_via_API(driver, base_url, org_uuid)
#
#     step('And and the CE certificate is issued')
#     org_cert_admin_page = OrgCertificationsAdminPage(driver)
#     org_cert_id = org_cert_admin_page.get_org_cert_id(base_url, org_name)
#     issue_certificate_via_API(driver, base_url, org_cert_id)
#
#     step('And navigates to the cyber insurance page')
#     cyber_insurance_pg = CyberInsurancePage(driver)
#
#     step('And the 100k insurance card is showing as inactive')
#     insurance_cover_amt_100k = '100K'
#
#     def assert_results():
#         cyber_insurance_pg.go_to_page(base_url, org_uuid)
#         assert _100k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_100k)
#
#     # This wait statement is used because the insurance cards are not updated instantly.
#     wait_until_cmd_executed(assert_results, max_tries=10, polling_seconds=1)
#
#     assert inactive in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_100k)
#
#     step('When the insurance is be upgraded to 100k')
#     response = upgrade_insurance_via_API(driver, base_url, org_uuid, '100k')
#     response_json = response.json()
#     assert 'Insurance successfully upgraded to £100000' in response_json['success'], \
#         f'Insurance upgrade was unsuccessful. Response was {response_json}'
#
#     step('And the cyber insurance page is refreshed')
#     step('Then the 100k insurance card is showing as active')
#     insurance_cover_amt_100k = '100K'
#
#     def assert_results():
#         cyber_insurance_pg.refresh()
#         assert _100k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_100k)
#
#     # This wait statement is used because the insurance cards are not updated instantly.
#     wait_until_cmd_executed(assert_results, max_tries=10, polling_seconds=1)
#
#     assert inactive not in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_100k)
#     assert active in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_100k)

# @skip_unless_waffle_switch_on('enable_insurance_opt_in_external_declaration')
# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_insurance_upgrade_during_declaration_CUSTEAM_T1020(driver, base_url, username, password):
#     user_type = 'non_iasme_partner_user'
#     username = get_username(user_type, base_url)
#     step(f'Given a non-iasme partner user {username} is logged in')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And a Cybersmart Complete org with individual deployment is created')
#     org_details = create_cs_complete_org_via_API(driver, base_url, install_type='individual')
#
#     step('And the CE survey is started')
#     org_uuid = org_details.get('org_uuid')
#     start_survey_via_API(driver, base_url, org_uuid)
#
#     step('And submits the survey with insurance opted in')
#     submit_survey_via_API(driver, base_url, username, org_uuid, insurance_opt_in=True)
#
#     step('And sends the declaration for external signing')
#     email = f'test{get_transformed_date()}@{get_email_domain()}'
#     send_declaration_for_external_signing_via_API(driver, base_url, org_uuid, email)
#
#     step('And the review and approve email is received')
#     email_subject = 'Please review and approve your certification submission'
#     email_body = 'Finish Your Certification Now'
#     email = common_steps.check_email_using_email_body(email, email_subject, email_body)
#     review_and_approve_link = common_steps.get_link_from_email(email, 'Review')
#     common_steps.go_to_page_via_email_link(review_and_approve_link, base_url)
#
#     step('And the "Review and Approve" link leads to the declaration page')
#     step('And the upgrade question is displayed')
#     declaration_pg = DeclarationPage(driver)
#     declaration_pg.insurance_250k_msg_is_displayed()
#     declaration_pg.upgrade_question_is_displayed()
#
#     step('And the declaration is submitted with an insurance upgrade')
#     submit_declaration_with_insurance_upgrade_via_API(driver, base_url, org_uuid)
#
#     step('And the CE certificate is issued')
#     org_cert_admin_page = OrgCertificationsAdminPage(driver)
#     org_name = org_details.get('org_name')
#     org_cert_id = org_cert_admin_page.get_org_cert_id(base_url, org_name)
#     issue_certificate_via_API(driver, base_url, org_cert_id)
#
#     step('Then the 250k insurance card is showing as active')
#     cyber_insurance_pg = CyberInsurancePage(driver)
#     insurance_cover_amt_250k = '250K'
#
#     def assert_results():
#         cyber_insurance_pg.go_to_page(base_url, org_uuid)
#         assert _250k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)
#
#     # This wait statement is used because the insurance cards are not updated instantly.
#     wait_until_command_executed(
#         assert_results, max_tries=10, polling_seconds=1, specific_exception=(AssertionError, TimeoutException))
#
#     assert inactive not in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)
#     assert active in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_250k)


# @skip_unless_waffle_switch_on('enable_insurance_opt_in_external_declaration')
# @pytest.mark.flaky(reruns=1, reruns_delay=2)
# def test_emailflow_insurance_optin_during_declaration_CUSTEAM_T1019(driver, base_url, username, password):
#     user_type = 'non_iasme_partner_user'
#     username = get_username(user_type, base_url)
#     step(f'Given a non-iasme partner user {username} is logged in')
#     common_steps = CommonSteps(driver)
#     common_steps.login_and_provide_admin_access_using_API(base_url, username, password)
#
#     step('And a Cybersmart Complete org with individual deployment is created')
#     org_details = create_cs_complete_org_via_API(driver, base_url, install_type='individual')
#
#     step('And the CE survey is started')
#     org_uuid = org_details.get('org_uuid')
#     start_survey_via_API(driver, base_url, org_uuid)
#
#     step('And submits the survey with insurance opted out')
#     submit_survey_via_API(driver, base_url, username, org_uuid, insurance_opt_in=False)
#
#     step('And sends the declaration for external signing')
#     email = f'test{get_transformed_date()}@{get_email_domain()}'
#     send_declaration_for_external_signing_via_API(driver, base_url, org_uuid, email)
#
#     step('And the review and approve email is received')
#     email_subject = 'Please review and approve your certification submission'
#     email_body = 'Finish Your Certification Now'
#     email = common_steps.check_email_using_email_body(email, email_subject, email_body)
#     review_and_approve_link = common_steps.get_link_from_email(email, 'Review')
#     common_steps.go_to_page_via_email_link(review_and_approve_link, base_url)
#
#     step('And the "Review and Approve" link leads to the declaration page')
#     step('And the opt-in question is displayed')
#     declaration_pg = DeclarationPage(driver)
#     declaration_pg.opt_in_question_is_displayed()
#
#     step('And the declaration is submitted with an insurance opt-in')
#     submit_declaration_with_insurance_opt_in_via_API(driver, base_url, org_uuid, username)
#
#     step('And the CE certificate is issued')
#     org_cert_admin_page = OrgCertificationsAdminPage(driver)
#     org_name = org_details.get('org_name')
#     org_cert_id = org_cert_admin_page.get_org_cert_id(base_url, org_name)
#     issue_certificate_via_API(driver, base_url, org_cert_id)
#
#     step('Then the 25k insurance card is showing as active')
#     cyber_insurance_pg = CyberInsurancePage(driver)
#     insurance_cover_amt_25k = '25K'
#
#     def assert_results():
#         cyber_insurance_pg.go_to_page(base_url, org_uuid)
#         assert active in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_25k)
#
#     # This wait statement is used because the insurance cards are not updated instantly.
#     wait_until_command_executed(assert_results, max_tries=10, polling_seconds=1, specific_exception=AssertionError)
#
#     assert _25k_protection in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_25k)
#     assert inactive not in cyber_insurance_pg.get_insurance_card_text(insurance_cover_amt_25k)
