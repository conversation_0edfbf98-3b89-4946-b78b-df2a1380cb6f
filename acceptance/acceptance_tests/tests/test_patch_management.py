
from acceptance.acceptance_tests.helpers.api_helper.device_mgmt import add_fake_device_via_API, \
    post_event_log_via_API, \
    enable_patching_via_API
from acceptance.acceptance_tests.helpers.api_helper.org_creation import create_core_org_via_API, get_org_uuid
from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
from acceptance.acceptance_tests.helpers.test_logger import step
from acceptance.acceptance_tests.pages.admin.app_install_admin import AppInstallAdminPage
from acceptance.acceptance_tests.pages.admin.organisations_settings_admin import OrgSettingsAdminPage
from acceptance.acceptance_tests.pages.non_admin.device_detail import DeviceDetailPage
from acceptance.acceptance_tests.pages.non_admin.org_dashboard import BulkOrgDashboardPage
from acceptance.acceptance_tests.steps.common_steps import CommonSteps
from acceptance.properties_manager import get_auto_test_org_prefix


def create_device(driver, base_url, username, password):
    step(f'Given a user is logged in as {username}')
    common_steps = CommonSteps(driver)
    common_steps.login_and_provide_admin_access_using_API(base_url, username, password)

    step('And a bulk deployment core org is created')
    org_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
    create_core_org_via_API(driver, base_url, org_name, install_type='bulk', voucher_code=None, industry_abbrev='APRE')
    org_uuid = get_org_uuid(driver, base_url, org_name)

    step('And patching is enabled for the org')
    org_settings_admin_pg = OrgSettingsAdminPage(driver)
    org_settings_id = org_settings_admin_pg.get_org_settings_id(base_url, org_name)
    enable_patching_via_API(driver, base_url, org_settings_id)

    step('And a device is added')
    org_dashboard = BulkOrgDashboardPage(driver)
    org_dashboard.go_to_page(base_url, org_uuid)
    appuser_uuid = org_dashboard.get_appuser_uuid()
    org_dashboard_url = common_steps.get_current_url()
    hostname = f'Host{get_transformed_date()}'
    caption = f'OS{get_transformed_date()}'
    device_id = f'device{get_transformed_date()}'
    serial_number = f'serial{get_transformed_date()}'
    org_uuid = org_dashboard_url.split('organisation/')[1].split("/")[0]
    add_fake_device_via_API(driver, base_url, appuser_uuid, hostname, caption, device_id, serial_number, org_uuid)
    app_install_admin_pg = AppInstallAdminPage(driver)
    app_install_id = app_install_admin_pg.get_app_install_id(base_url, device_id)
    return {'device_id': device_id, 'serial_number': serial_number, 'appuser_uuid': appuser_uuid, 'org_uuid': org_uuid, 'hostname': hostname, 'app_install_id': app_install_id}


def get_patch_history_table(driver, base_url, org_uuid, hostname):
    org_dashboard = BulkOrgDashboardPage(driver)
    org_dashboard.go_to_page(base_url, org_uuid)
    org_dashboard.go_to_device_detail_page(hostname)
    device_details_pg = DeviceDetailPage(driver)

    device_details_pg.wait_until_page_is_loaded()
    device_details_pg.scroll_to_bottom_of_page()
    device_details_pg.click_patch_history()
    return device_details_pg.get_patch_history_table_cells()

# @skip_unless_waffle_flag_on('opswat_patch')
# def test_patch_management_happy_path_CUSTEAM_T1351(driver, base_url, username, password):
#     device_details = create_device(driver, base_url, username, password)
#     device_id = device_details.get('device_id')
#     serial_number = device_details.get('serial_number')
#     appuser_uuid = device_details.get('appuser_uuid')
#     org_uuid = device_details.get('org_uuid')
#     hostname = device_details.get('hostname')
#     app_install_id = device_details.get('app_install_id')
#
#     step('And the device has a vulnerable app installed')
#     opswat_software_via_API(driver, base_url, device_id, serial_number, appuser_uuid)
#
#     step('When a patch installation is scheduled')
#     org_dashboard = BulkOrgDashboardPage(driver)
#     org_dashboard.go_to_page(base_url, org_uuid)
#     org_dashboard.go_to_device_detail_page(hostname)
#     device_details_pg = DeviceDetailPage(driver)
#
#     def action():
#         device_details_pg.refresh()
#         device_details_pg.wait_until_page_is_loaded()
#         device_details_pg.scroll_to_bottom_of_page()
#         device_details_pg.vuln_package_is_displayed()
#
#     wait_until_cmd_executed(action, max_tries=20, polling_seconds=3)
#
#     common_steps = CommonSteps(driver)
#     device_details_pg_url = common_steps.get_current_url()
#     patch_summary_via_API(driver, base_url, device_details_pg_url, app_install_id)
#     patch_history_via_API(driver, base_url, device_details_pg_url, app_install_id)
#
#     step('And a GET request is made to api/v3/opswat-patch/scheduled-product-installers/')
#     response = get_scheduled_product_installers_via_API(driver, base_url, device_id, serial_number, appuser_uuid)
#
#     step('And the status code is as expected')
#     status_code = response.status_code
#     assert status_code == 200
#
#     step('Then the response structure is as expected')
#     expected_product_name = "Firefox"
#     expected_product_patch_installer_id = 29
#     response_json = response.json()
#     assert isinstance(response_json, list), "Top-level response should be a list"
#     assert response_json[0]['status'].lower() == 'pending'
#
#     top_level_keys = {
#         "id": int,
#         "opswat_product_patch_installer": dict,
#         "status": str,
#         "signature": int
#     }
#
#     opswat_keys = {
#         "id": int,
#         "opswat_id": str,
#         "product": dict,
#         "patch_installer": dict,
#         "os_allow": str,
#         "os_deny": str,
#     }
#
#     product_keys = {
#         "opswat_id": str,
#         "name": str,
#         "vendor": int,
#     }
#
#     patch_installer_keys = {
#         "id": int,
#         "product_name": str,
#         "vulnerabilities": list,
#         "release_note_link": str,
#         "eula_link": str,
#         "latest_version": str,
#         "language_default": str,
#         "fresh_installable": bool,
#         "release_date": str,
#         "requires_reboot": bool,
#         "requires_uninstall_first": bool,
#         "schema_version": int,
#         "download_links": list,
#     }
#
#     download_link_keys = {
#         "id": int,
#         "architecture": str,
#         "language": str,
#         "link": str,
#         "os_ids": list,
#         "os_architecture": dict,
#     }
#
#     os_arch_keys = {
#         "id": int,
#         "name": str,
#     }
#
#     found_product = False
#
#     for item in response_json:
#         assert isinstance(item, dict), "Each item in list must be a dict"
#
#         for key, expected_type in top_level_keys.items():
#             if key == "signature" and key not in item:
#                 continue
#             assert key in item, f"Missing '{key}' in list item"
#             if key in item and item[key] is not None:
#                 assert isinstance(item[key], expected_type), (
#                     f"'{key}' should be {expected_type.__name__}, got {type(item[key]).__name__}"
#                 )
#
#         op_patch = item["opswat_product_patch_installer"]
#         for key, expected_type in opswat_keys.items():
#             assert key in op_patch, f"Missing '{key}' in opswat_product_patch_installer"
#             assert isinstance(op_patch[key], expected_type), (
#                 f"'{key}' in opswat_product_patch_installer should be {expected_type.__name__}, "
#                 f"got {type(op_patch[key]).__name__}"
#             )
#
#         product = op_patch["product"]
#         for key, expected_type in product_keys.items():
#             assert key in product, f"Missing '{key}' in product"
#             assert isinstance(product[key], expected_type), (
#                 f"'{key}' in product should be {expected_type.__name__}, got {type(product[key]).__name__}"
#             )
#
#         patch_installer = op_patch["patch_installer"]
#         for key, expected_type in patch_installer_keys.items():
#             assert key in patch_installer, f"Missing '{key}' in patch_installer"
#             assert isinstance(patch_installer[key], expected_type), (
#                 f"'{key}' in patch_installer should be {expected_type.__name__}, "
#                 f"got {type(patch_installer[key]).__name__}"
#             )
#
#         for dl in patch_installer["download_links"]:
#             assert isinstance(dl, dict), "Each element of download_links must be a dict"
#             for key, expected_type in download_link_keys.items():
#                 assert key in dl, f"Missing '{key}' in download link"
#                 assert isinstance(dl[key], expected_type), (
#                     f"'{key}' in download link should be {expected_type.__name__}, "
#                     f"got {type(dl[key]).__name__}"
#                 )
#
#             os_arch = dl["os_architecture"]
#             for key, expected_type in os_arch_keys.items():
#                 assert key in os_arch, f"Missing '{key}' in os_architecture"
#                 assert isinstance(os_arch[key], expected_type), (
#                     f"'{key}' in os_architecture should be {expected_type.__name__}, "
#                     f"got {type(os_arch[key]).__name__}"
#                 )
#
#         if op_patch["id"] == expected_product_patch_installer_id and patch_installer["product_name"] == expected_product_name:
#             found_product = True
#
#     assert found_product, (
#         f"Did not find a record where opswat_product_patch_installer.id == {expected_product_patch_installer_id} "
#         f"and patch_installer.product_name == {expected_product_name}. Response was {response_json}"
#     )
#
#     step('And the event log can be posted by CAP')
#     opswat_scheduled_product_installer_id = response_json[0].get('id')
#     event_log_response = post_event_log_via_API(
#         driver, base_url, device_id, serial_number, appuser_uuid, opswat_scheduled_product_installer_id)
#     event_log_response_json = event_log_response.json()
#     assert isinstance(event_log_response_json, dict), "Top-level response must be a dict"
#     expected_keys = {
#         'id': int,
#         'opswat_scheduled_product_installer': int,
#         'status': str,
#         'error_code': str,
#         'details': str,
#         'created': str,
#         'modified': str,
#     }
#
#     for key, expected_type in expected_keys.items():
#         assert key in event_log_response_json, f"Missing key '{key}' in response"
#         assert isinstance(event_log_response_json[key], expected_type), (
#             f"Expected '{key}' to be {expected_type.__name__}, got {type(event_log_response_json[key]).__name__}"
#         )
#
#     assert event_log_response_json['opswat_scheduled_product_installer'] == opswat_scheduled_product_installer_id, (
#         f"opswat_scheduled_product_installer should be {opswat_scheduled_product_installer_id}"
#     )
#
#     assert event_log_response_json['status'].lower() == 'pending'

def test_invalid_input_details_for_event_log_CUSTEAM_T1352(driver, base_url, username, password):
    step(f'Given a user is logged in as {username}')
    common_steps = CommonSteps(driver)
    common_steps.login(base_url, username, password)

    step('And a bulk deployment core org is created')
    org_name = f'{get_auto_test_org_prefix()}{get_transformed_date()}'
    create_core_org_via_API(driver, base_url, org_name, install_type='bulk', voucher_code=None, industry_abbrev='APRE')
    org_uuid = get_org_uuid(driver, base_url, org_name)

    step('And a device is added')
    org_dashboard = BulkOrgDashboardPage(driver)
    org_dashboard.go_to_page(base_url, org_uuid)
    appuser_uuid = org_dashboard.get_appuser_uuid()
    org_dashboard_url = common_steps.get_current_url()
    hostname = f'Host{get_transformed_date()}'
    caption = f'OS{get_transformed_date()}'
    device_id = f'device{get_transformed_date()}'
    serial_number = f'serial{get_transformed_date()}'
    org_uuid = org_dashboard_url.split('organisation/')[1].split("/")[0]
    add_fake_device_via_API(driver, base_url, appuser_uuid, hostname, caption, device_id, serial_number, org_uuid)

    step('When a POST request is made to api/v3/opswat-patch/event-log/ with invalid input details')
    response = post_event_log_via_API(
        driver, base_url, device_id, serial_number, appuser_uuid, 'invalid_id')

    step('Then a 400 status code is returned')
    assert response.status_code == 400

# @skip_unless_waffle_flag_on('opswat_patch')
# def test_patch_retry_escalates_to_error_after_3_attempts_CUSTEAM_T1379(driver, base_url, username, password):
#     device_details = create_device(driver, base_url, username, password)
#     device_id = device_details.get('device_id')
#     serial_number = device_details.get('serial_number')
#     appuser_uuid = device_details.get('appuser_uuid')
#     org_uuid = device_details.get('org_uuid')
#     hostname = device_details.get('hostname')
#     app_install_id = device_details.get('app_install_id')
#
#     step('And the device has a vulnerable app installed')
#     opswat_software_via_API(driver, base_url, device_id, serial_number, appuser_uuid)
#
#     step('And a patch installation is scheduled')
#     org_dashboard = BulkOrgDashboardPage(driver)
#     org_dashboard.go_to_page(base_url, org_uuid)
#     org_dashboard.go_to_device_detail_page(hostname)
#     device_details_pg = DeviceDetailPage(driver)
#
#     def action():
#         device_details_pg.refresh()
#         device_details_pg.wait_until_page_is_loaded()
#         device_details_pg.scroll_to_bottom_of_page()
#         device_details_pg.vuln_package_is_displayed()
#
#     wait_until_cmd_executed(action, max_tries=20, polling_seconds=3)
#
#     common_steps = CommonSteps(driver)
#     device_details_pg_url = common_steps.get_current_url()
#     patch_summary_via_API(driver, base_url, device_details_pg_url, app_install_id)
#     patch_history_via_API(driver, base_url, device_details_pg_url, app_install_id)
#
#     step('And a GET request is made to api/v3/opswat-patch/scheduled-product-installers/')
#     response = get_scheduled_product_installers_via_API(driver, base_url, device_id, serial_number, appuser_uuid)
#
#     step('And the status code is as expected')
#     status_code = response.status_code
#     assert status_code == 200
#
#     retrying_user_dismissed_status = 'retrying_user_dismissed'
#     step(f'And the event log is posted 3 times with the {retrying_user_dismissed_status} status')
#     response_json = response.json()
#     opswat_scheduled_product_installer_id = response_json[0].get('id')
#     for i in range(3):
#         post_event_log_via_API(
#             driver, base_url, device_id, serial_number, appuser_uuid, opswat_scheduled_product_installer_id,
#             retrying_user_dismissed_status)
#
#     retrying_user_dismissed_msg = 'Retrying - User Dismissed'
#     step(f'And the patch status on the device detail page shows as "{retrying_user_dismissed_msg}"')
#     table = get_patch_history_table(driver, base_url, org_uuid, hostname)
#     assert retrying_user_dismissed_msg in table
#
#     step(f'When the event log is posted the 4th time with the {retrying_user_dismissed_status} status')
#     post_event_log_via_API(
#         driver, base_url, device_id, serial_number, appuser_uuid, opswat_scheduled_product_installer_id,
#         retrying_user_dismissed_status)
#
#     step('Then the final patch status auto transitions to "Error"')
#     table = get_patch_history_table(driver, base_url, org_uuid, hostname)
#     assert "Error" in table
