import os

import requests
from bs4 import BeautifulSoup

app_form_url_encoded_content_type = 'application/x-www-form-urlencoded'
html_parser = 'html.parser'
admin_headers = {
    'CF-Access-Client-Id': 'b71aaca1cce3c7687223dee5aae8331d.access',
    'CF-Access-Client-Secret': 'ee149afa43d3af657cf8df47cc63a8f267a7bd47f9539420ba7a5538177345a6'
}


def get_headers(driver):
    return {
        'Accept': 'application/json',
        'cookie': '; '.join([x['name'] + '=' + x['value'] for x in driver.get_cookies()])
    }


def create_data_via_API(driver, base_url, body, path):
    headers = get_headers(driver)
    url = os.path.join(base_url, path)
    headers.update({'referer': url, 'Content-Type': 'application/x-www-form-urlencoded'} | admin_headers)
    return requests.request("POST", url, headers=headers, data=body)


def add_response_cookies_to_driver(response, driver):
    cookies = response.cookies.get_dict()
    for key in cookies.keys():
        driver.add_cookie({'name': key, 'value': cookies[key]})


def get_csrfmiddlewaretoken(soup, driver=None, base_url=None):
    if driver:
        # First try to get token from current page
        try:
            current_soup = BeautifulSoup(driver.page_source, features=html_parser)
            token_element = current_soup.select_one('[name="csrfmiddlewaretoken"]')
            if token_element:
                return token_element['value']
        except Exception:
            pass

        # Only navigate if we couldn't find the token
        driver.get(base_url)
        soup = BeautifulSoup(driver.page_source, features=html_parser)
        return soup.select_one('[name="csrfmiddlewaretoken"]')['value']
    else:
        return soup.select_one('[name="csrfmiddlewaretoken"]')['value']
