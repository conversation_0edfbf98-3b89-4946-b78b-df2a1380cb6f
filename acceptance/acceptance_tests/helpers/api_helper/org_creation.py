import os

import requests
from selenium.webdriver.common.by import By

from acceptance.acceptance_tests.helpers.api_helper.utils import get_csrfmiddlewaretoken, get_headers
from acceptance.acceptance_tests.helpers.date_util import get_transformed_date
from acceptance.acceptance_tests.helpers.wait_statements import wait_until_presence_of_element
from acceptance.properties_manager import get_auto_test_org_prefix

employee_size = '2-4 EMPLOYEES'
auto_test_org_prefix = get_auto_test_org_prefix()


def get_common_body(driver, base_url, org_name):
    return {
        'csrfmiddlewaretoken': get_csrfmiddlewaretoken(None, driver, base_url),
        'name': org_name,
        'industry': 'APRE',
        'org_website_domain': f'{org_name}.com',
        'size': employee_size,
        'custom_bundle_price_vat_included': 'on',
    }


def update_body_based_on_install_type(body, install_type, org_name):
    if install_type.lower() == 'bulk':
        body.update({
            'bulk_install': 'True',
            'approved_domain': f'{org_name}.com'
        })
    elif install_type.lower() == 'individual':
        body.update({
            'bulk_install': 'False',
            'email_message': org_name
        })
    else:
        raise Exception(f'install type {install_type} not recognised')
    return body


def create_custom_org_with_CE_only_via_API(driver, base_url):
    org_name = f'{auto_test_org_prefix}{get_transformed_date()}'
    body = {
        'certifications': '1',
        'bundle': 'no_bundle',
        **get_common_body(driver, base_url, org_name)
    }
    create_org_util(driver, base_url, body)
    return {
        'org_name': org_name,
        'org_uuid': get_org_uuid(driver, base_url, org_name)
    }


def create_custom_org_with_CE_and_CAP_via_API(driver, base_url, install_type):
    org_name = f'{auto_test_org_prefix}{get_transformed_date()}'
    body = {
            'certifications': '1',
            'software-switch': 'on',
            'bundle': 'no_bundle',
            **get_common_body(driver, base_url, org_name)
    }
    updated_body = update_body_based_on_install_type(body, install_type, org_name)
    create_org_util(driver, base_url, updated_body)
    return {
        'org_name': org_name,
        'org_uuid': get_org_uuid(driver, base_url, org_name)
    }


def create_specific_custom_org_with_CE_and_CAP_bulk_via_API(driver, base_url, org_name):
    body = {
            'certifications': '1',
            'software-switch': 'on',
            'bulk_install': 'True',
            'approved_domain': f'{org_name}.com',
            'bundle': 'no_bundle',
            **get_common_body(driver, base_url, org_name)
            }
    create_org_util(driver, base_url, body)
    return {
        'org_name': org_name,
        'org_uuid': get_org_uuid(driver, base_url, org_name)
    }


def create_custom_CAP_only_org_via_API(driver, base_url, install_type):
    org_name = f'{auto_test_org_prefix}{get_transformed_date()}'
    body = {
            'certifications': 'no-certification',
            'software-switch': 'on',
            'bundle': 'no_bundle',
            **get_common_body(driver, base_url, org_name)
    }
    updated_body = update_body_based_on_install_type(body, install_type, org_name)
    create_org_util(driver, base_url, updated_body)
    return {
        'org_name': org_name,
        'org_uuid': get_org_uuid(driver, base_url, org_name)
    }


def create_core_org_via_API(driver, base_url, org_name, install_type, voucher_code, industry_abbrev):
    body = {
            'certifications': '1',
            'software-switch': 'on',
            'bundle': '1',
            **get_common_body(driver, base_url, org_name)
    }
    body.update({
        'industry': industry_abbrev
    })
    if voucher_code:
        body.update({'coupon': voucher_code})
    updated_body = update_body_based_on_install_type(body, install_type, org_name)
    return create_org_util(driver, base_url, updated_body)


def create_cs_complete_org_via_API(driver, base_url, install_type):
    org_name = f'{auto_test_org_prefix}{get_transformed_date()}'
    body = {
            'certifications': '4',
            'software-switch': 'on',
            'bundle': '5',
            **get_common_body(driver, base_url, org_name)
    }
    updated_body = update_body_based_on_install_type(body, install_type, org_name)
    create_org_util(driver, base_url, updated_body)
    return {
        'org_name': org_name,
        'org_uuid': get_org_uuid(driver, base_url, org_name)
    }


def create_custom_plan_CE_only_org_via_API(driver, base_url):
    org_name = f'{auto_test_org_prefix}{get_transformed_date()}'
    body = {
            'bundle': 'no_bundle',
            'custom-bundle-switch': 'on',
            'custom_bundle': '1',
            'custom_bundle_price': '50.00',
            **get_common_body(driver, base_url, org_name)
            }
    create_org_util(driver, base_url, body)
    return org_name


def create_custom_individual_deployment_org_with_CE_and_privacy_toolbox(driver, base_url, po_number):
    org_name = f'{auto_test_org_prefix}{get_transformed_date()}'
    body = {
            'bundle': 'no_bundle',
            'certifications-switch': 'on',
            'certifications': '4',
            'software-switch': 'on',
            'bulk_install': 'False',
            'email_message': org_name,
            'po_number': po_number,
            **get_common_body(driver, base_url, org_name)

    }
    create_org_util(driver, base_url, body)
    return {
        'org_name': org_name,
        'org_uuid': get_org_uuid(driver, base_url, org_name)
    }


def create_org_util(driver, base_url, body):
    url = os.path.join(base_url, 'partners/create-organisation/')
    headers = get_headers(driver)
    headers.update({'referer': url})
    return requests.request("POST", url, headers=headers, data=body)


def get_org_uuid(driver, base_url, org_name):
    driver.get(os.path.join(base_url, f'partners/organisations/?search={org_name}'))
    org_link_locator = By.CSS_SELECTOR, f'[data-original-title*="{org_name}"]'
    wait_until_presence_of_element(driver, org_link_locator)
    org_td = wait_until_presence_of_element(driver, org_link_locator)
    org_url = org_td.get_attribute('href')
    return org_url.split('organisation/')[1].split("/")[0]


def create_staging_eu_org(driver, base_url, org_name):
    url = os.path.join(base_url, 'partners/create-organisation/')
    body = {'csrfmiddlewaretoken': get_csrfmiddlewaretoken(None, driver, base_url),
               'name': org_name,
               'industry': 'APRE',
               'org_website_domain': f'{org_name}.com',
               'size': '2-4 EMPLOYEES',
               'certifications': '11',
               'bulk_install': 'False',
               'email_message': 'test'}
    headers = get_headers(driver)
    headers.update({'referer': url})
    requests.request("POST", url, headers=headers, data=body)
