#!/usr/bin/env bash

export CPLUS_INCLUDE_PATH=/usr/include/gdal
export C_INCLUDE_PATH=/usr/include/gdal

echo "Creating result directories"

RESULTS='/app/results'

if [ -d "/results" ]
then
    ln -s /results ${RESULTS}
    rm -rf ${RESULTS}/*
else
    mkdir -p ${RESULTS}
fi
mkdir -p ${RESULTS}/{logs,robot-report}
mkdir -p ${RESULTS}/test-results/{robot,pytest}
mkdir -p ${RESULTS}/acceptance/robot/output

echo "The Following tests will be performed"
TEST_FILE=$(cat circleci_test_files.txt| sed -r 's/^.{4}//')
echo "$TEST_FILE"
pytest --verbose --migrateci --reuse-db --junitxml=${RESULTS}/test-results/pytest/junit.xml -n $(($(nproc)*2)) --dist worksteal $TEST_FILE
test $? -eq 0 || exit $?

echo "All done"

echo "Killing Django in background"

kill -9 $DJANGO_PID

echo "Done"
exit 0