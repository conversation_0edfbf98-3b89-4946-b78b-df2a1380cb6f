version: 2.1
parameters:
  run_acceptance_test_data_cleanup_develop:
      type: boolean
      default: false

  run_acceptance_test_data_cleanup_staging:
    type: boolean
    default: false

  run_opt_out_of_weekly_emails_develop:
    type: boolean
    default: false

  run_opt_out_of_weekly_emails_staging:
    type: boolean
    default: false

  run_intensive_appcheckin_load_tests:
    type: boolean
    default: false

  run_intensive_patch_load_tests:
    type: boolean
    default: false

  load_test_number_of_users:
    type: integer
    default: 10
    description: "Number of concurrent users for load testing"

  load_test_spawn_rate:
    type: integer
    default: 1
    description: "Rate at which users are spawned per second"

  load_test_duration:
    type: integer
    default: 60
    description: "Duration of load test in seconds"

  load_test_cap_version:
    type: string
    default: "5.1.16.518"
    description: "CAP version to use for load testing"

commands:
  acceptance_test_steps:
    description: "Re-usable acceptance test steps"
    parameters:
      tests_to_run:
        type: string
        default: "not accessibility and not cleanup and not security"
      test_env:
        type: string
        default: ""
        description: "Override TEST_ENV value. If empty, uses CIRCLE_BRANCH"
    steps:
      - checkout
      - attach_workspace:
          at: ~/cyber-smart/
      - run:
          name: Load image
          command: |
            docker image load < "images/cyber-smart-acceptance"
      - run:
          name: prepare temporary directory for acceptance test results
          command: mkdir -p ~/cyber-smart/results; chmod a+rw ~/cyber-smart/results
      - run:
          name: run acceptance tests
          command: |
            set -a
            if [ -n "<< parameters.test_env >>" ]; then
              TEST_ENV="<< parameters.test_env >>"
            else
              TEST_ENV=${CIRCLE_BRANCH}
            fi
            TESTS_TO_RUN=$(echo "<< parameters.tests_to_run >>" | tr ' ' '_')
            TRUSTD_CLIENT_SECRET_DEV=${TRUSTD_CLIENT_SECRET_DEV}
            TRUSTD_CLIENT_SECRET_STG=${TRUSTD_CLIENT_SECRET_STG}
            PLAYSTORE_FIRST_NAME=${PLAYSTORE_FIRST_NAME}
            PLAYSTORE_EMAIL_ADDRESS=${PLAYSTORE_EMAIL_ADDRESS}
            PLAYSTORE_PASSWORD=${PLAYSTORE_PASSWORD}
            LAMBDA_TEST_USERNAME=${LAMBDA_TEST_USERNAME}
            LAMBDA_TEST_ACCESS_KEY=${LAMBDA_TEST_ACCESS_KEY}
            circleci tests glob "**/acceptance_tests/tests/test_*.py" | circleci tests run --command ">acceptance/files.txt xargs echo" --verbose
            docker compose -f docker-compose-acceptance-test.yml up --exit-code-from acceptance
      - run:
          name: set known failure message and test stats
          command: |
            echo 'export KNOWN_FAILURE_MESSAGE="$(cat results/known_failure_message.txt || echo " ")"' >> $BASH_ENV
            
            # Extract test statistics if available
            if [ -f results/test_stats.txt ]; then
              TOTAL_TESTS=$(grep "total_tests:" results/test_stats.txt | cut -d':' -f2 | tr -d ' ')
              FAILED_TESTS=$(grep "failed_tests:" results/test_stats.txt | cut -d':' -f2 | tr -d ' ')
              
              if [ ! -z "$TOTAL_TESTS" ] && [ ! -z "$FAILED_TESTS" ]; then
                echo "export TEST_STATS_MESSAGE=\"$FAILED_TESTS tests failed out of $TOTAL_TESTS\"" >> $BASH_ENV
              else
                echo "export TEST_STATS_MESSAGE=\"Test statistics not available\"" >> $BASH_ENV
              fi
            else
              echo "export TEST_STATS_MESSAGE=\"Test statistics not available\"" >> $BASH_ENV
            fi
            
            source $BASH_ENV
          when: always
      - persist_to_workspace:
          root: .
          paths:
            - acceptance/files.txt
      - store_test_results:
          path: ~/cyber-smart/results
      - store_artifacts:
          path: ~/cyber-smart/results
  acceptance_test_steps_load:
    description: "Re-usable acceptance test steps"
    parameters:
      tests_to_run:
        type: string
        default: "not accessibility and not cleanup and not security"
    steps:
      - checkout
      - attach_workspace:
          at: ~/cyber-smart/
      - run:
          name: prepare temporary directory for acceptance test results
          command: mkdir -p ~/cyber-smart/results; chmod a+rw ~/cyber-smart/results
      - run:
          name: run acceptance tests
          command: |
            set -a
            TEST_ENV=${CIRCLE_BRANCH} 
            TESTS_TO_RUN=$(echo "<< parameters.tests_to_run >>" | tr ' ' '_')
            LOAD_TEST_NUMBER_OF_USERS=<< pipeline.parameters.load_test_number_of_users >>
            LOAD_TEST_SPAWN_RATE=<< pipeline.parameters.load_test_spawn_rate >>
            LOAD_TEST_DURATION=<< pipeline.parameters.load_test_duration >>
            LOAD_TEST_CAP_VERSION=<< pipeline.parameters.load_test_cap_version >>
            docker compose -f docker-compose-acceptance-test.yml up --exit-code-from acceptance
      - run:
          name: set known failure message
          command: |
            echo 'export KNOWN_FAILURE_MESSAGE="$(cat results/known_failure_message.txt || echo " ")"' >> $BASH_ENV
            source $BASH_ENV
          when: always
      - persist_to_workspace:
          root: .
          paths:
            - acceptance/files.txt
      - store_test_results:
          path: ~/cyber-smart/results
      - store_artifacts:
          path: ~/cyber-smart/results
  create_pull_request:
    description: "Create pull request from CI"
    parameters:
      target-branch:
        type: string
        default: "The changes from source_branch will be merged into this target_branch."
      source-branch:
        type: string
        default: "The change from this branch will be merge into target_branch."
      gh_token:
        type: string
        default: "Github token"
    steps:
      - run:
          name: Create Pull Request
          command: |
            curl -X POST \
              -H "Accept: application/vnd.github+json" \
              -H "Authorization: Bearer << parameters.gh_token >>" \
              -H "X-GitHub-Api-Version: 2022-11-28" \
              -d '{
                "title": "PR for << parameters.source-branch >> -> << parameters.target-branch >> ",
                "body": "Created a  pull request to merge << parameters.source-branch >> into << parameters.target-branch >>.",
                "head": "<< parameters.source-branch >>",
                "base": "<< parameters.target-branch >>"
              }' \
              https://api.github.com/repos/cyber-smart/cybersmart-platform-core/pulls


acceptance_test_machine_setup: &acceptance_test_machine_setup
  working_directory: ~/cyber-smart/
  machine:
    image: ubuntu-2404:current
    docker_layer_caching: false
  resource_class: arm.large
  environment:
    DOCKER_BUILDKIT: 1

build_acceptance_test_container: &build_acceptance_test_container
    <<: *acceptance_test_machine_setup
    steps:
      - checkout
      - attach_workspace:
          at: ~/cyber-smart/
      - run:
          name: prepare temporary directory for acceptance test results
          command: mkdir -p ~/cyber-smart/results; chmod a+rw ~/cyber-smart/results
      - run:
         command: |
           curl -L https://depot.dev/install-cli.sh | \
             sudo env DEPOT_INSTALL_DIR=/usr/local/bin sh
      - run:
          name: Create files
          command: |
            echo "circleci" > acceptance/circleci_acceptance_tests_server.txt
            echo ${ZEPHYR_TOKEN} > acceptance/circleci_acceptance_tests_zephyr_token.txt
      - run:
          name: build containers
          command: depot bake --project 0zcg38j2r1 -f docker-compose-acceptance-test.yml --load
      - run:
          name: save docker image as tar
          command: |
            mkdir -p images
            docker image save -o "images/cyber-smart-acceptance" "cyber-smart-acceptance"
      - persist_to_workspace:
          root: .
          paths:
            - acceptance/circleci_acceptance_tests_server.txt
            - acceptance/circleci_acceptance_tests_zephyr_token.txt
            - images
acceptance_test_failed_slack_notification: &acceptance_test_failed_slack_notification
        event: fail
        template: basic_fail_1
        custom: |
          {
            "text": "CircleCI job failed.",
            "blocks": [
              {
                "type": "header",
                "text": {
                  "type": "plain_text",
                  "text": "Job Failed. :red_circle:",
                  "emoji": true
                }
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Job*: ${CIRCLE_JOB}"
                  }
                ]
              },
              {
                "type": "section",
                "fields": [
                  {
                    "type": "mrkdwn",
                    "text": "*Project*: $CIRCLE_PROJECT_REPONAME"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Branch*: $CIRCLE_BRANCH"
                  },
                  {
                    "type": "mrkdwn",
                    "text": "*Author*: $CIRCLE_USERNAME"
                  }
                ],
                "accessory": {
                  "type": "image",
                  "image_url": "https://assets.brandfolder.com/otz5mn-bw4j2w-6jzqo8/original/circle-logo-badge-black.png",
                  "alt_text": "CircleCI logo"
                }
              },
              {
                "type": "section",
                "text": {
                  "type": "mrkdwn",
                  "text": "$TEST_STATS_MESSAGE\n$KNOWN_FAILURE_MESSAGE"
                }
              },
              {
                "type": "actions",
                "elements": [
                  {
                    "type": "button",
                    "action_id": "basic_fail_view",
                    "text": {
                      "type": "plain_text",
                      "text": "View Job"
                    },
                    "url": "${CIRCLE_BUILD_URL}"
                  }
                ]
              },
              {
                "type": "actions",
                "elements": [
                  {
                    "type": "button",
                    "action_id": "basic_fail_view",
                    "text": {
                      "type": "plain_text",
                      "text": "View Test Report"
                    },
                    "url": "https://output.circle-artifacts.com/output/job/${CIRCLE_WORKFLOW_JOB_ID}/artifacts/0/~/cyber-smart/results/acceptance_test_report.html?sort=result"
                  }
                ]
              }          
            ]
          }
        channel: circle-ci-acceptance-tests

acceptance_test_passed_slack_notification: &acceptance_test_passed_slack_notification
        event: pass
        template: basic_success_1
        channel: circle-ci-acceptance-tests

orbs:
  jira: circleci/jira@2.2.0
  aws-ecr: circleci/aws-ecr@9.3.2
  aws-ecs: circleci/aws-ecs@6.0.0
  slack: circleci/slack@5.0.0
  aws-cli: circleci/aws-cli@5.1.0
  new-relic: home24/new-relic@1.0.2
executors:
  arm-medium:
    machine:
      image: ubuntu-2204:current
      docker_layer_caching: false
    resource_class: arm.medium
    environment:
      DOCKER_BUILDKIT: 1
jobs:
  cancel_older_build_and_push_image_develop:
    docker:
      - image: cimg/base:stable
    resource_class: small
    environment:
      THIS_PIPELINE_NUMBER: << pipeline.number >>
    steps:
      - run:
          name: Cancel older build_and_push_image workflows on develop
          command: |
            sudo apt-get update && sudo apt-get install -y jq
  
            # If not on develop branch, skip:
            if [ "$CIRCLE_BRANCH" != "develop" ]; then
              echo "Branch is $CIRCLE_BRANCH, not develop. Skipping older workflow cancellation."
              exit 0
            fi
  
            if [ -z "$THIS_PIPELINE_NUMBER" ]; then
              echo "No pipeline number found; skipping older workflow cancellation."
              exit 0
            fi
  
            echo "=== Cancel older build_and_push_image workflows for pipeline #$THIS_PIPELINE_NUMBER on 'develop' ==="
  
            # 1) Fetch all pipelines on develop
            PIPELINES_JSON=$(
              curl -s -H "Circle-Token: $ACCEPTANCE_TOKEN" \
                "https://circleci.com/api/v2/project/github/$CIRCLE_PROJECT_USERNAME/$CIRCLE_PROJECT_REPONAME/pipeline?branch=develop"
            )
  
            # 2) Filter to older pipelines (pipeline.number < THIS_PIPELINE_NUMBER).
            #    We do NOT filter pipeline.state because pipelines do not have 'running'.
            echo "$PIPELINES_JSON" \
            | jq -c --arg curr "$THIS_PIPELINE_NUMBER" '
                .items
                | map(
                    select((.number | tonumber) < ($curr | tonumber))
                  )
                | .[]
              ' \
            | while read -r pipeline_item; do
                older_pipeline_id=$(echo "$pipeline_item" | jq -r '.id')
                older_pipeline_num=$(echo "$pipeline_item" | jq -r '.number')
                echo "Found older pipeline #$older_pipeline_num (ID: $older_pipeline_id). Checking its workflows..."
  
                # 3) List workflows for that pipeline
                WF_JSON=$(
                  curl -s -H "Circle-Token: $ACCEPTANCE_TOKEN" \
                    "https://circleci.com/api/v2/pipeline/$older_pipeline_id/workflow"
                )
  
                # 4) Identify the build_and_push_image workflow if it's still in progress
                echo "$WF_JSON" \
                | jq -c '
                    .items
                    | map(select(
                        .name == "build_and_push_image"
                        and (
                          .status == "running"
                          or .status == "on_hold"
                          or .status == "failing"
                        )
                      ))
                    | .[]
                  ' \
                | while read -r wf_item; do
                    wf_id=$(echo "$wf_item" | jq -r '.id')
                    wf_status=$(echo "$wf_item" | jq -r '.status')
  
                    echo "  Canceling older build_and_push_image workflow $wf_id (status=$wf_status)..."
                    # Use the v2 API to cancel the entire workflow
                    curl -s -X POST \
                      -H "Circle-Token: $ACCEPTANCE_TOKEN" \
                      "https://circleci.com/api/v2/workflow/$wf_id/cancel" \
                      > /dev/null
                done
            done
  
            echo "=== Done canceling older build_and_push_image workflows. ==="
  build:
    working_directory: ~/cyber-smart/
    machine:
      image: ubuntu-2204:current
      docker_layer_caching: false # This speeds up build time by caching docker containers' layers. BEWARE it's paid feature!
    parallelism: 1 # Tests are split accross cores as efficiently using xdist
    environment:
      DOCKER_BUILDKIT: 1
    resource_class: arm.large
    steps:
      - checkout
      - attach_workspace:
          at: ~/cyber-smart/
      - run:
          name: prepare temporary directory for rspec results
          command: mkdir -p ~/cyber-smart/results; chmod a+rw ~/cyber-smart/results
      - run:
          name: prepare temporary directory for db dump
          command: mkdir -p ~/cyber-smart/db-dump; chmod a+rw ~/cyber-smart/db-dump
      - run:
          name: Get Migrations Hash
          #First Step:- Calculating the md5 hash of all the migration files separately and storing inside per_migration_hash.txt.
          #Second Step:- Sorting per_migration_hash.txt file so that the order remains consistent because the above step isn’t an ordered step.
          #Third Step:- Calculating the md5 hash sum by combining the hash value of all the files which we got from the first step
          #Final Step:- Printing the final hash
          command: |
            find src/cyber_smart/apps/ -path "*migrations*" -name "*.py" -exec md5sum {} \; > per_migration_hash.txt
            sort per_migration_hash.txt > sorted_per_migration_hash.txt
            md5sum sorted_per_migration_hash.txt | cut -d' ' -f1 > combined_migrations_hash.txt
            echo "Hash generated for migration files is $(<combined_migrations_hash.txt)"
      - restore_cache:
          keys:
            - django-db-snapshot-{{ checksum "combined_migrations_hash.txt" }}
      - run:
          command: |
            curl -L https://depot.dev/install-cli.sh | \
              sudo env DEPOT_INSTALL_DIR=/usr/local/bin sh
      - run:
          name: install dependencies
          command: |
            sudo apt-get update
            sudo apt-get install -y graphviz
            pip install --upgrade pip
            pip install --upgrade pydeps pydot
            pydeps --version
      - run:
          name: generate dependency graph
          command: |
            export PYTHONPATH=src/cyber_smart/apps
            timeout 10s pydeps --show-dot --no-show src/cyber_smart/apps > apps_deps.dot || true
            if [ ! -f apps_deps.dot ]; then
              echo "Error: apps_deps.dot not found."
              exit 1
            fi
            LINES=$(wc -l < apps_deps.dot)
            if [ "$LINES" -lt 5000 ]; then
              echo "Error: apps_deps.dot only has ${LINES} lines; expected at least 5000."
              exit 1
            fi
            cat apps_deps.dot
      - run:
          name: build containers and filter tests
          command: |
            set -a
            DEFAULT_BRANCHES=("develop" "staging" "master")
            if [[ " ${DEFAULT_BRANCHES[@]} " =~ " ${CIRCLE_BRANCH} " ]]; then
              echo "CIRCLE_BRANCH is ${CIRCLE_BRANCH}. Running all unit tests."
              circleci tests glob "src/**/test_*.py" | circleci tests split > src/circleci_test_files.txt
              depot bake --project 0zcg38j2r1 -f docker-compose-test.yml --load
            else
              for branch in "${DEFAULT_BRANCHES[@]}"; do
                if git show-ref --verify --quiet "refs/remotes/origin/$branch"; then
                  BASE_BRANCH=$branch
                  break
                fi
              done
              echo "Base branch is: $BASE_BRANCH"
              git fetch origin $BASE_BRANCH
              BRANCH_POINT=$(git merge-base origin/$BASE_BRANCH HEAD)             
              CHANGED_FILES=$(git diff --name-only $BRANCH_POINT HEAD)
              echo "Changed files are:"
              echo "$CHANGED_FILES"
              echo "$CHANGED_FILES" > changed_files.txt
              chmod +x src/dependency_analysis.py
              if grep -q "^src/cyber_smart/apps/" changed_files.txt; then
                if ! grep -qv "^src/cyber_smart/apps/" changed_files.txt; then
                  echo "Changes only in apps directory. Running tests for specific apps."
                  CHANGED_APPS=$(grep "^src/cyber_smart/apps/" changed_files.txt | cut -d'/' -f4 | sort -u | paste -sd, -)
                  python3 src/dependency_analysis.py "$CHANGED_APPS" > src/circleci_test_files.txt
                  echo "Tests to be run:"
                  cat src/circleci_test_files.txt
                elif grep -q -f exclude_from_unit_tests.txt changed_files.txt && grep -q "^src/cyber_smart/apps/" changed_files.txt; then
                  echo "Changes in both apps and excluded directories. Running tests for specific apps."
                  CHANGED_APPS=$(grep "^src/cyber_smart/apps/" changed_files.txt | cut -d'/' -f4 | sort -u | paste -sd, -)
                  python3 src/dependency_analysis.py "$CHANGED_APPS" > src/circleci_test_files.txt
                  echo "Tests to be run:"
                  cat src/circleci_test_files.txt
                else
                  echo "Changes in both apps and non-excluded directories. Running all tests."
                  circleci tests glob "src/**/test_*.py" | circleci tests split > src/circleci_test_files.txt
                fi
              elif grep -q -f exclude_from_unit_tests.txt changed_files.txt && ! grep -q -v -f exclude_from_unit_tests.txt changed_files.txt; then
                echo "Changes only in excluded directories. Skipping unit tests."
                exit 0
              else
                echo "Changes outside apps directory. Running all tests."
                circleci tests glob "src/**/test_*.py" | circleci tests split > src/circleci_test_files.txt
              fi
              depot bake --project 0zcg38j2r1 -f docker-compose-test.yml --load
            fi
      - run:
          name: run tests
          command: |
            if [ -s src/circleci_test_files.txt ]; then
              docker compose -f docker-compose-test.yml up --exit-code-from tests
            else
              echo "No tests to run."
            fi
      - store_test_results:
          path: ~/cyber-smart/results
      - store_artifacts:
          path: ~/cyber-smart/results
      - save_cache:
          key: django-db-snapshot-{{ checksum "combined_migrations_hash.txt" }}
          paths:
            - ~/cyber-smart/db-dump
  
  build_acceptance_test_container_develop:
    <<: *build_acceptance_test_container

  build_acceptance_test_container_staging:
    <<: *build_acceptance_test_container

  acceptance_tests_develop:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          test_env: "develop"
          tests_to_run: "not accessibility and not cleanup and not security and not emailflow and not mobileflow and not nis2"
#      - slack/notify:
#          <<: *acceptance_test_failed_slack_notification
#      - slack/notify:
#          <<: *acceptance_test_passed_slack_notification

  acceptance_email_tests_develop:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "emailflow"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  acceptance_tests_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          test_env: "staging"
          tests_to_run: "not accessibility and not cleanup and not security and not emailflow and not mobileflow and not nis2"
#      - slack/notify:
#          <<: *acceptance_test_failed_slack_notification
#      - slack/notify:
#          <<: *acceptance_test_passed_slack_notification

  acceptance_tests_staging_eu:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "nis2"
          test_env: "staging_eu"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  acceptance_email_tests_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "emailflow"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  trustd_individual_enrolment_test_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "mobileflowIndividual"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  trustd_bulk_enrolment_test_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "mobileflowBulk"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  load_tests_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps_load:
          tests_to_run: "load_general"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  security_acceptance_tests_develop:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "security"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  security_acceptance_tests_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "security"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  accessibility_acceptance_tests_develop:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "T754 or T755 or T756 or T761 or T762"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  accessibility_acceptance_tests_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "T754 or T755 or T756 or T761 or T762"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  acceptance_tests_env_clean_up_develop:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "environment"

  acceptance_tests_env_clean_up_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "environment"

  weekly_email_opt_out_scheduled_build_develop:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "weekly"

  weekly_email_opt_out_scheduled_build_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps:
          tests_to_run: "weekly"

  load_tests_appcheckin_intensive_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps_load:
          tests_to_run: "load_appcheckin"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  load_tests_patch_intensive_staging:
    <<: *acceptance_test_machine_setup
    steps:
      - acceptance_test_steps_load:
          tests_to_run: "load_patch"
      - slack/notify:
          <<: *acceptance_test_failed_slack_notification
      - slack/notify:
          <<: *acceptance_test_passed_slack_notification

  notify:
    docker:
      - image: 'cimg/base:stable'
    steps:
      - slack/notify:
          custom: |
            {
              "blocks": [
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "*Cybersmart Deployment Pipeline*\n:rocket::rocket::rocket::rocket: The ${CIRCLE_BRANCH} deployment has started. :rocket::rocket::rocket::rocket:\n They deployment takes around 20- 30 mins. You will be notified when the deployment is finished.\n Keep an eye on the deployment channel. :eyes::eyes:"
                  },
                  "accessory": {
                    "type": "image",
                    "image_url": "https://www.irion-edm.com/wp-content/uploads/2021/03/Solution-Design-Deployment-3.png",
                    "alt_text": "alt text for image"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "View Job",
                            "emoji": true
                        },
                        "value": "click_me_123",
                        "url": "https://app.circleci.com/pipelines/github/cyber-smart/cybersmart-platform-core?branch=${CIRCLE_BRANCH}"
                    }
                  ]
                }
              ]
            }
          event: always
  
  osv-scanner:
    working_directory: ~/cyber-smart/
    docker:
      - image: cimg/go:1.21.1
    resource_class: small
    steps:
      - checkout
      - attach_workspace:
          at: ~/cyber-smart/
      - run: go version
      - run: go install github.com/google/osv-scanner/cmd/osv-scanner@v1
      - run: osv-scanner --config osv-scanner.toml --format table --lockfile 'poetry.lock'
      - slack/notify:
          event: fail
          template: basic_fail_1
  check-code-quality:
    working_directory: ~/cyber-smart/
    docker:
      - image: cimg/python:3.12.0
    resource_class: small
    steps:
      - checkout
      - attach_workspace:
          at: ~/cyber-smart/
      - run: python --version
      - run: pip install ruff
      - run: ruff check .
      - slack/notify:
          event: fail
          template: basic_fail_1

  check-tailwind-build:
    working_directory: ~/cyber-smart/
    docker:
      - image: cimg/node:20.11.0
    resource_class: small
    steps:
      - checkout
      - attach_workspace:
          at: ~/cyber-smart/
      - run: node --version
      - run: npm --version
      - run: npm install
      - run:
          name: Check if Tailwind CSS build is up-to-date
          command: |
            # Create backup of existing tailwind output
            cp static/css/tailwind-output.css static/css/tailwind-output.css.backup

            # Run tailwind build to generate fresh output
            npm run tailwind-build

            # Compare the files
            if ! diff static/css/tailwind-output.css.backup static/css/tailwind-output.css > /dev/null; then
              echo "ERROR: Tailwind CSS output file is not up-to-date!"
              echo "Please run 'npm run tailwind-build' locally and commit the changes."
              echo ""
              echo "Differences found:"
              diff static/css/tailwind-output.css.backup static/css/tailwind-output.css || true
              exit 1
            else
              echo "Tailwind CSS output file is up-to-date."
            fi
      - slack/notify:
          event: fail
          template: basic_fail_1

  check-missing-translations:
    working_directory: ~/cyber-smart/
    docker:
      - image: cimg/python:3.11.13
    resource_class: small
    steps:
      - checkout
      - run:
          name: Install dependencies
          command: |
            pip install --upgrade pip
            pip install poetry
            poetry config virtualenvs.create false
            poetry install --no-interaction --no-ansi --with dev
            sudo apt-get update
            sudo apt-get install -y gettext
      - run:
          name: Set Django settings
          command: |
            ls -alh
            export DJANGO_SETTINGS_MODULE=cyber_smart.settings
            export PYTHONPATH=/home/<USER>/cyber-smart/src:$PYTHONPATH
            echo "export DJANGO_SETTINGS_MODULE=cyber_smart.settings" >> $BASH_ENV
      - run:
          name: Check for missing translations (fail fast)
          command: |
            echo "Checking for missing translations..."            
            cd src/
            echo "Running makemessages to update translation files..."
            python manage.py makemessages --locale=sv --no-obsolete
            echo "makemessages completed"
            
            echo "Finding all SV .po files ..."
            PO_FILES=$(find ../ -type f -path '*/sv/LC_MESSAGES/*.po' 2>/dev/null || true)
            
            for po_file in $PO_FILES; do
              echo "Checking file: $po_file"
              if ! awk '
                $0 == "msgstr \"\"" {
                  # read the *next* line into $0
                  if (getline && $0 !~ /^"/) {
                    exit 1
                  }
                }
              ' "$po_file"; then
                echo "Missing translations found in $po_file"
                exit 1
              fi
            done
      - slack/notify:
          event: fail
          template: basic_fail_1
  ecs-update-service:
    working_directory: ~/cyber-smart
    docker:
      - image: cimg/base:2024.10
    parameters:
      cluster:
        type: string
      container_image_name_updates:
        type: string
      family:
        type: string
      role_arn:
        type: string
      region:
        type: string
      verify_revision_is_deployed:
        type: boolean
      max_poll_attempts:
        type: integer
        default: 40
      poll_interval:
        type: integer
        default: 20
      container_env_var_updates:
        type: string
        default: ""
      service_name:
        type: string
        default: ""
    steps:
      - aws-cli/setup:
          role_arn: << parameters.role_arn >>
          region: << parameters.region >>
      - aws-ecs/update_service:
          cluster: << parameters.cluster >>
          container_image_name_updates: << parameters.container_image_name_updates >>
          family: << parameters.family >>
          region: << parameters.region >>
          verify_revision_is_deployed: << parameters.verify_revision_is_deployed >>
          container_env_var_updates: << parameters.container_env_var_updates >>
          max_poll_attempts: << parameters.max_poll_attempts >>
          poll_interval: << parameters.poll_interval >>
          service_name: << parameters.service_name >>
      - slack/notify:
          event: fail
          template: basic_fail_1
  run-migration-task:
    working_directory: ~/cyber-smart
    docker:
      - image: cimg/base:2024.10
    resource_class: small
    parameters:
      role_arn:
        type: string
      region:
        type: string
      cluster:
        type: string
      task_def:
        type: string
      service_name:
        type: string
    steps:
      - aws-cli/setup:
          role_arn: << parameters.role_arn >>
          region: << parameters.region >>
          profile_name: circleci_profile
      - run:
          name: Retrieve Subnet and Security Group IDs
          command: |
            # Get Subnet IDs and Security Group ID for the RDS instance
            SUBNET_IDS=$(aws ecs describe-services \
              --cluster << parameters.cluster >> \
              --services << parameters.service_name >>  \
              --query "services[0].networkConfiguration.awsvpcConfiguration.subnets[*]" \
              --output text --profile  circleci_profile --region << parameters.region >> |  tr '\t' ',')
            
            SECURITY_GROUP_ID=$(aws ecs describe-services \
              --cluster << parameters.cluster >> \
              --services << parameters.service_name >> \
              --query "services[0].networkConfiguration.awsvpcConfiguration.securityGroups" \
              --output text --profile circleci_profile --region << parameters.region >> )

            echo "SUBNET_IDS=$SUBNET_IDS" >> $BASH_ENV
            echo "SECURITY_GROUP_ID=$SECURITY_GROUP_ID" >> $BASH_ENV
      - run:
          name: Run One-Time ECS Task
          command: |
            # Run a one-time ECS task with the retrieved subnet IDs and security group ID
            echo "Running ECS task with subnet IDs: $SUBNET_IDS and security group ID: $SECURITY_GROUP_ID"
            TASK_ID=$(aws ecs run-task \
              --cluster << parameters.cluster >> \
              --task-definition << parameters.task_def >> \
              --launch-type FARGATE \
              --count 1 \
              --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_IDS],securityGroups=[$SECURITY_GROUP_ID],assignPublicIp=ENABLED}" \
              --query "tasks[0].taskArn" \
              --output text \
              --region << parameters.region >> \
              --profile circleci_profile)
            echo "Started ECS task with task ARN: $TASK_ID"
            echo "export TASK_ID='${TASK_ID}'" >> $BASH_ENV
      - run:
          name: Check ECS Task Status
          command: |
            echo "Started ECS task with task ARN: $TASK_ID"
            # Wait for the ECS task to complete (retry every 10 seconds)-  20 minutes
            for i in {1..120}; do
              TASK_STATUS=$(aws ecs describe-tasks \
                --cluster << parameters.cluster >> \
                --tasks $TASK_ID \
                --query "tasks[0].lastStatus" \
                --output text \
                --region << parameters.region >> \
                --profile circleci_profile)

              if [ "$TASK_STATUS" == "STOPPED" ]; then
                echo "Task completed successfully."
                break
              fi
              echo "Task is still running. Checking again in 10 seconds..."
              sleep 10
            done

            # Check for any errors in the task's exit code
            EXIT_CODE=$(aws ecs describe-tasks \
              --cluster << parameters.cluster >>  \
              --tasks $TASK_ID \
              --query "tasks[0].containers[0].exitCode" \
              --output text \
              --region << parameters.region >> \
              --profile circleci_profile)

            if [ "$EXIT_CODE" != "0" ]; then
              echo "Task failed with exit code $EXIT_CODE."
              exit 1  # Exit CircleCI job with failure
            fi
            echo "Task completed successfully."
      - slack/notify:
          event: fail
          template: basic_fail_1

workflows:
# Log into AWS,build and push image to Amazon ECR
# To do, push celery/Dockerfile and nginx/Dockerfile
  build_and_push_image:
    when:
      and: # All must be true to trigger
        - equal: [ false, << pipeline.parameters.run_acceptance_test_data_cleanup_develop >> ]
        - equal: [ false, << pipeline.parameters.run_acceptance_test_data_cleanup_staging >> ]
        - equal: [ false, << pipeline.parameters.run_opt_out_of_weekly_emails_develop >> ]
        - equal: [ false, << pipeline.parameters.run_opt_out_of_weekly_emails_staging >> ]
        - equal: [ false, << pipeline.parameters.run_intensive_appcheckin_load_tests >> ]
        - equal: [ false, << pipeline.parameters.run_intensive_patch_load_tests >> ]
    jobs:
      - cancel_older_build_and_push_image_develop:
          filters:
            branches:
              only: develop
      - osv-scanner:
          name: scanning-for-vulnerabilities
          context: devops-slack-tokens
      - check-code-quality:
          name: check-code-quality
          context: devops-slack-tokens
      - check-tailwind-build:
          name: check-tailwind-build
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - /^CUSTEAM.*$/
      - check-missing-translations:
          name: check-missing-translations
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - /^CUSTEAM.*$/
      - notify:
          name: Notify Team
          filters:
            branches:
              only:
                - master-ie
                - master-nl
                - develop
                - staging
                - sme/dev
                - master
                - master-uk
                - master-eu

############################################################################################################################################################
      - build:
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - build_acceptance_test_container_develop

      - build_acceptance_test_container_staging

      - acceptance_tests_develop:
          requires:
            - build_acceptance_test_container_develop
          filters:
            branches:
              only:
                - testingCIFix3
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - acceptance_email_tests_develop:
          requires:
            - build_acceptance_test_container_develop
            - deploy-service-update-web-app-develop
          filters:
            branches:
              only:
                - develop
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - acceptance_tests_staging:
          requires:
            - build_acceptance_test_container_staging
          filters:
            branches:
              only:
                - testingCIFix3
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - acceptance_tests_staging_eu:
          requires:
            - build_acceptance_test_container_staging
            - staging-eu-deploy-service-update-web-app-tf
          filters:
            branches:
              only:
                - staging
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - acceptance_email_tests_staging:
          requires:
            - build_acceptance_test_container_staging
            - deploy-service-update-web-app-tf-staging
          filters:
            branches:
              only:
                - staging
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - trustd_individual_enrolment_test_staging:
          requires:
            - build_acceptance_test_container_staging
            - deploy-service-update-web-app-tf-staging
          filters:
            branches:
              only:
                - staging
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - trustd_bulk_enrolment_test_staging:
          requires:
            - trustd_individual_enrolment_test_staging
          filters:
            branches:
              only:
                - staging
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - load_tests_staging:
          requires:
            - acceptance_email_tests_staging:
                - success
                - failed
          filters:
            branches:
              only:
                - staging
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - security_acceptance_tests_develop:
          requires:
            - build_acceptance_test_container_develop
            - deploy-service-update-web-app-develop
          filters:
            branches:
              only:
                - develop
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

      - security_acceptance_tests_staging:
          requires:
            - build_acceptance_test_container_staging
            - deploy-service-update-web-app-tf-staging
          filters:
            branches:
              only:
                - staging
          post-steps:
            - jira/notify:
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>

#      - accessibility_acceptance_tests_develop:
#          requires:
#            - build_acceptance_test_container_develop
#            - deploy-service-update-web-app-develop
#          filters:
#            branches:
#              only:
#                - develop
#          post-steps:
#            - jira/notify:
#                pipeline_id: << pipeline.id >>
#                pipeline_number: << pipeline.number >>
#
#      - accessibility_acceptance_tests_staging:
#          requires:
#            - build_acceptance_test_container_staging
#            - deploy-service-update-web-app-tf-staging
#          filters:
#            branches:
#              only:
#                - staging
#          post-steps:
#            - jira/notify:
#                pipeline_id: << pipeline.id >>
#                pipeline_number: << pipeline.number >>

      # New relic tracking - develop
      - new-relic/record-deployment: &new-relic-record-deployment-develop
          name: new-relic-record-deployment-develop-django-web
          context: newrelic
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnwxMDAzMDc3NzUw"
          entity_type: entity
          requires:
            - deploy-service-update-web-app-develop
          filters:
            branches:
              only:
                - develop
      - new-relic/record-deployment:
          <<: *new-relic-record-deployment-develop
          name: new-relic-record-deployment-develop-django-api
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnwxMDg3NjY1NzQy"
      - new-relic/record-deployment:
          <<: *new-relic-record-deployment-develop
          name: new-relic-record-deployment-develop-django-celery
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnwxMDAzMDgzNDc2"
  
      # New relic tracking - staging
      - new-relic/record-deployment: &new-relic-record-deployment-staging
          name: new-relic-record-deployment-staging-django-web
          context: newrelic
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnw5NDc4MjIwMjU"
          entity_type: entity
          requires:
            - deploy-service-update-web-app-tf-staging
          filters:
            branches:
              only:
                - staging
      - new-relic/record-deployment:
          <<: *new-relic-record-deployment-staging
          name: new-relic-record-deployment-staging-django-api
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnwxMDg1NTIxOTA2"
      - new-relic/record-deployment:
          <<: *new-relic-record-deployment-staging
          name: new-relic-record-deployment-staging-django-celery
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnw5NDc4ODA5NDU"

      # New relic tracking - production 
      - new-relic/record-deployment: &new-relic-record-deployment-prod
          name: new-relic-record-deployment-prod-celery
          context: newrelic
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnwxMDQ5MzY1Njk1"
          entity_type: entity
          requires:
            - deploy-service-update-web-app-api-prod
          filters:
            branches:
              only:
                - master
      - new-relic/record-deployment:
          <<: *new-relic-record-deployment-prod
          name: new-relic-record-deployment-prod-api
          id: "MzExNDc2fEFQTXxBUFBMSUNBVElPTnw5NDgzNjU2Njc"
      - new-relic/record-deployment: 
          <<: *new-relic-record-deployment-prod
          name: new-relic-record-deployment-prod-web
          id: "*******************************************"
   
    ###############################################################################Master#############################################################
      # Production environment - master branch
      - aws-ecr/build_and_push_image:
          name: build-and-push-prod
          executor: arm-medium
          platform: linux/arm64
          requires:
            - build
          account_id: "************"
          auth: 
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
          create_repo: true
          # Name of dockerfile to use. Defaults to Dockerfile.
          dockerfile: compose/production/django/Dockerfile
          # AWS_REGION_ENV_VAR_NAME
          region: eu-west-2
          # myECRRepository
          repo: 'prod-web-app'
          # myECRRepoTag
          #tag: "$CIRCLE_SHA1"
          filters:
            branches:
              only:
                - master
      - aws-ecr/build_and_push_image:
          name: build-and-push-prod-celery
          executor: arm-medium
          platform: linux/arm64
          requires:
            - build
          account_id: "************"
          auth: 
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
          create_repo: true
          # Name of dockerfile to use. Defaults to Dockerfile.
          dockerfile: compose/production/celery/Dockerfile
          # AWS_REGION_ENV_VAR_NAME
          region: eu-west-2
          # myECRRepository
          repo: 'prod-web-app-celery'
          # myECRRepoTag
          #tag: "$CIRCLE_SHA1"
          filters:
            branches:
              only:
                - master
      - aws-ecr/build_and_push_image:
          name: build-and-push-prod-nginx
          executor: arm-medium
          platform: linux/arm64
          requires:
            - build
          account_id: "************"
          auth: 
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
          create_repo: true
          # Name of dockerfile to use. Defaults to Dockerfile.
          dockerfile: compose/production/nginx/Dockerfile
          # AWS_REGION_ENV_VAR_NAME
          region: eu-west-2
          # myECRRepository
          repo: 'prod-web-app-nginx'
          # myECRRepoTag
          #tag: "$CIRCLE_SHA1"
          filters:
            branches:
              only:
                - master
      # Production environment
      - ecs-update-service:
          name: deploy-service-update-web-app-prod
          requires:
            - build-and-push-prod
            - build-and-push-prod-celery
            - build-and-push-prod-nginx
          role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
          region: eu-west-2
          context: devops-slack-tokens
          family: prod-web-app-service
          cluster: prod-web-app-cluster
          verify_revision_is_deployed: true
          container_image_name_updates: container=prod-web-app,tag=latest
          filters:
            branches:
              only:
                - master

      - ecs-update-service:
          name: deploy-service-update-web-app-api-prod
          requires:
            - build-and-push-prod
            - build-and-push-prod-celery
            - build-and-push-prod-nginx
          role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
          region: eu-west-2
          family: prod-web-api-service
          verify_revision_is_deployed: true
          service_name: prod-web-app-api
          context: devops-slack-tokens
          cluster: prod-web-app-cluster
          container_image_name_updates: container=prod-web-api,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-app-celery-prod
          requires:
            - build-and-push-prod
            - build-and-push-prod-celery
            - build-and-push-prod-nginx
          role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
          region: eu-west-2
          family: prod-web-app-celery
          context: devops-slack-tokens
          verify_revision_is_deployed: true
          service_name: cs-prod-web-celery
          cluster: prod-web-app-cluster
          container_image_name_updates: container=prod-web-app-celery,tag=latest
          filters:
            branches:
              only:
                - master
      # # Websocket deployment for production - this will be enabled before websocket deployed to production
      # - ecs-update-service:
      #     name: deploy-service-update-web-app-websocket-prod
      #     requires:
      #       - build-and-push-prod
      #       - build-and-push-prod-celery
      #       - build-and-push-prod-nginx
      #     role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
      #     region: eu-west-2
      #     family: cs-prod-websocket
      #     context: devops-slack-tokens
      #     verify_revision_is_deployed: true
      #     service_name: cs-prod-websocket
      #     cluster: prod-web-app-cluster
      #     container_image_name_updates: container=prod-websocket,tag=latest
      #     filters:
      #       branches:
      #         only:
      #           - master
      - run-migration-task:
          name: perform-prod-migration-and-create-materialise-view
          requires:
            - build-and-push-prod
            - build-and-push-prod-celery
            - build-and-push-prod-nginx
          role_arn: arn:aws:iam::************:role/cs-root-circle-ci-aws-role
          region: eu-west-2
          service_name: 'prod-web-app-service'
          cluster: 'prod-web-app-cluster'
          task_def: 'cs-prod-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - master

######## Deploy to Cybersmart AU #####
      - ecs-update-service:
          name: deploy-service-update-web-app-prod-au
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          family: cs-au-web-app-service
          verify_revision_is_deployed: true
          cluster: cs-au-web-app-cluster
          container_image_name_updates: container=au-web-app,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-api-prod-au
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          family: cs-au-web-api-service
          verify_revision_is_deployed: true
          cluster: cs-au-web-app-cluster
          container_image_name_updates: container=au-web-api,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-celery-prod-au
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          family: cs-au-web-celery-service
          verify_revision_is_deployed: true
          cluster: cs-au-web-app-cluster
          container_image_name_updates: container=au-web-celery,tag=latest
          filters:
            branches:
              only:
                - master
      # Websocket deployment for AU - this will be enabled before websocket deployed to production
      # - ecs-update-service:
      #     name: deploy-service-update-web-websocket-prod-au
      #     requires:
      #       - deploy-service-update-web-app-api-prod
      #     context: devops-slack-tokens
      #     role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
      #     region: ap-southeast-2
      #     family: cs-au-websocket-service
      #     verify_revision_is_deployed: true
      #     cluster: cs-au-web-app-cluster
      #     container_image_name_updates: container=au-websocket,tag=latest
      #     filters:
      #       branches:
      #         only:
      #           - master
      - run-migration-task:
          name: perform-prod-au-migration-and-create-materialise-view
          requires:
            - deploy-service-update-web-app-api-prod
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          service_name: 'cs-au-web-app-service'
          cluster: 'cs-au-web-app-cluster'
          task_def: 'cs-au-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - master

######## Deploy to Cybersmart NZ #####
      - ecs-update-service:
          name: deploy-service-update-web-app-prod-nz
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          verify_revision_is_deployed: true
          family: cs-nz-web-app-service
          cluster: cs-nz-web-app-cluster
          container_image_name_updates: container=nz-web-app,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-api-prod-nz
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          verify_revision_is_deployed: true
          family: cs-nz-web-api-service
          cluster: cs-nz-web-app-cluster
          container_image_name_updates: container=nz-web-api,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-celery-prod-nz
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          verify_revision_is_deployed: true
          family: cs-nz-web-celery-service
          cluster: cs-nz-web-app-cluster
          container_image_name_updates: container=nz-web-celery,tag=latest
          filters:
            branches:
              only:
                - master
      # Websocket deployment for NZ - this will be enabled before websocket deployed to production
      # - ecs-update-service:
      #     name: deploy-service-update-web-websocket-prod-nz
      #     requires:
      #       - deploy-service-update-web-app-api-prod
      #     context: devops-slack-tokens
      #     role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
      #     region: ap-southeast-2
      #     verify_revision_is_deployed: true
      #     family: cs-nz-websocket-service
      #     cluster: cs-nz-web-app-cluster
      #     container_image_name_updates: container=nz-websocket,tag=latest
      #     filters:
      #       branches:
      #         only:
      #           - master
      - run-migration-task:
          name: perform-prod-nz-migration-and-create-materialise-view
          requires:
            - deploy-service-update-web-app-api-prod
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: ap-southeast-2
          service_name: 'cs-nz-web-app-service'
          cluster: 'cs-nz-web-app-cluster'
          task_def: 'cs-nz-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - master
######## Deploy to Cybersmart IE #####
      - ecs-update-service:
          name: deploy-service-update-web-app-prod-ie
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-west-1
          verify_revision_is_deployed: true
          family: cs-ie-web-app-service
          cluster: cs-ie-web-app-cluster
          container_image_name_updates: container=ie-web-app,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-api-prod-ie
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-west-1
          verify_revision_is_deployed: true
          family: cs-ie-web-api-service
          cluster: cs-ie-web-app-cluster
          container_image_name_updates: container=ie-web-api,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-celery-prod-ie
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-west-1
          verify_revision_is_deployed: true
          family: cs-ie-web-celery-service
          cluster: cs-ie-web-app-cluster
          container_image_name_updates: container=ie-web-celery,tag=latest
          filters:
            branches:
              only:
                - master
      # Websocket deployment for IE - this will be enabled before websocket deployed to production
      # - ecs-update-service:
      #     name: deploy-service-update-web-websocket-prod-ie
      #     requires:
      #       - deploy-service-update-web-app-api-prod
      #     context: devops-slack-tokens
      #     role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
      #     region: eu-west-1
      #     verify_revision_is_deployed: true
      #     family: cs-ie-websocket-service
      #     cluster: cs-ie-web-app-cluster
      #     container_image_name_updates: container=ie-websocket,tag=latest
      #     filters:
      #       branches:
      #         only:
      #           - master
      - run-migration-task:
          name: perform-prod-ie-migration-and-create-materialise-view
          requires:
            - deploy-service-update-web-app-api-prod
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-west-1
          service_name: 'cs-ie-web-app-service'
          cluster: 'cs-ie-web-app-cluster'
          task_def: 'cs-ie-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - master
######## Deploy to Cybersmart EU #####
      - ecs-update-service:
          name: deploy-service-update-web-app-prod-eu
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-central-1
          verify_revision_is_deployed: true
          family: cs-eu-web-app-service
          cluster: cs-eu-web-app-cluster
          container_image_name_updates: container=eu-web-app,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-api-prod-eu
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-central-1
          verify_revision_is_deployed: true
          family: cs-eu-web-api-service
          cluster: cs-eu-web-app-cluster
          container_image_name_updates: container=eu-web-api,tag=latest
          filters:
            branches:
              only:
                - master
      - ecs-update-service:
          name: deploy-service-update-web-celery-prod-eu
          requires:
            - deploy-service-update-web-app-api-prod
          context: devops-slack-tokens
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-central-1
          verify_revision_is_deployed: true
          family: cs-eu-web-celery-service
          cluster: cs-eu-web-app-cluster
          container_image_name_updates: container=eu-web-celery,tag=latest
          filters:
            branches:
              only:
                - master
      # Websocket deployment for EU - this will be enabled before websocket deployed to production
      # - ecs-update-service:
      #     name: deploy-service-update-web-websocket-prod-eu
      #     requires:
      #       - deploy-service-update-web-app-api-prod
      #     context: devops-slack-tokens
      #     role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
      #     region: eu-central-1
      #     verify_revision_is_deployed: true
      #     family: cs-eu-websocket-service
      #     cluster: cs-eu-web-app-cluster
      #     container_image_name_updates: container=eu-websocket,tag=latest
      #     filters:
      #       branches:
      #         only:
      #           - master
      - run-migration-task:
          name: perform-prod-eu-migration-and-create-materialise-view
          requires:
            - deploy-service-update-web-app-api-prod
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-central-1
          service_name: 'cs-eu-web-app-service'
          cluster: 'cs-eu-web-app-cluster'
          task_def: 'cs-eu-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - master
# ###############################################################Develop Build##################################################################################

# note images are shared since they will not differ between countries
      - aws-ecr/build_and_push_image:
          name: build-and-push-develop-celery-web-app
          executor: arm-medium
          platform: linux/arm64
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          create_repo: false
          dockerfile: compose/production/celery/Dockerfile
          context: 'AWS-Staging'
          region: eu-west-2
          repo: 'cs-develop-web-app-celery'
          filters:
            branches:
              only:
                - develop
      - aws-ecr/build_and_push_image:
          name: build-and-push-develop-nginx-web-app
          executor: arm-medium
          platform: linux/arm64
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          create_repo: false
          dockerfile: compose/production/nginx/Dockerfile
          context: 'AWS-Staging'
          region: eu-west-2
          repo: 'cs-develop-web-app-nginx'
          filters:
            branches:
              only:
                - develop
      - aws-ecr/build_and_push_image:
          name: build-and-push-develop-web-app
          executor: arm-medium
          platform: linux/arm64
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          create_repo: false
          dockerfile: compose/production/django/Dockerfile
          context: 'AWS-Staging'
          region: eu-west-2
          repo: 'cs-develop-web-app'
          filters:
            branches:
              only:
                - develop

      - run-migration-task:
          name: perform-develop-migration-and-create-materialise-view
          requires:
            - build-and-push-develop-web-app
            - build
            - scanning-for-vulnerabilities
            - check-code-quality
            - build-and-push-develop-nginx-web-app
            - build-and-push-develop-celery-web-app
            - build-and-push-develop-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          service_name: 'cs-develop-web-app-service'
          cluster: 'cs-develop-web-app-cluster'
          task_def: 'cs-develop-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - develop
      # Develop Environment Deployment
      - ecs-update-service:
          name: deploy-service-update-web-app-develop
          requires:
            - build
            - scanning-for-vulnerabilities
            - check-code-quality
            - build-and-push-develop-nginx-web-app
            - build-and-push-develop-celery-web-app
            - build-and-push-develop-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          context: devops-slack-tokens
          family: cs-develop-web-app-service
          verify_revision_is_deployed: true
          container_image_name_updates: container=develop-web-app,tag=latest
          cluster: cs-develop-web-app-cluster
          filters:
            branches:
              only:
                - develop
      - ecs-update-service:
          name: deploy-service-update-web-api-develop
          requires:
            - build
            - scanning-for-vulnerabilities
            - check-code-quality
            - build-and-push-develop-nginx-web-app
            - build-and-push-develop-celery-web-app
            - build-and-push-develop-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          context: devops-slack-tokens
          family: cs-develop-web-api-service
          verify_revision_is_deployed: true
          container_image_name_updates: container=develop-web-api,tag=latest
          cluster: cs-develop-web-app-cluster
          filters:
            branches:
              only:
                - develop
      - ecs-update-service:
          name: deploy-service-update-web-celery-develop
          requires:
            - build
            - scanning-for-vulnerabilities
            - check-code-quality
            - build-and-push-develop-nginx-web-app
            - build-and-push-develop-celery-web-app
            - build-and-push-develop-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          context: devops-slack-tokens
          family: cs-develop-web-celery-service
          verify_revision_is_deployed: true
          container_image_name_updates: container=develop-web-celery,tag=latest
          cluster: cs-develop-web-app-cluster
          filters:
            branches:
              only:
                - develop
      # Websocket deployment for develop
      - ecs-update-service:
          name: deploy-service-update-web-websocket-develop
          requires:
            - build
            - scanning-for-vulnerabilities
            - check-code-quality
            - build-and-push-develop-nginx-web-app
            - build-and-push-develop-celery-web-app
            - build-and-push-develop-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          context: devops-slack-tokens
          family: cs-develop-websocket-service
          verify_revision_is_deployed: true
          container_image_name_updates: container=develop-websocket,tag=latest
          cluster: cs-develop-web-app-cluster
          filters:
            branches:
              only:
                - develop

#     # ###############################Staging#######################################

      - aws-ecr/build_and_push_image:
          name: build-and-push-staging-cve-web-app
          executor: arm-medium
          platform: linux/arm64
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          create_repo: false
          dockerfile: compose/production/nvdtools/Dockerfile
          context: 'AWS-Staging'
          region: eu-west-2
          repo: 'cs-staging-vulnerability-matcher'
          filters:
            branches:
              only:
                - staging
      - aws-ecr/build_and_push_image:
          name: build-and-push-staging-celery-web-app
          executor: arm-medium
          platform: linux/arm64
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          create_repo: false
          dockerfile: compose/production/celery/Dockerfile
          context: 'AWS-Staging'
          region: eu-west-2
          repo: 'cs-staging-web-app-celery'
          filters:
            branches:
              only:
                - staging
      - aws-ecr/build_and_push_image:
          name: build-and-push-staging-nginx-web-app
          executor: arm-medium
          platform: linux/arm64
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          create_repo: false
          dockerfile: compose/production/nginx/Dockerfile
          context: 'AWS-Staging'
          region: eu-west-2
          repo: 'cs-staging-web-app-nginx'
          filters:
            branches:
              only:
                - staging
      - aws-ecr/build_and_push_image:
          name: build-and-push-staging-web-app
          executor: arm-medium
          platform: linux/arm64
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          create_repo: false
          dockerfile: compose/production/django/Dockerfile
          context: 'AWS-Staging'
          region: eu-west-2
          repo: 'cs-staging-web-app'
          filters:
            branches:
              only:
                - staging
    #Staging Deployment
      - ecs-update-service:
          name: deploy-service-update-web-app-tf-staging
          requires:
            - build
            - build-and-push-staging-nginx-web-app
            - build-and-push-staging-celery-web-app
            - build-and-push-staging-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-web-app-service
          container_image_name_updates: container=staging-web-app,tag=latest
          cluster: cs-staging-web-app-cluster
          filters:
            branches:
              only:
                - staging
      - ecs-update-service:
          name: deploy-service-update-web-api-tf-staging
          requires:
            - build
            - build-and-push-staging-nginx-web-app
            - build-and-push-staging-celery-web-app
            - build-and-push-staging-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-web-api-service
          container_image_name_updates: container=staging-web-api,tag=latest
          cluster: cs-staging-web-app-cluster
          filters:
            branches:
              only:
                - staging
      - ecs-update-service:
          name: deploy-service-update-web-celery-tf-staging
          requires:
            - build
            - build-and-push-staging-nginx-web-app
            - build-and-push-staging-celery-web-app
            - build-and-push-staging-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-web-celery-service
          container_image_name_updates: container=staging-web-celery,tag=latest
          cluster: cs-staging-web-app-cluster
          filters:
            branches:
              only:
                - staging
      # Websocket deployment for staging
      - ecs-update-service:
          name: deploy-service-update-web-websocket-tf-staging
          requires:
            - build
            - build-and-push-staging-nginx-web-app
            - build-and-push-staging-celery-web-app
            - build-and-push-staging-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-websocket-service
          container_image_name_updates: container=staging-websocket,tag=latest
          cluster: cs-staging-web-app-cluster
          filters:
            branches:
              only:
                - staging
      # Staging Environment Deployment
      - ecs-update-service:
          name: deploy-service-update-cve-staging
          requires:
            - build
            - build-and-push-staging-cve-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          context: devops-slack-tokens
          verify_revision_is_deployed: true
          family: cs-cve-staging-web-app-service
          container_image_name_updates: container=cve-staging-web-app,tag=latest
          cluster: cs-cve-staging-web-app-cluster
          filters:
            branches:
              only:
                - staging
      - run-migration-task:
          name: perform-staging-migration-and-create-materialise-view
          requires:
            - build
            - build-and-push-staging-nginx-web-app
            - build-and-push-staging-celery-web-app
            - build-and-push-staging-web-app
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          service_name: 'cs-staging-web-app-service'
          cluster: 'cs-staging-web-app-cluster'
          task_def: 'cs-staging-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - staging
      # ###############################Staging-EU#######################################
      #Staging Deployment
      - ecs-update-service:
          name: staging-eu-deploy-service-update-web-app-tf
          requires:
            - deploy-service-update-web-app-tf-staging
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-eu-web-app-service
          container_image_name_updates: container=staging-eu-web-app,tag=latest
          cluster: cs-staging-eu-web-app-cluster
          filters:
            branches:
              only:
                - staging
      - ecs-update-service:
          name: staging-eu-deploy-service-update-web-api-tf
          requires:
            - deploy-service-update-web-app-tf-staging
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-eu-web-api-service
          container_image_name_updates: container=staging-eu-web-api,tag=latest
          cluster: cs-staging-eu-web-app-cluster
          filters:
            branches:
              only:
                - staging
      - ecs-update-service:
          name: staging-eu-deploy-service-update-web-celery-tf
          requires:
            - deploy-service-update-web-app-tf-staging
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-eu-web-celery-service
          container_image_name_updates: container=staging-eu-web-celery,tag=latest
          cluster: cs-staging-eu-web-app-cluster
          filters:
            branches:
              only:
                - staging
      # Websocket deployment for staging-eu
      - ecs-update-service:
          name: staging-eu-deploy-service-update-web-websocket-tf
          requires:
            - deploy-service-update-web-app-tf-staging
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          verify_revision_is_deployed: true
          context: devops-slack-tokens
          family: cs-staging-eu-websocket-service
          container_image_name_updates: container=staging-eu-websocket,tag=latest
          cluster: cs-staging-eu-web-app-cluster
          filters:
            branches:
              only:
                - staging
      - run-migration-task:
          name: staging-eu-perform-migration-and-create-materialise-view
          requires:
            - deploy-service-update-web-app-tf-staging
          role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
          region: eu-west-2
          service_name: 'cs-staging-eu-web-app-service'
          cluster: 'cs-staging-eu-web-app-cluster'
          task_def: 'cs-staging-eu-migration-service'
          context: devops-slack-tokens
          filters:
            branches:
              only:
                - staging
#################################Staging-AU#######################################
    #Staging Deployment
      # - ecs-update-service:
      #     name: deploy-service-update-web-app-tf-staging-au
      #     requires:
      #       - build
      #       - build-and-push-staging-nginx-web-app
      #       - build-and-push-staging-celery-web-app
      #       - build-and-push-staging-web-app
      #     role_arn: arn:aws:iam::************:role/cs-staging-circle-ci-aws-role
      #     region: ap-southeast-2
      #     verify_revision_is_deployed: true
      #     family: cs-au-staging-web-app-service
      #     container_image_name_updates: container=au-staging-web-app,tag=latest
      #     cluster: cs-au-staging-web-app-cluster
      #     context: devops-slack-tokens
      #     filters:
      #       branches:
      #         only:
      #           - staging
#################################################################CVE-PROD###########################################################
      - aws-ecr/build_and_push_image:
          name: build-and-push-prod-cve-web-app
          executor: arm-medium
          platform: linux/arm64
          requires:
            - build
          account_id: "************"
          auth:
            - aws-cli/setup:
                role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          create_repo: true
          dockerfile: compose/production/nvdtools/Dockerfile
          context: 'AWS-Production-London'
          region: eu-west-2
          repo: 'cs-cve-prod-vulnerability-matcher'
          filters:
            branches:
              only:
                - master
      # Prod Environment Deployment
      - ecs-update-service:
          name: deploy-service-update-cve-prod
          requires:
            - build-and-push-prod-cve-web-app
          role_arn: arn:aws:iam::************:role/cs-fututer-prod-circleci-role
          region: eu-west-2
          context: devops-slack-tokens
          verify_revision_is_deployed: true
          family: cs-cve-prod-web-app-service
          container_image_name_updates: container=cve-prod-web-app,tag=latest
          cluster: cs-cve-prod-web-app-cluster
          filters:
            branches:
              only:
                - master

#################################################################CVE-PROD#########################################################

  acceptance_test_scheduled_build_develop:
    when: << pipeline.parameters.run_acceptance_test_data_cleanup_develop >>
    jobs:
      - build_acceptance_test_container_develop
      - acceptance_tests_env_clean_up_develop:
          requires:
            - build_acceptance_test_container_develop

  acceptance_test_scheduled_build_staging:
    when: << pipeline.parameters.run_acceptance_test_data_cleanup_staging >>
    jobs:
      - build_acceptance_test_container_staging
      - acceptance_tests_env_clean_up_staging:
          requires:
            - build_acceptance_test_container_staging

  email_opt_out_scheduled_build_develop:
    when: << pipeline.parameters.run_opt_out_of_weekly_emails_develop >>
    jobs:
      - build_acceptance_test_container_develop
      - weekly_email_opt_out_scheduled_build_develop:
          requires:
            - build_acceptance_test_container_develop

  email_opt_out_scheduled_build_staging:
    when: << pipeline.parameters.run_opt_out_of_weekly_emails_staging >>
    jobs:
      - build_acceptance_test_container_staging
      - weekly_email_opt_out_scheduled_build_staging:
          requires:
            - build_acceptance_test_container_staging

  intensive_load_tests_appcheckin_staging:
    when: << pipeline.parameters.run_intensive_appcheckin_load_tests >>
    jobs:
      - build_acceptance_test_container_staging
      - load_tests_appcheckin_intensive_staging:
          requires:
            - build_acceptance_test_container_staging

  intensive_load_tests_patch_staging:
    when: << pipeline.parameters.run_intensive_patch_load_tests >>
    jobs:
      - build_acceptance_test_container_staging
      - load_tests_patch_intensive_staging:
          requires:
            - build_acceptance_test_container_staging
requires:
        - build