{% load static %}
{% load divide_tags %}
{% load questionnaire %}
{% load i18n %}
<div 
    {% if not q|show_question:certification.survey.pk and not prefix == "re-" %}style="display: none" {% endif %}
    class="form-body question {% if q.parent %}has-parent {% if q.mandatory_choices_pks %}required-parent-choice{% endif %}{% endif %} {% if q.parent_2 %}has-parent-two{% endif %} {% if q.children.all.count %}has-children{% endif %} {% if q.children_2.all.count %}has-children-2{% endif %}"
    data-question-order="{{ q.order_str }}"
    data-question-code="{{ q.code|default:'' }}"
    data-question-pk="{{ q.pk }}"
    data-auto-answer="{{ q.auto_answer_questions.count }}"
    data-auto-answer-url="{{ certification.auto_answer_url }}"
    data-type="{{ q.response_type }}"
    data-widget-type="{{ q.widget }}"
    {% if q.parent %} data-parent-question-order="{{ q.parent.order_str }}" data-parent-question-pk="{{ q.parent.pk }}" {% endif %}
    {% if q.parent_2 %} data-parent-question-order-two="{{ q.parent_2.order_str }}" data-parent-question-pk-two="{{ q.parent_2.pk }}" {% endif %}
    data-show-children-on="{{ q.show_children_on }}"
    data-required-parent-choice-pk="{{ q.mandatory_choices_pks }}"
    data-show-children-on-two="{{ q.show_children_on_2 }}"
>
    <div class="form-group {% if q.pk in failed_questions %} has-error {% endif %}">
        <div class="col-md-12 q">
            <div class="row {% if q.pk in failed_questions %}assessment-failed-question{% endif %}">
                {% if certos_mode %}
                    <div class="col-md-5">
                        <label class="control-label abc" style="width: 100%;text-align: left !important; margin-bottom: 5px" question-id="{{q.order_str}}">
                            <span class="pull-right assessment-ranking rank-{{ q.ranking }}">{{ q.get_ranking_display }}</span>
                            {% if q.required %}<span class="pull-right assessment-ranking rank-4">{% trans "required" %}</span>{% endif %}
                            <span class="assessment-question-number"> {{q.pervade_num }} </span>
                            <div class="tw-prose">
                                {{q.title|safe}}
                            </div>
                        </label>
                        {% include 'rulebook/assessment/answer/display.html' with allow_edit=allow_edit %}
                    </div>
                    <div class="col-md-3">
                        <div class="m-l-20">
                            <div class="assessor-crs-container">
                                <div class="{{ prefix }}assessor-crs-{{ q.pk }}">
                                    {% for note in q.not_accepted_assessor_notes %}
                                        <div class="{{ prefix }}assessor-cr {{ prefix }}assessor-cr-{{ q.pk }}">
                                            <span {% if forloop.counter == 1 %} id="{{ prefix }}assessor-cr-text-{{ q.pk }}{% endif %}">{{ note.text }}</span> <i class="fa fa-comment {{ prefix }}assessor-cr-icon pull-right" aria-hidden="true"></i><br/>
                                            <div class="text-muted m-t-15 {{ prefix }}cr-date-{{ q.pk }}">{{ note.created }}</div>
                                        </div>
                                        <button id="{{ prefix }}accept-cr-button-{{ q.pk }}" class="btn btn-success {{ prefix }}accept-cr btn-outline m-t-10" data-question-pk="{{ q.pk }}" data-certification-pk="{{ certification.pk }}" data-url="{% url 'rulebook:certos:accept-note' note.id%}">{% trans "Accept answer" %}</button>
                                        <div id="{{ prefix }}cr-accepted-{{ q.pk }}" class="text-success font-bold {% if not q.assessor_notes.all.last.answer_accepted %}accept-cr-hide{% endif %}">{% trans "Answer accepted" %}</div>
                                        <button id="{{ prefix }}add-cr-button-{{ q.pk }}" class="btn btn-default {{ prefix }}add-cr btn-outline m-t-10" data-question-pk="{{ q.pk }}">{% trans "Request clarification" %}</button>
                                    {% empty %}
                                        {% with q.assessor_notes.all.last as note %}
                                            {% if note.answer_accepted %}
                                                <div class="{{ prefix }}assessor-cr {{ prefix }}assessor-cr-{{ q.pk }}">
                                                    <span id="{{ prefix }}assessor-cr-text-{{ q.pk }}">{{ note.text }}</span> <i class="fa fa-comment {{ prefix }}assessor-cr-icon pull-right" aria-hidden="true"></i><br/>
                                                    <div class="text-muted m-t-15 {{ prefix }}cr-date-{{ q.pk }}">{{ note.created }}</div>
                                                </div>
                                                <button id="{{ prefix }}accept-cr-button-{{ q.pk }}" class="btn btn-success {{ prefix }}accept-cr accept-cr-hide btn-outline m-t-10" data-question-pk="{{ q.pk }}" data-certification-pk="{{ certification.pk }}" data-url="{% url 'rulebook:certos:accept-note' note.id%}">{% trans "Accept answer" %}</button>
                                                <div id="{{ prefix }}cr-accepted-{{ q.pk }}" class="text-success font-bold">{% trans "Answer accepted" %}</div>
                                                <button id="{{ prefix }}add-cr-button-{{ q.pk }}" class="btn btn-default {{ prefix }}add-cr btn-outline m-t-10" data-question-pk="{{ q.pk }}">{% trans "Request clarification" %}</button>
                                            {% endif %}
                                        {% endwith %}
                                    {% endfor %}
                                </div>
                            </div>
                            <textarea id="{{ prefix }}cr-textarea-{{ q.pk }}" class="form-control {{ prefix }}assessment-cr-input" placeholder="Leave your clarification request here"></textarea>
                            {% if not q.assessor_notes.count %}
                                <center>
                                    <button id="{{ prefix }}add-cr-button-{{ q.pk }}" class="btn btn-info {{ prefix }}add-cr btn-outline m-t-10" data-question-pk="{{ q.pk }}">{% trans "Add Request" %}</button>
                                </center>
                            {% endif %}
                            <button id="{{ prefix }}save-cr-button-{{ q.pk }}" style="display: none" class="btn btn-primary {{ prefix }}save-cr m-t-10" data-question-pk="{{ q.pk }}" data-certification-pk="{{ certification.pk }}" data-url="{% url 'rulebook:certos:create-note' %}">{% trans "Save" %}</button>
                            <button id="{{ prefix }}cancel-cr-button-{{ q.pk }}" style="display: none" class="btn btn-primary {{ prefix }}cancel-cr btn-outline m-t-10" data-question-pk="{{ q.pk }}">{% trans "Cancel" %}</button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="tw-prose">
                            {{ q.marking_info|default:''|safe }}
                        </div>
                    </div>
                {% else %}
                    <div class="col-md-12">
                        <label class="control-label abc" style="width: 100%;text-align: left !important; margin-bottom: 5px" question-id="{{q.order_str}}">
                            <span class="updated-in-pervade-{{ q.pk }}-{{ q.responses.first.pk }} pull-right assessment-ranking rank-5 rank-hide" {% if updated_in_pervade and q.responses.first.updated_in_pervade %}style="display: block" {% endif %}>Updated Answer</span>
                            <span class="assessment-question-number text-primary m-r-5"> {{q.pervade_num }} </span>
                            <div class="tw-prose">
                                {{q.title|safe}}
                            </div>
                        </label>
                        {% include 'rulebook/assessment/answer/display.html' with allow_edit=allow_edit %}
                    </div>
                {% endif %}
            </div>
            <div class="row">
                <hr>
            </div>
        </div>
    </div>
</div>