{% load questionnaire %}
{% load i18n %}
{% load l10n %}

<div id="expanded_options_{{ widget.question.order_str }}_{{ widget.question.pk }}">
    {% for c in widget.question.choices.all %}
        <div class="radio radio-success">
            <input id="choice_{{ c.pk }}_{{ widget.question.pk }}" class="expanded_options {% if c.auto_answer_choices.count %}auto-answer-trigger{% endif %} {% if c.allow_custom_input %}expanded_options allow-custom-input{% endif %}" name="response_{{ widget.question.pk }}_{{ widget.question.order_str }}" value="{{ c.value }}" type="radio" {% if widget.response.choice.pk == c.pk %}checked{% endif %} data-question-order="{{ widget.question.order_str}}" data-question-pk="{{ widget.question.pk }}" data-choice-pk="{{ c.pk }}">
            <label for="choice_{{ c.pk }}_{{ widget.question.pk }}">{{ c.value }}</label>
        </div>
    {% endfor %}
    {% if widget.question.other_form %}
        {% if widget.question.has_trigger_option %}
            <textarea {% if not widget.question.has_custom_answer %}style="display: none"{% endif %} class="form-control m-t-20" name="response_{{ widget.question.pk }}_{{ widget.question.order_str}}" id="other_form_{{ widget.question.order_str}}_{{ widget.question.pk }}">{% if widget.question.has_custom_answer %}{{ widget.question.has_custom_answer.0.value }}{% endif %}</textarea>
        {% else %}
            <div class="radio radio-success">
                <input id="other_{{ widget.question.pk }}" class="expanded_options" name="response_{{ widget.question.pk }}_{{ widget.question.order_str}}" data-question-order="{{ widget.question.order_str}}" data-question-pk="{{ widget.question.pk }}" value="other" type="radio" {% if widget.question.has_custom_answer %}checked{% endif %}>
                <label for="other_{{ widget.question.pk }}">{{ widget.question.other_form_label|default:"Other" }}</label>
            </div>
            <textarea {% if not widget.question.has_custom_answer %}style="display: none"{% endif %} class="form-control m-t-20" name="response_{{ widget.question.pk }}_{{ widget.question.order_str}}" id="other_form_{{ widget.question.order_str}}_{{ widget.question.pk }}">{% if widget.question.has_custom_answer %}{{ widget.response.value }}{% endif %}</textarea>
        {% endif %}
    {% endif %}
    {% if widget.response.choice.is_invalid and widget.question.is_failed %}
        </br>
        <div class="badge badge-pill badge-sm badge-danger p-r-20 p-l-20" style="text-transform: none;">
            {% trans "Note, this answer is not compliant with the certification requirements" %}
        </div>
    {% endif %}
</div>
{% if widget.question.failed_choices.all %}
    <div id="failed_expanded_options_{{ widget.question.order_str}}_{{ widget.question.pk }}" {% if widget.response %}style="display: none" {% endif %}>
        <div class="more-info" style="display: block;"><span>{% trans "Please explain: " %}</span> </div>
        <div class="clearfix"></div>
        {% for c in widget.question.failed_choices.all %}
            <div class="radio radio-success">
                <input id="choice_{{ c.pk }}_{{ widget.question.pk }}_failed" class="failed_expanded_options" name="failed_response_{{ widget.question.pk }}_{{ widget.question.order_str}}" value="{{ c.value }}" type="radio" {% if widget.failed_response.choice.pk == c.pk %}checked{% endif %} data-question-order="{{ widget.question.order_str}}" data-question-pk="{{ widget.question.pk }}">
                <label for="choice_{{ c.pk }}_{{ widget.question.pk }}_failed">{{ c.value }}</label>
            </div>
        {% endfor %}
        {% if widget.question.other_form %}
            <div class="radio radio-success">
                <input id="other_{{ widget.question.pk }}_failed" class="failed_expanded_options" name="failed_response_{{ widget.question.pk }}_{{ widget.question.order_str}}" data-question-order="{{ widget.question.order_str}}" data-question-pk="{{ widget.question.pk }}" value="other" type="radio" {% if widget.question.failed_custom_choice %}checked{% endif %}>
                <label for="other_{{ widget.question.pk }}_failed">{{ widget.question.other_form_label|default:"Other" }}</label>
            </div>
            <textarea {% if not widget.question.failed_custom_choice %}style="display: none"{% endif %} class="form-control m-t-20" name="failed_response_{{ widget.question.pk }}_{{ widget.question.order_str}}" id="failed_other_form_{{ widget.question.order_str}}_{{ widget.question.pk }}">{% if widget.question.failed_custom_choice %}{{ widget.failed_response.value }}{% endif %}</textarea>
        {% endif %}
    </div>
{% endif %}
