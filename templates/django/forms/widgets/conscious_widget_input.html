{% load questionnaire %}
{% load i18n %}
<div class="radio radio-success">
    <input class="conscious_radio" type="radio" {% if widget.response.value_boolean %}checked{% endif %} id="response_{{ widget.question.pk }}_yes" name="{{ widget.name }}" value="on" data-question-compliant_answer="{{ widget.question.compliant_answer }}" data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}">
    <label for="response_{{ widget.question.pk }}_yes">{% trans "Yes" %}</label>
</div>
<div class="radio radio-success">
    <input class="conscious_radio" type="radio" {% if widget.response.value_boolean is False %}checked{% endif %} id="response_{{ widget.question.pk }}_no" name="{{ widget.name }}" value="off" data-question-compliant_answer="{{ widget.question.compliant_answer }}" data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}">
    <label for="response_{{ widget.question.pk }}_no">{% trans "No" %}</label>
</div>
{% if not widget.question.show_text_box %}
<input type="hidden" name="{{ widget.name }}" value="">
{% endif %}
<div class="clearfix"></div>

{% if widget.question.show_text_box %}
    <div id="expanded_options_{{ widget.question.order_str }}_{{ widget.question.pk }}" {% if not widget.response.conscious_value_is_compliant %}hidden{% endif %}>
        <div class="more-info" style="display: block;"><span>{% if not widget.question.text_box_is_required %}{% trans "Optional explanation: " %}{% else %}{% trans "Please explain: " %}{% endif %}</span> </div>
        <div class="clearfix"></div>
        <textarea class="form-control m-t-20" name="{{ widget.name }}" id="other_form_{{ widget.question.order_str }}_{{ widget.question.pk }}">{{ widget.response.value_text|default:"" }}</textarea>
    </div>
{% endif %}
