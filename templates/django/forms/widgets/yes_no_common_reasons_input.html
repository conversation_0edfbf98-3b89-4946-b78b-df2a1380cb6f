{% load questionnaire %}
{% load i18n %}

<input type="checkbox" {% if widget.response.value_boolean %}checked{% endif %} data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}" name="{{ widget.name }}" class="js-switch js-check-change show-textarea-always"/>
<div class="clearfix"></div>

<div id="expanded_options_{{ widget.question.order_str }}_{{ widget.question.pk }}">
    <div class="more-info" style="display: block;"><span>{% if widget.attrs.optional_text %}{% trans "Optional explanation: " %}{% else %}{% trans "Please explain: " %}{% endif %}</span> </div>
    <div class="clearfix"></div>
    <textarea class="form-control m-t-20" name="response_{{ widget.question.pk }}_{{ widget.question.order_str }}" id="other_form_{{ widget.question.order_str }}_{{ widget.question.pk }}">{% if widget.question.has_custom_answer %}{{ widget.response.value_text|default:"" }}{% endif %}</textarea>
    {% if widget.response.choice.is_invalid and widget.question.is_failed %}
        <div class="badge badge-pill badge-sm badge-danger p-r-20 p-l-20" style="text-transform: none;">
            {% trans "Note, this answer is not compliant with the certification requirements" %}
        </div>
    {% endif %}
</div>
{% if widget.question.common_reasons %}
    <div class="tw-prose">
        {{ widget.question.common_reasons|safe }}
    </div>
{% endif %}
