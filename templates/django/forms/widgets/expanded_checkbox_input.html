{% load i18n %}
{% for c in widget.question.choices.all %}
    <div class="checkbox checkbox-success">
        {% with "response_"|add:widget.question.pk|add:"_"|add:widget.question.order_str|add:"_"|add:c.pk as name %}
            {% include "django/forms/widgets/checkbox_send_off_values.html" with hidden_checkbox_name=name enabled=True %}
        {% endwith %}
        <input id="checkbox_{{ c.pk }}_{{ widget.question.pk }}" class="checkbox-choices {% if c.allow_custom_input %}expanded_options allow-custom-input{% endif %} {% if c.mandatory_questions.all.count %}has-required-questions{% endif %}" data-checkbox-choice-pk="{{ c.pk }}" type="{{ widget.type }}" name="response_{{ widget.question.pk }}_{{ widget.question.order_str }}_{{ c.pk }}" {% if c.responses.all.0 %}checked{% endif %} data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}"/>
        <label for="checkbox_{{ c.pk }}_{{ widget.question.pk }}">{{ c.value }}</label>
    </div>
{% endfor %}
{% if widget.question.other_form %}
    <textarea {% if not widget.question.has_custom_answer %}style="display: none"{% endif %} class="form-control m-t-20" name="response_{{ widget.question.pk }}_{{ widget.question.order_str }}_other_value" id="other_form_{{ widget.question.order_str }}_{{ widget.question.pk }}">{% if widget.question.has_custom_answer %}{{ widget.question.has_custom_answer.0.value }}{% endif %}</textarea>
{% endif %}