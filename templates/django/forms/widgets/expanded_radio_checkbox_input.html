{% load i18n %}
{% if widget.question.hide_choices %}
    <input type="checkbox" {% if widget.response %}checked{% endif %} data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}" name="{{ widget.name }}" class="js-switch js-check-change"/>
    <div class="clearfix"></div>
{% endif %}
<div {% if not widget.response and widget.question.hide_choices %}style="display: none"{% endif %} id="expanded_options_{{ widget.question.order_str }}_{{ widget.question.pk }}">
    <div class="more-info show_expanded_options" style="display: block;"><span>{% trans "Expanded options: " %}</span> </div>
    <div class="clearfix"></div>
    {% for c in widget.question.choices.all %}
        <div class="checkbox checkbox-success">
            {% with "response_"|add:widget.question.pk|add:"_"|add:widget.question.order_str|add:"_"|add:c.pk as name %}
                {% include "django/forms/widgets/checkbox_send_off_values.html" with hidden_checkbox_name=name enabled=True %}
            {% endwith %}
            <input id="radio_checkbox_{{ c.pk }}_{{ widget.question.pk }}" class="checkbox-choices {% if c.mandatory_questions.all.count %}has-required-questions{% endif %}" data-checkbox-choice-pk="{{ c.pk }}" type="{{ widget.type }}" name="response_{{ widget.question.pk }}_{{ widget.question.order_str }}_{{ c.pk }}" {% if c.responses.all.0 %}checked{% endif %}/>
            <label for="radio_checkbox_{{ c.pk }}_{{ widget.question.pk }}">{{ c.value }}</label>
        </div>
    {% endfor %}
    {% if widget.question.other_form %}
        <div class="checkbox checkbox-success">
            {% with "response_"|add:widget.question.pk|add:"_"|add:widget.question.order_str|add:"_other" as name %}
                {% include "django/forms/widgets/checkbox_send_off_values.html" with hidden_checkbox_name=name enabled=True %}
            {% endwith %}
            <input id="checkbox_other_{{ widget.question.pk }}" value="other" class="expanded_options" data-checkbox-choice-pk="other" type="{{ widget.type }}" name="response_{{ widget.question.pk }}_{{ widget.question.order_str }}_other" data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}" {% if widget.question.has_custom_answer %}checked{% endif %}/>
            <label for="checkbox_other_{{ widget.question.pk }}">{{ widget.question.other_form_label|default:"Other" }}</label>
        </div>
        <textarea {% if not widget.question.has_custom_answer %}style="display: none"{% endif %} class="form-control m-t-20" name="response_{{ widget.question.pk }}_{{ widget.question.order_str }}_other_value" id="other_form_{{ widget.question.order_str }}_{{ widget.question.pk }}">{% if widget.question.has_custom_answer %}{{ widget.question.response.value }}{% endif %}</textarea>
    {% endif %}
</div>
{% if widget.question.failed_choices.all %}
    <div id="failed_expanded_options_{{ widget.question.order_str }}_{{ widget.question.pk }}" {% if widget.response %}style="display: none" {% endif %}>
        <div class="more-info" style="display: block;"><span>{% trans "Please explain: " %}</span> </div>
        <div class="clearfix"></div>
        {% for c in widget.question.failed_choices.all %}
            <div class="radio radio-success">
                <input id="radio_checkbox_{{ c.pk }}_{{ widget.question.pk }}_failed" class="failed_expanded_options" name="failed_response_{{ widget.question.pk }}_{{ widget.question.order_str }}" value="{{ c.value }}" type="radio" {% if widget.failed_response.choice.pk == c.pk %}checked{% endif %} data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}">
                <label for="radio_checkbox_{{ c.pk }}_{{ widget.question.pk }}_failed">{{ c.value }}</label>
            </div>
        {% endfor %}
        {% if widget.question.other_form %}
            <div class="radio radio-success">
                <input id="other_{{ widget.question.pk }}_failed" class="failed_expanded_options" name="failed_response_{{ widget.question.pk }}_{{ widget.question.order_str }}" data-question-order="{{ widget.question.order_str }}" data-question-pk="{{ widget.question.pk }}" value="other" type="radio" {% if widget.question.failed_custom_choice %}checked{% endif %}>
                <label for="other_{{ widget.question.pk }}_failed">{{ widget.question.other_form_label|default:"Other" }}</label>
            </div>
            <textarea {% if not widget.question.failed_custom_choice %}style="display: none"{% endif %} class="form-control m-t-20" name="failed_response_{{ widget.question.pk }}_{{ widget.question.order_str }}" id="failed_other_form_{{ widget.question.order_str }}_{{ widget.question.pk }}">{% if widget.question.failed_custom_choice %}{{ widget.failed_response.value }}{% endif %}</textarea>
        {% endif %}
    </div>
{% endif %}