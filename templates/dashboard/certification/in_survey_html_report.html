{% load static %}
{% load i18n %}
{% load questionnaire %}
{% load divide_tags %}
<div class="row">
    <div class="col-md-12">
        <div class="panel-wrapper collapse in" aria-expanded="true">
            <div class="panel-body">
                <form id='submitForm' enctype="multipart/form-data" name="submitForm" action="." method="post" class="form-horizontal form-bordered" novalidate>
                    <input type="hidden" name="time" id="time">
                    {% csrf_token %}
                    <div id="exTab2">
                        <ul class="nav nav-tabs">
                            {% for topic in topics %}
                                <li class="tab_li{{topic.order}} {% if request|current_topic:certification %}{% if request|current_topic:certification == topic.order %} active{% endif %}{% else %}{% if forloop.counter == 1 %} active{% endif %}{% endif %}" questions="{{topic.order}}">
                                    <a href="#tab{{topic.order}}" data-toggle="tab" class="{% topic_status topic topics_status_structure 'complete_section' 'error' '' failed_questions %}">{{topic.title|escape}}
                                        <div class="arrow-right"></div>
                                        <div class="arrow-left"></div>
                                    </a>
                                </li>
                            {% endfor %}
                        </ul>
                        <div class="tab-content">
                            {% for topic in topics %}
                                <div id="tab{{ topic.order }}" class="tab-pane {% if request|current_topic:certification %}{% if request|current_topic:certification == topic.order %} active{% endif %}{% else %}{% if forloop.counter == 1 %} active{% endif %}{% endif %}">
                                    {% if topic.description %}
                                        <div class="text-center p-b-m topic-description">
                                            <div>{{ topic.description|safe }}</div>
                                        </div>
                                    {% endif %}
                                    {% for q in topic.questions.all %}
                                        {% if q.code == "cyber_insurance_assessment_scope" and organisation.is_aviva_customer  and certification.type == CYBER_ESSENTIALS%}
                                        {% else %}
                                        <div {% if not q|show_question:certification.survey.pk %}style="display: none" {% endif %} class="form-body question {% if q.parent %}has-parent {% if q.mandatory_choices_pks %}required-parent-choice{% endif %}{% endif %} {% if q.parent_2 %}has-parent-two{% endif %} {% if q.children.all.count %}has-children{% endif %} {% if q.children_2.all.count %}has-children-2{% endif %}" data-question-order="{{ q.order_str }}" data-question-code="{{ q.code|default:'' }}" data-question-pk="{{ q.pk }}" data-auto-answer="{{ q.auto_answer_questions.count }}" data-auto-answer-url="{{ certification.auto_answer_url }}" data-type="{{ q.response_type }}" data-widget-type="{{ q.widget }}" {% if q.parent %} data-parent-question-order="{{ q.parent.order_str }}" data-parent-question-pk="{{ q.parent.pk }}" {% endif %} {% if q.parent_2 %} data-parent-question-order-two="{{ q.parent_2.order_str }}" data-parent-question-pk-two="{{ q.parent_2.pk }}" {% endif %} data-show-children-on="{{ q.show_children_on }}" data-required-parent-choice-pk="{{ q.mandatory_choices_pks }}" data-show-children-on-two="{{ q.show_children_on_2 }}">
                                            <div class="form-group {% if q.order_str in failed_questions.keys|sort_list %} has-error {% endif %}">
                                                <div class="col-md-12 q">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <label class="control-label abc tw-prose" style="width: 100%;text-align: left !important; margin-bottom: 5px" question-id="{{q.order_str}}">{{ q.order_str }}. {{q.title|safe}}</label>
                                                            {% create_response_form q certification %}
                                                        </div>
                                                        <div class="col-md-6">
                                                            {% if q.tooltip %}
                                                                <div class="certification-plain-english tw-prose"><label>{% trans 'Plain English' %}: </label> {{q.tooltip|safe}}</div>
                                                            {% endif %}
                                                            {% if q.moreinfo %}
                                                                <div class="certification-implementation-tips text-primary tw-prose">
                                                                    <label>{% trans 'Official guidance' %}: </label> {{q.moreinfo|safe}}
                                                                </div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
