{% load static %}
{% load questionnaire %}
{% load divide_tags %}
{% load insurance_tags %}
{% load i18n %}
{% load waffle_tags %}

{% block extra-js %}
    <script src="{% static 'js/jquery.inputmask.bundle.min.js' %}"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js" integrity="sha512-dfX5uYVXzyU8+KHqj8bjo7UkOdg18PaOtpa48djpNbZHwExddghZ+ZmzWT06R5v6NSk3ZUfsH6FNEDepLx9hPQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="{% static 'js/highlights-textarea.js' %}"></script>
    <script type="text/javascript">
        function ParseCSV(element, pk) {
            var fileUpload = element.files;
            if (fileUpload[0].type == "text/csv" || fileUpload[0].type == "application/vnd.ms-excel") {
                if (typeof (FileReader) != "undefined") {
                    Papa.parse(fileUpload[0], {
                        complete: function(results) {
                            const question = $('[question-pk='+pk+']');
                            question.val(
                                Papa.unparse(results.data, {delimiter: ", "})
                            );
                            question.css('color', 'black');
                            // In case of A2.4 question, clear the backdrop div (see highlights-textarea.js)
                            question.parent().find('.backdrop .highlights').text('');
                            $('.unsupported-os-warning').hide();
                        }
                    });
                } else {
                    alert("This browser does not support HTML5.");
                }
            } else {
                alert("Please upload a valid CSV file.");
            }
        }
    </script>
    <script type='text/javascript'>
        {% flag 'user-guiding' %}
            {% if certification.survey.migrated_to_iasme_insurance and answered_insurance_questions %}
                function checkInsuranceTabAndPreviewGuide(withoutCheck) {
                    const insuranceGuideId = 120047;
                    if ($('li.insurance-tab.active').length || withoutCheck) {
                        window.userGuiding.previewGuide(insuranceGuideId, {checkHistory: true});
                    }
                }
            {% endif %}
        {% endflag %}
        $(document).ready(function() {
            var answersToggleButton = $('.view-answers');
            var answersBody = $('.questionnaire-review');
            var buttonTitle = $('.view-answers-title');
            answersToggleButton.click(function(e) {
                e.preventDefault();
                if (answersBody.is(":hidden")) {
                    answersBody.slideDown('slow', function () {
                        buttonTitle.text(gettext('Hide Answers'));
                    });
                } else {
                    answersBody.slideUp('slow', function () {
                        buttonTitle.text(gettext('View Answers'));
                    });
                }
            });
            {% flag 'user-guiding' %}
                {% if certification.survey.migrated_to_iasme_insurance and answered_insurance_questions %}
                    const insuranceTabLink = $('a.insurance-tab');
                    insuranceTabLink.click(() => {
                        checkInsuranceTabAndPreviewGuide(true);
                    })
                    // instantiate insurance guide if we start on insurance tab
                    checkInsuranceTabAndPreviewGuide();
                {% endif %}
            {% endflag %}
            // Use class to select all such buttons as there are many of these buttons across all tabs.
            $('.import-answers').on('click', function() {
                $('#import-answers-modal').modal('show');
            });
            $('.export-answers').on('click', function() {
                $('#export-answers-modal-{{certification.id}}').modal('show');
            });
        });
    </script>
{% endblock extra-js %}
{% block extra-css %}
    <link href="{% static 'css/highlights-textarea.css' %}" rel="stylesheet" type="text/css">
    <style type="text/css">
        .select2-container--default {
            width: 100%!important;
        }
    </style>
{% endblock extra-css %}
<div class="row">
    {% include "partials/loading.html" with show=False %}
    {% include 'partials/certification/import_answers_modal.html' %}
    {% include 'partials/certification/export_answers_modal.html' with is_in_progress_page=True %}
    <div class="col-md-12">
        <div class="panel panel-primary">
            <div class="panel-heading {% if is_gdpr %} hidden {% endif %}">
                <div class="row">
                    {% if certification.survey.assessment.is_sent_back %}
                        <div class="col-lg-12 text-center">
                            {% trans 'Please re-answer all questions taking into account Assessment Notes and Clarifications' %}
                        </div>
                    {% else %}
                        {% if organisation.is_direct_customer and organisation.is_trial %}
                            <div class="col-md-2">{% trans 'Survey' %}</div>
                        {% else %}
                            <div class="col-md-2">{% trans 'Progress' %}</div>
                            <div class="col-md-10">
                                <div class="meter">
                                    <span style="width:{{ certification.progress_percentage }}%"><span class="meter-progress"><span style="margin-left: 5px">{{ certification.progress_percentage }}%</span></span></span>
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
{#    we don't need it for now since we are forbidden to prepopulate quesitons for our customers     #}
{#    not removing this completely because this may be allowed again, so will be easy to restore    #}
{#            <div id="review-questionnaire" style="display:{{certification.is_auto_generated|yesno:'block,none'}}">#}
{#                {% include 'dashboard/certification/review_auto_generated.html' %}#}
{#            </div>#}
            <div id="survey-questionnaire" class="panel-wrapper collapse in" aria-expanded="true">
                <div class="panel-body">
                    <form
                        id='submitForm'
                        enctype="multipart/form-data"
                        name="submitForm"
                        action="."
                        method="post"
                        {% if is_gdpr %}
                            data-disabled-form="true"
                        {% endif %}
                        class="form-horizontal form-bordered"
                        novalidate>
                        <input type="hidden" name="time" id="time">
                        {% csrf_token %}
                        <div id="exTab2">
                            {% if organisation.is_direct_customer and organisation.is_trial %}
                            {% else %}
                                <ul class="nav nav-tabs" {%if topics|length == 8%} style="margin: auto; width: 90%;" {%endif%}>
                                    {% for topic in topics %}
                                        <li 
                                            class="tab_li{{topic.order}} {% if request|current_topic:certification %}{% if request|current_topic:certification == topic.order %} active{% endif %}{% else %}{% if forloop.first %} active{% endif %}{% endif %}{% if topic.title == 'Insurance' %} insurance-tab{% endif %}" 
                                            questions="{{topic.order}}"
                                        >
                                            <a id="tab-link-{{ topic.order }}" href="#tab{{topic.order}}" data-toggle="tab" class="{% topic_status topic topics_status_structure 'complete_section' 'error' '' failed_questions %} {% if topic.title == 'Insurance' %}insurance-tab{% endif %}">{{topic.title|escape}}
                                                <div class="arrow-right"></div>
                                                <div class="arrow-left"></div>
                                            </a>
                                        </li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                            <div class="tab-content tw-border">
                                {% for topic in topics %}
                                    <div id="tab{{ topic.order }}" data-topic-order="{{ topic.order }}" class="tab-pane {% if request|current_topic:certification %}{% if request|current_topic:certification == topic.order %} active{% endif %}{% else %}{% if forloop.counter == 1 %} active{% endif %}{% endif %}">
                                        {% if topic.description and not organisation.is_direct_customer and not org_has_100k_insurance or not organisation.is_trial and not org_has_100k_insurance %}
                                            <div class="text-center p-b-m topic-description">
                                                <div>{{ topic.description|insurance_link:certification.get_insurer|safe }}</div>
                                            </div>
                                        {% endif %}
                                        {% for q in topic.questions.all %}
                                            {% if q.code == "cyber_insurance_assessment_scope" and organisation.is_aviva_customer and certification.type == CYBER_ESSENTIALS or q.code == "superscript_insurance_opt_in" and certification.type == CYBER_ESSENTIALS and org_has_100k_insurance %}
                                            {% else %}
                                            <div {% if not q|show_question:certification.survey.pk %}style="display: none" {% endif %} class="form-body question {% if q.parent %}has-parent {% if q.mandatory_choices_pks %}required-parent-choice{% endif %}{% endif %} {% if q.parent_2 %}has-parent-two{% endif %} {% if q.children.all.count %}has-children{% endif %} {% if q.children_2.all.count %}has-children-2{% endif %}" data-question-order="{{ q.pervade_num }}" data-question-code="{{ q.code|default:'' }}" data-question-pk="{{ q.pk }}" data-auto-answer="{{ q.auto_answer_questions.count }}" data-auto-answer-url="{{ auto_answer_url }}" data-type="{{ q.response_type }}" data-widget-type="{{ q.widget }}" {% if q.parent %} data-parent-question-order="{{ q.parent.order_str }}" data-parent-question-pk="{{ q.parent.pk }}" {% endif %} {% if q.parent_2 %} data-parent-question-order-two="{{ q.parent_2.order_str }}" data-parent-question-pk-two="{{ q.parent_2.pk }}" {% endif %} data-show-children-on="{{ q.show_children_on }}" data-required-parent-choice-pk="{{ q.mandatory_choices_pks }}" data-show-children-on-two="{{ q.show_children_on_2 }}">
                                                <div class="form-group {% if q.pk in failed_questions %}has-error{% endif %}">
                                                    <div class="col-md-12 q">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <label class="control-label abc tw-prose" style="width: 100%;text-align: left !important; margin-bottom: 5px" question-id="{{q.order_str}}">
                                                                        {{q.pervade_num}}: {{q.title|safe}}
                                                                    {% if q.pk in failed_questions %}
                                                                        {% if q.ranking == q.RANKING_MINOR %}
                                                                            <span style="text-transform: none" class="badge badge-pill badge-sm badge-warning p-r-20 p-l-20">{{ q.get_ranking_display }}</span>
                                                                        {% elif q.ranking == q.RANKING_MAJOR %}
                                                                            <span style="text-transform: none" class="badge badge-pill badge-sm badge-danger p-r-20 p-l-20">{{ q.get_ranking_display }}</span>
                                                                        {% elif q.ranking == q.RANKING_FAIL %}
                                                                            <span style="text-transform: none" class="badge badge-pill badge-sm badge-extra-danger p-r-20 p-l-20">{{ q.get_ranking_display }}</span>
                                                                        {% else %}
                                                                            <span style="text-transform: none" class="badge badge-pill badge-sm badge-grey p-r-20 p-l-20">{% trans "Info Only" %}</span>
                                                                        {% endif %}
                                                                    {% endif %}
                                                                </label>
                                                                {% if q.pervade_title == "A2.4 End User Devices" and certification.is_cyber_essentials and certification.version.version_number >= 2022 %}
                                                                    {% include "dashboard/certification/questions/cyber_essentials/2023/A2.4.html" %}
                                                                {% else %}
                                                                    {% if q.pk in failed_questions %}
                                                                        {% create_response_form q certification True %}
                                                                    {% else %}
                                                                        {% create_response_form q certification %}
                                                                    {% endif %}
                                                                {% endif %}
                                                                {% if q.not_accepted_assessor_notes|length %}
                                                                    <h5 style="color: #4c5aa0">{% trans 'Assessment Notes and Clarifications' %}:</h5>
                                                                    {% for note in q.not_accepted_assessor_notes %}
                                                                        <div class="assessor-cr" style="width: 100%; margin-left: 0">
                                                                            {{ note.text }} <i class="fa fa-comment assessor-note-icon pull-right" aria-hidden="true"></i><br/>
                                                                            <div class="text-muted m-t-15">{{ note.created }}</div>
                                                                        </div>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                {% if q.code == 'organisation_name' %}
                                                                    <br><br>
                                                                    <a class="btn btn-outline btn-primary" id="notcompany">{% trans 'Click here if your organisation is not registered with Companies House and to enter your name manually' %}</a>
                                                                {% endif %}
                                                                {% if q.allow_csv_text_fill %}
                                                                    <br>
                                                                    <label class="control-label">Import evidence from CSV</label><br>
                                                                    This will overwrite the text in the above field with the imported file.
                                                                    <input type="file" id="fileUpload-{{ q.pk }}" class="form-control" onchange="ParseCSV(this, '{{ q.pk }}')" />
                                                                {% endif %}
                                                            </div>
                                                            <div class="col-md-6">
                                                                {% if q.moreinfo %}
                                                                    <div class="certification-implementation-tips text-primary tw-prose">
                                                                        <label>{% trans 'Official guidance' %}: </label> {{q.moreinfo|safe}}
                                                                    </div>
                                                                {% endif %}
                                                                {% include "dashboard/certification/buttons/cybersmart_guidance.html" %}
                                                                {% include "dashboard/certification/buttons/get_devices.html" %}
                                                                {% if q.related_check %}
                                                                    {% include "dashboard/certification/buttons/get_automated_survey_answer.html" with check=q.related_check%}
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endif %}
                                        {% endfor %}
                                        {% if forloop.last %}
                                            <center>
                                                <div class="validate_question"></div>
                                                {% if failed_questions %}
                                                    {% if failed_only_required %}
                                                        <p class="text-danger">
                                                            {% trans "Please answer the required questions" %}
                                                        </p>
                                                    {% else %}
                                                        <p class="text-danger">{{ failed_message }}</p>
                                                    {% endif %}
                                                    <p class="text-danger">[{% trans 'Please review questions' %} {% for q in failed_questions_nums %}<a class="fix-response" href="" question-id="{{ q }}">{{ q }}</a>{% if not forloop.last %}, {% endif %}{% endfor %} {% trans 'to continue to certification' %}]</p>
                                                {% endif %}
                                                {% include "dashboard/certification/buttons/save_and_next_button.html" with is_last=True %}
                                            </center>
                                        {% else %}
                                            <center>
                                                <div class="validate_question"></div>
                                                {% if failed_questions %}
                                                    {% if failed_only_required %}
                                                        <p class="text-danger">
                                                            {% trans 'Please answer the required questions' %}
                                                        </p>
                                                    {% else %}
                                                        <p class="text-danger">{{ failed_message }}</p>
                                                    {% endif %}
                                                    <p class="text-danger">[{% trans 'Please review questions' %} {% for q in failed_questions_nums %}<a class="fix-response" href="" question-id="{{ q }}">{{ q }}</a>{% if not forloop.last %}, {% endif %}{% endfor %} {% trans 'to continue to certification' %}]</p>
                                                {% endif %}
                                                {% if not is_gdpr %}
                                                    {% include "dashboard/certification/buttons/save_and_next_button.html" %}
                                                {% endif %}
                                            </center>
                                        {% endif %}
                                        {% if certification.survey.assessment.is_sent_back %}
                                            <button class="btn btn-warning view-answers pull-left">
                                                <span class="btn-label"><i class="fa fa-navicon"></i></span>
                                                <span class="view-answers-title">{% trans 'View Answers' %}</span>
                                            </button>
                                        {% endif %}
                                    </div>
                                {% endfor %}
                                {% if is_gdpr %}
                                    <button class="btn btn-default btn-outline btn-rounded csv_export" data-topic-order="{{ topic.order }}"> {% trans 'Export CSV' %} </button>
                                {% endif %}
                                {% include "signup/payment/modal.html" %}
                                {% trans 'Cyber insurance provides crucial financial protection and support in the event of a cyber attack, helping your business recover quickly and minimising potential losses and disruptions.' as unsure_about_insurance_content %}
                                {% include "partials/modal_insurance_upsell.html" with modal_id="unsure_about_insurance_modal" modal_title=unsure_about_insurance_title modal_body=unsure_about_insurance_content %}
                                {% include "dashboard/certification/invite_team.html" %}
                                {% include "partials/plan_upgrade_v4_only_popup.html" %}
                            </div>
                        </div>
                    </form>
                    {% if certification.survey.assessment.is_sent_back %}
                        <div class="white-box questionnaire-review m-t-15" style="display: none">
                            {% include 'dashboard/certification/review/answers.html' with panel_color="white" live_counters=False warning_message=False failed_topics=False expand_collapse_topics=True certos_mode=False preview_button=is_iasme_cb_partner confirmation_on_save=True updated_in_pervade=True allow_edit=is_iasme_cb_partner review_topics=review_topics %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
