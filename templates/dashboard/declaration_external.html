{% extends 'base.html' %}

{% load static %}
{% load i18n %}
{% load divide_tags %}
{% load questionnaire %}
{% block head_title %}{% trans 'Declaration' %}{% endblock %}

{% block extra-css %}
    <link href="{% static 'css/select2.min.css' %}" rel="stylesheet" />
    <script src="{% static 'js/select2.min.js' %}"></script>
    <link href="{% static 'plugins/custom-select/custom-select.css' %}" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" type="text/css" href="{% static 'css/tooltipster.bundle.min.css' %}" />
    <script src="{% static 'js/tooltipster.bundle.min.js' %}"></script>
    <link href="{% static 'css/certification.css' %}" rel="stylesheet" />
    <style type="text/css">
        .ex-steps >li.current {
            background: #03a9f3;
        }
        .wrapper {
            position: relative;
            width: 100%;
            height: 200px;
            -moz-user-select: none;
            -webkit-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        .gdpr-info [class^=ti-]{
            vertical-align: middle;
            margin-right: 10px;
        }
        .gdpr-info [class^=ti-] span{
            font-size:1.25em;
        }
        .declaration-success, .declaration-fail{
            white-space: initial;
        }
    </style>
{% endblock %}

{% block main-content %}
    <div class="container-fluid">
        {% include "breadcrumbs/organisation-stuff.html" with page_name=certification.full_title %}
        <div class="row bg-title">
            <div class="col-lg-6 col-md-6 col-sm-6 col-xs-12">
                <h4 class="page-title">
                    {% if certification.type == CYBER_ESSENTIALS %}
                        <img class="icon icon-title" alt="CyberEssentials" src="{% static 'icons/page-title/CyberEssentials.svg' %}">
                    {% elif certification.type == CYBER_ESSENTIALS_PLUS %}
                        <img class="icon icon-title" alt="CyberEssentialsPlus" src="{% static 'icons/page-title/CyberEssentialsPlus.svg' %}">
                    {% elif certification.type == GDPR %}
                        <img class="icon icon-title" alt="GDPR" src="{% static 'icons/page-title/GDPR.svg' %}">
                    {% elif certification.type == CYBERSMART_COMPLETE %}
                        <img class="icon icon-title" alt="CYBERSMART_COMPLETE" src="{% static 'icons/page-title/Complete-temp.svg' %}">
                    {% endif %}
                    {% trans "Declaration" %}
                </h4>
            </div>
        </div>
        <div class="back-from-email">
            <a href="{{ certification.declaration_url }}" class="btn btn-default pull-left m-b-5">
                <span class="glyphicon glyphicon-arrow-left" aria-hidden="true"></span>
                {% trans 'Back' %}
            </a>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="white-box">
                    <div style="position: relative;">
                        {% include 'partials/access-restriction.html' with restricted_page='declaration-external' show=access_error %}
                        {% if survey_declaration.signature %}
                            <div class="text-center">
                                <p>{% trans 'This declaration has been approved. We will assess your submission and issue certification within 24 hours' %}!</p>
                                <p>{% trans 'You will be notified when this has been issued' %}</p>
                                {% include "dashboard/certification/images.html" %}
                            </div>
                            <div class="row m-t-20">
                                <div class="col-lg-2 col-lg-offset-5">
                                    <h5><b>{% trans "Name:" %}</b> {{ survey_declaration.declaration_name }}</h5>
                                    <h5><b>{% trans "Job Title:" %}</b> {{ survey_declaration.declaration_job }}</h5>
                                    <h5><b>{% trans "Date:" %}</b> {{ survey_declaration.declaration_date }}</h5>
                                </div>
                            </div>
                        {% else %}
                            <div class="not-approved">
                                <div class="row m-b-40">
                                    <div class="col-lg-6 col-lg-offset-3">
                                        <center><h3>{% trans 'Assessment Declaration' %}</h3></center>
                                        {% if certification.version.declaration_text %}
                                            <center><u>{% trans 'Date' %}: {% now "d M Y" %}</u></center>
                                            <div class="text-center">{% trans 'You have been requested to submit the following assessment, please do so using the signature pad on the right. If you have any questions, please contact us using the chat button in the bottom right' %}.</div>
                                        {% endif %}
                                        
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-6 col-xs-12">
                                        <form method="POST" action="{{ certification.external_declaration_url }}">
                                            {% csrf_token %}
                                            <div class="form-group">
                                                {% if show_insurance_questions %}
                                                    {% include 'partials/declaration/insurance_questions.html' %}
                                                {% endif %}
                                                {% if is_insurance_upgrade_question_required %}
                                                    {% include 'partials/declaration/insurance_upgrade_questions.html' %}
                                                {% endif %}
                                                <div id="signature_section" class="tw-pt-4 {% if show_insurance_questions %}tw-hidden{% endif %}">
                                                    <h4 class="tw-font-sans tw-font-extrabold">{% trans "Sign and submit" %}</h4>
                                                    {% if declaration_text %}
                                                        <p class="tw-mb-2">
                                                            {% autoescape off %}
                                                                {{ declaration_text|default:'' }}
                                                            {% endautoescape %}
                                                        </p>
                                                    {% endif %}
                                                    <p style="width: 40%; float: left; line-height: 38px;">{% trans 'Name' %}: </p>
                                                    <input class="form-control" id="id_first_name" maxlength="30" name="name" value="{{ request.user.get_full_name }}" required  pattern=".*\S+.*" style="float:left; width: 60%;">
                                                    <div class="clearfix"></div>
                                                    <p style="width: 40%; float: left; line-height: 38px;">{% trans 'Job title (Board level or equivalent)' %}: </p>
                                                    <input class="form-control" id="id_job" maxlength="30" name="job" value="{{ survey_declaration.declaration_job }}" required pattern=".*\S+.*" style="float:left; width: 60%;">
                                                    <div class="clearfix"></div>
                                                    <b>{% trans 'Digital Signature' %}:</b>
                                                    {% if survey_declaration.signature %}
                                                        <img name="img" src="{{survey_declaration.signature.url}}">
                                                        <button type="submit" name="action" class="btn btn-primary pull-right">{% trans 'Submit' %}</button>
                                                    {% else %}
                                                        <div class="signature_w has_signature" style="display: block;">
                                                            <div class="wrapper signature_d">
                                                                <canvas id="signature-pad" class="signature-pad" width=400 height=200></canvas>
                                                            </div>
                                                            <div>{% trans 'Please sign in the box above with your mouse' %}<br><br></div>
                                                            <input type="text" name="signature_name" required="" style="opacity: 0">
                                                            <a class="btn btn-default pull-left" id="clear">{% trans "Clear signature" %}</a>
                                                            <button type="submit" name="action" class="btn btn-primary pull-right">{% trans 'Submit' %}</button>
                                                        </div>
                                                    {% endif %}

                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="col-sm-6 col-xs-12">
                                        <div class="tw-flex tw-flex-row tw-items-center tw-justify-between">
                                            <h4 class="tw-font-sans tw-font-extrabold">{% trans 'Review your ' %}{{ certification.version.type }} {% trans 'assessment' %}</h4>
                                            <a href='data:text/plain;charset=utf-8,{{downloaded_survey}}' download="{{ certification.version.type|safe }} assessment.csv"
                                                class="tw-font-sans tw-font-medium tw-text-base" id="download_assessment_survey">
                                                <svg class="tw-h-5 tw-w-5 tw-fill-current">
                                                    <use href="{% static 'icons/icons-sprite.svg' %}#icon-download"></use>
                                                </svg>
                                                {% trans "Download" %}
                                            </a>
                                        </div>
                                        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                                            {% for topic in topics %}
                                                <div class="panel panel-default">
                                                    <div class="panel-heading" role="tab" id="headingTwo">
                                                        <h4 class="panel-title">
                                                            <a class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#content{{ topic.pk }}" aria-expanded="false" aria-controls="collapseTwo">
                                                                {{ topic.title }}
                                                            </a>
                                                        </h4>
                                                    </div>
                                                    <div id="content{{ topic.pk }}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingTwo">
                                                        <div class="panel-body">
                                                            {% for q in topic.questions.all %}
                                                                <div {% if not q|show_question:certification.survey.pk %}style="display: none" {% endif %} class="form-body question {% if q.parent %}has-parent {% if q.mandatory_choices_pks %}required-parent-choice{% endif %}{% endif %} {% if q.parent_2 %}has-parent-two{% endif %} {% if q.children.all.count %}has-children{% endif %} {% if q.children_2.all.count %}has-children-2{% endif %}" data-question-order="{{ q.order_str }}" data-question-code="{{ q.code|default:'' }}" data-question-pk="{{ q.pk }}" data-auto-answer="{{ q.auto_answer_questions.count }}" data-auto-answer-url="{{ certification.auto_answer_url }}" data-type="{{ q.response_type }}" data-widget-type="{{ q.widget }}" {% if q.parent %} data-parent-question-order="{{ q.parent.order_str }}" data-parent-question-pk="{{ q.parent.pk }}" {% endif %} {% if q.parent_2 %} data-parent-question-order-two="{{ q.parent_2.order_str }}" data-parent-question-pk-two="{{ q.parent_2.pk }}" {% endif %} data-show-children-on="{{ q.show_children_on }}" data-required-parent-choice-pk="{{ q.mandatory_choices_pks }}" data-show-children-on-two="{{ q.show_children_on_2 }}">
                                                                    <div class="form-group {% if q.order_str in failed_questions.keys|sort_list %} has-error {% endif %}">
                                                                        <div class="col-md-12 q">
                                                                            <div class="row">
                                                                                <div class="col-md-12">
                                                                                    <label class="control-label abc tw-prose" style="width: 100%;text-align: left !important; margin-bottom: 5px" question-id="{{q.order_str}}">{{ q.order_str }}. {{q.title|safe}}</label>
                                                                                    {% for response in q.responses.all %}
                                                                                        {% if not response.not_applicable %}
                                                                                            {% if response.value %}
                                                                                                <div class="declaration-success">
                                                                                                    {{ response.humanize_response_value }}
                                                                                                </div>
                                                                                            {% else %}
                                                                                                <div class="declaration-fail">
                                                                                                    {{ response.humanize_response_value }}
                                                                                                </div>
                                                                                            {% endif %}
                                                                                        {% else %}
                                                                                            {% if q.responses.all|length == 1 %}
                                                                                                {% if response.value_boolean == False %}
                                                                                                    <div class="declaration-fail">
                                                                                                        {% if response.get_value %}{{ response.get_value }}{% else %}{% trans "No" %}{% endif %}
                                                                                                    </div>
                                                                                                {% else %}
                                                                                                    <div class="declaration-fail">
                                                                                                        {% trans "No" %}
                                                                                                    </div>
                                                                                                {% endif %}
                                                                                            {% endif %}
                                                                                        {% endif %}
                                                                                    {% endfor %}
                                                                                    <hr>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            {% endfor %}
                                                        </div>
                                                    </div>
                                                </div>
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}



{% block extra-js %}
    <script src="{% static 'js/modules/partials/declaration.js' %}"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            setupSignaturePad()
        });
    </script>
    <script type="text/javascript">
        $(document).ready(function() {
            $('.tooltip').tooltipster({
                'maxWidth': 300,
                'theme': 'tooltipster-shadow'
            });
        });
    </script>
{% endblock %}
