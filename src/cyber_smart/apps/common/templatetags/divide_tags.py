import locale
import os

from django import template
from django.utils.http import urlsafe_base64_encode
from django.utils.safestring import mark_safe

from common.utils import waffle_flag_is_active_for_user as utils_waffle_flag_is_active_for_user

register = template.Library()


@register.filter
def divide(value, arg):
    if value == 0 or arg == 0 or not isinstance(value, int) or not isinstance(arg, int):
        return 0
    try:
        if int(value) <= int(arg):
            return int(round((float(value) / int(arg) * 100)))
        else:
            return int(round((float(arg) / int(value) * 100)))
    except (ValueError, ZeroDivisionError):
        return 0


@register.filter
def emailToDomain(value):
    if value is None:
        return ''
    try:
        return value.split("@").pop()
    except (ValueError, ZeroDivisionError):
        return None


@register.filter
def defaultIfNone(value, arg):
    if value is None or arg is None:
        return ''
    try:
        if value == 40:
            return arg.organisation.get_industry_display()
        elif value == 50:
            return arg.email.split("@").pop()
        elif value == 60:
            return arg.organisation.get_size_display()
        elif value == 110:
            try:
                if arg.installs.all():
                    _os = ''
                    for x in arg.installs.all():
                        _os += str(x.os) + ' machines, '
                    return _os
                else:
                    return ''
            except Exception:
                return ''
        elif value == 130:
            return arg.first_name + ' ' + arg.last_name
        elif value == 640:
            return arg.email
        else:
            return ''
    except (ValueError, ZeroDivisionError):
        return None


@register.filter
def button_not_applicable(q):
    button = ''
    if 'has-not-applicable' in q.title:
        class_success = ''
        disabled = ''
        not_applicable = True
        if q.not_applicable:
            class_success = 'not_applicable_success'
            not_applicable = False
            disabled = 'disabled'
        button = '<button class="btn notApplicable  ' + class_success + '" type="button" question-id="' + \
            str(q.order) + '" question="' + str(q.id) + '" not-applicable="' + \
            str(not_applicable) + '" ' + disabled + '>Not Applicable</button>'
    return button


@register.simple_tag
def percent_to_css_class(percent,
                         range_string="0-50:progress-bar-danger,51-99:progress-bar-warning,100:progress-bar-success"):
    """
    Returns specific css class if a percent included in css percent range.
    Example of range_string: "0-50:red,51-99:orange,100:green"
    :param percent: percent
    :type percent: int
    :param range_string: contains percent ranges and css classes corresponded to them
    :type range_string: str, unicode
    :return: css class
    :rtype: str, unicode
    """
    try:
        percent = int(percent)
    except (ValueError, TypeError):
        percent = 0
    parsed_range = {}
    for item in range_string.split(','):
        percent_range, css_class = item.split(":")
        range_split = percent_range.split("-")
        parsed_range[tuple(range(
            int(range_split[0]), int(range_split[1]) + 1
        ) if len(range_split) > 1 else range(
            int(range_split[0]), int(range_split[0]) + 1)
        )] = css_class
    for k in parsed_range:
        if percent in k:
            return parsed_range[k]


@register.simple_tag
def key_to_value(value, pattern_string):
    """
    Returns a value by a passed key.
    Example of pattern_string: "value1:red-class,value2:orange-class,value3:green-class"
    :param value: value
    :type value: str
    :param pattern_string: contains keys and corresponded to them values
    :type pattern_string: str, unicode
    :return: css class
    :rtype: str, unicode
    """

    parsed_pattern = {}
    for item in pattern_string.split(','):
        parsed_pattern[item.split(':')[0]] = item.split(':')[1]
    return parsed_pattern.get(value)


@register.filter()
def currency(value):
    """
    Converts raw decimal value in cents to the UK pounds.
    :param value: cents
    :type value: decimal
    :return: pounds
    :rtype: str
    """
    try:
        locale.setlocale(locale.LC_ALL, 'en_GB.UTF-8')
        result = locale.currency(value, grouping=True)
    except Exception:
        locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        result = locale.currency(value, grouping=True)
    return result


@register.filter
def percentage(part, whole):
    """
    Returns part percentage to whole.
    :param part: part of whole
    :type part: int
    :param whole: whole number
    :type whole: int
    :return: percentage
    :rtype: int
    """
    if part and whole:
        return int(100 * float(part) / float(whole))
    else:
        return 0


@register.filter
def get_item(dictionary, key):
    """
    Gets value from dict
    :param dictionary: dictionary
    :param key: dict key
    :return: dict value
    """
    return dictionary.get(key)


@register.filter
def build_key_install_check_status(value1, value2) -> str:
    """
    Builds the key for the dict checks in check-report.html
    """
    return '_'.join([str(value1), str(value2)])


@register.filter
def filename(value):
    """
    Returns basename of full-path. Return error if file not found
    """
    try:
        return os.path.basename(value.name)
    except Exception:
        return 'Unknown'


@register.filter
def sort_list(value):
    """
    Returns sorted list.
    """
    value = list(map(int, value))
    value.sort()
    return value


@register.filter
def subtract(value, arg):
    return value - arg


@register.filter
def url_encode(value):
    if value:
        return urlsafe_base64_encode(value.encode('utf-8', 'ignore'))
    else:
        return ''


@register.simple_tag
def call_method(obj, method_name, *args, **kwargs):
    method = getattr(obj, method_name)
    return method(*args, **kwargs)


@register.simple_tag
def true_false_class(obj, true_class, false_class):
    """
    Takes boolean value and returns false or true css class passed as argument
    :param obj: any boolean value
    :type obj: bool
    :param true_class: css class that will be returned in case of obj is True
    :type true_class: unicode
    :param false_class: css class that will be returned in case of obj is False
    :type false_class: unicode
    :return: css class
    :rtype: unicode
    """
    return true_class if obj else false_class


@register.filter()
def nbsp(value):
    """
    Replaces usual spaces in string by non breaking spaces. "some words" --> "some&nbsp;words"
    :param value: string value
    :param value: str or unicode
    """
    if value:
        return mark_safe("<br/>".join("&nbsp;".join(value.split(' ')).split('\n')))
    else:
        return None


@register.filter()
def multiply(value, arg):
    return float(value) * float(arg)

@register.simple_tag
def waffle_flag_is_active_for_user(flag_name, user):
    return utils_waffle_flag_is_active_for_user(user, flag_name)
