from django.test import TestCase

from appusers.models.factories import AppInstallFactory
from opswat.factories import OpswatOperatingSystemFactory
from opswat.opswat_operating_system import OpswatOperatingSystem


class OpswatOperatingSystemModelTest(TestCase):
    """Test cases for OpswatOperatingSystem model."""

    def setUp(self):
        self.app_install = AppInstallFactory()

    def test_has_known_os_id_with_unknown_os(self):
        """Test has_known_os_id returns False for unknown OS (os_id=-1)."""
        os_record = OpswatOperatingSystemFactory(
            app_install=self.app_install,
            os_id=-1,  # Unknown OS
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS
        )
        self.assertFalse(
            os_record.has_known_os_id(),
            "has_known_os_id should return False for unknown OS (os_id=-1)"
        )

    def test_has_known_os_id_with_none(self):
        """Test has_known_os_id returns False when os_id is None."""
        # Since os_id doesn't allow NULL in the database, we'll test the logic directly
        os_record = OpswatOperatingSystemFactory(
            app_install=self.app_install,
            os_id=0,  # Create with valid ID first
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS
        )
        # Now manually set os_id to None to test the logic
        os_record.os_id = None
        self.assertFalse(
            os_record.has_known_os_id(),
            "has_known_os_id should return False when os_id is None"
        )
