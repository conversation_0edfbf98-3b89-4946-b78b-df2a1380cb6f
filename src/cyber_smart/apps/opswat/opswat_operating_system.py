import re
from typing import List, Optional, Tuple

from django.db import models
from model_utils.models import TimeStampedModel


class OpswatOperatingSystem(TimeStampedModel):
    """
    A model to store OPSWAT operating system information for an app install.
    The data is obtained through OPSWAT GetOSInfo SDK call.
    """
    OS_TYPE_WINDOWS = 1
    OS_TYPE_LINUX = 2
    OS_TYPE_MAC = 4

    OS_TYPE_CHOICES = [
        (OS_TYPE_WINDOWS, 'Windows'),
        (OS_TYPE_LINUX, 'Linux'),
        (OS_TYPE_MAC, 'Mac'),
    ]

    app_install = models.OneToOneField(
        "appusers.AppInstall",
        verbose_name="App Install",
        on_delete=models.CASCADE,
        related_name="opswat_os",
        help_text="The app install this OS information belongs to."
    )
    os_id = models.IntegerField(
        verbose_name="OS ID",
        help_text="The operating system ID. These are unique IDs that identify supported OS versions across different platforms. "
                  "A value of -1 indicates an unknown OS where the system could not determine the specific OS version. "
                  "This is likely when the OS is so new that OPSWAT has not yet added it to their database. "
                  "When os_id is -1, OS filtering is skipped during patch matching. Coming from OPSWAT GetOSInfo SDK call."
    )
    os_type = models.IntegerField(
        verbose_name="OS Type",
        choices=OS_TYPE_CHOICES,
        help_text="The operating system type: 1 - Windows, 2 - Linux, 4 - Mac. Coming from OPSWAT GetOSInfo SDK call."
    )
    architecture = models.CharField(
        verbose_name="Architecture",
        max_length=50,
        help_text="The operating system architecture: '32-bit', '64-bit'. Coming from OPSWAT GetOSInfo SDK call."
    )

    class Meta:
        verbose_name = "OPSWAT Operating System"
        verbose_name_plural = "OPSWAT Operating Systems"
        indexes = [
            models.Index(fields=["os_id"]),
        ]

    def has_known_os_id(self) -> bool:
        """
        Check if this OS record has valid OS information for filtering.

        Returns False if:
        - os_id is None
        - os_id is -1 (unknown OS)

        Returns:
            bool: True if OS info is valid for filtering, False otherwise
        """
        return self.os_id is not None and self.os_id != -1

    def __str__(self) -> str:
        """
        Return the string representation of the OPSWAT operating system.
        """
        os_type_display = self.get_os_type_display() if hasattr(self, 'get_os_type_display') else self.os_type
        return f"{self.app_install} - OS ID: {self.os_id}, Type: {os_type_display} ({self.architecture or 'No architecture'})"


def parse_os_range(range_str: str) -> List[Tuple[Optional[int], Optional[int]]]:
    """
    Parse OS range string format into list of (min, max) tuples.

    The OS range format supports:
    - Single range: "[1,10]" -> [(1, 10)]
    - Multiple ranges: "[1,10],[20,30]" -> [(1, 10), (20, 30)]
    - Special value -2 means no limit: "[-2,58]" -> [(None, 58)], "[59,-2]" -> [(59, None)]
    - Single value ranges: "[66,66]" -> [(66, 66)]

    NOTE: Ranges with decimal numbers (e.g., "[10.14, 20]") are skipped. These likely
    represent OS version numbers but are not currently supported.

    Args:
        range_str: String containing OS ranges in format "[min,max]"

    Returns:
        List of tuples where each tuple is (min_id, max_id).
        None values indicate no limit in that direction.

    Examples:
        >>> parse_os_range("[1,10]")
        [(1, 10)]
        >>> parse_os_range("[-2,58], [67, 70]")
        [(None, 58), (67, 70)]
        >>> parse_os_range("[59,-2]")
        [(59, None)]
        >>> parse_os_range("[-2, 42], [66, 66], [75,75]")
        [(None, 42), (66, 66), (75, 75)]
        >>> parse_os_range("[-2, 10.14]")  # Decimal numbers are skipped
        []
    """
    if not range_str or not range_str.strip():
        return []

    ranges = []
    # Pattern to match [number,number] where numbers can be negative, with optional spaces
    pattern = r'\[\s*(-?[\d.]+)\s*,\s*(-?[\d.]+)\s*\]'

    for match in re.finditer(pattern, range_str):
        min_str = match.group(1)
        max_str = match.group(2)

        # TODO: Improve after getting OPSWAT response on how to best treat decimal OS IDs.
        # Currently skipping ranges with decimal numbers. These likely correspond to macOS
        # and Windows release versions, but those are not currently accessible in any other
        # OPSWAT JSON files.
        # Examples found: "[-2, 10.14]", "[10.0.19045,61]", "[10.0.22631,78]"
        if '.' in min_str or '.' in max_str:
            continue

        try:
            min_val = int(min_str)
            max_val = int(max_str)
        except ValueError:
            # Skip invalid integer values
            continue

        # -2 means no limit
        if min_val == -2:
            min_val = None
        if max_val == -2:
            max_val = None

        ranges.append((min_val, max_val))

    return ranges


def is_os_id_in_ranges(os_id: Optional[int], ranges: List[Tuple[Optional[int], Optional[int]]]) -> bool:
    """
    Check if an OS ID falls within any of the given ranges.

    Args:
        os_id: The OS ID to check
        ranges: List of (min, max) tuples representing allowed ranges

    Returns:
        True if os_id is within any range, False otherwise
    """
    if os_id is None or not ranges:
        return False

    for min_val, max_val in ranges:
        # Handle None values (no limit)
        min_check = True if min_val is None else min_val <= os_id
        max_check = True if max_val is None else os_id <= max_val

        if min_check and max_check:
            return True

    return False


def is_installer_os_compatible(installer, os_id: int) -> bool:
    """
    Check if an installer is compatible with the given OS ID based on os_allow and os_deny fields.

    Args:
        installer: The installer to check (OpswatProductPatchInstaller instance)
        os_id: The OS ID to check against (must not be None)

    Returns:
        True if the installer is compatible with the OS, False otherwise

    Logic:
        - If os_deny is set and os_id is in deny ranges, return False
        - If os_allow is set and os_id is NOT in allow ranges, return False
        - Otherwise, return True
    """
    # Check os_deny first - if OS is in deny list, it's not compatible
    if installer.os_deny:
        deny_ranges = parse_os_range(installer.os_deny)
        if is_os_id_in_ranges(os_id, deny_ranges):
            return False

    # Check os_allow - if set, OS must be in allow list
    if installer.os_allow:
        allow_ranges = parse_os_range(installer.os_allow)
        if not is_os_id_in_ranges(os_id, allow_ranges):
            return False

    # If we get here, the installer is compatible
    return True
