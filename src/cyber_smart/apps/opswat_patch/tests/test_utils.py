from datetime import timed<PERSON><PERSON>
from unittest.mock import Mock

from appusers.models import InstalledSoftwareOrganisationSummary
from appusers.models.factories import AppInstallFactory, AppUserFactory
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual
from appusers.tests.test_materialized_views import (
    AI2_EDGE,
    AI2_SAFARI,
    UniquenessAndFrequencyViewTestCase,
)
from django.test import TestCase
from django.utils import timezone
from freezegun import freeze_time
from opswat.factories import (
    InstalledProductFactory,
    InstalledProductVersionFactory,
    OpswatOperatingSystemFactory,
    ProductFactory,
    ProductSignatureFactory,
    ProductVendorFactory,
    ProductVersionFactory,
)
from opswat.models import Product
from opswat.opswat_operating_system import OpswatOperatingSystem
from opswat_patch.factories import (
    OpswatPatchInstallerFactory,
    OpswatProductPatchInstallerFactory,
    OpswatScheduledProductInstallerFactory,
)
from opswat.opswat_operating_system import (
    is_installer_os_compatible,
    is_os_id_in_ranges,
    parse_os_range,
)
from opswat_patch.models import OpswatProductPatchInstaller
from opswat_patch.utils import enrich_installed_software_with_installers
from organisations.factories import OrganisationFactory


class ParseOsRangeTests(TestCase):
    """Test cases for OS range parsing functionality."""

    def test_parse_single_range(self):
        """Test parsing a single OS range."""
        result = parse_os_range("[1,10]")
        self.assertEqual(result, [(1, 10)])

    def test_parse_multiple_ranges(self):
        """Test parsing multiple OS ranges."""
        result = parse_os_range("[1,10],[20,30]")
        self.assertEqual(result, [(1, 10), (20, 30)])

    def test_parse_negative_range(self):
        """Test parsing range with negative values."""
        result = parse_os_range("[-10,58]")
        self.assertEqual(result, [(-10, 58)])

    def test_parse_empty_string(self):
        """Test parsing empty string returns empty list."""
        self.assertEqual(parse_os_range(""), [])
        self.assertEqual(parse_os_range("   "), [])

    def test_parse_invalid_format(self):
        """Test parsing invalid format returns empty list."""
        result = parse_os_range("invalid")
        self.assertEqual(result, [])

    def test_parse_mixed_ranges(self):
        """Test parsing mixed positive and negative ranges."""
        result = parse_os_range("[-10,-5],[0,5],[10,20]")
        self.assertEqual(result, [(-10, -5), (0, 5), (10, 20)])

    def test_parse_no_limit_ranges(self):
        """Test parsing ranges with -2 (no limit) values."""
        # -2 at start means no lower limit
        result = parse_os_range("[-2,58]")
        self.assertEqual(result, [(None, 58)])

        # -2 at end means no upper limit
        result = parse_os_range("[59,-2]")
        self.assertEqual(result, [(59, None)])

        # Multiple ranges with -2
        result = parse_os_range("[-2,58], [67, 70]")
        self.assertEqual(result, [(None, 58), (67, 70)])

        # Complex case with single values
        result = parse_os_range("[-2, 42], [66, 66], [75,75]")
        self.assertEqual(result, [(None, 42), (66, 66), (75, 75)])

    def test_parse_decimal_ranges_are_skipped(self):
        """Test that ranges with decimal numbers are skipped."""
        # Single decimal range should be skipped
        result = parse_os_range("[-2, 10.14]")
        self.assertEqual(result, [])

        # Mixed decimal and integer ranges - only integer ranges kept
        result = parse_os_range("[10.0.19045,61],[63,65],[71,74]")
        self.assertEqual(result, [(63, 65), (71, 74)])

        # Complex case from real data
        result = parse_os_range("[10.0.19045,61],[63,65],[71,74],[10.0.22631,78],[80,-2]")
        self.assertEqual(result, [(63, 65), (71, 74), (80, None)])

        # Decimal in first value
        result = parse_os_range("[10.5, 20]")
        self.assertEqual(result, [])

        # Decimal in second value
        result = parse_os_range("[10, 20.5]")
        self.assertEqual(result, [])


class IsOsIdInRangesTests(TestCase):
    """Test cases for OS ID range checking."""

    def test_os_id_in_single_range(self):
        """Test OS ID within a single range."""
        ranges = [(1, 10)]
        self.assertTrue(is_os_id_in_ranges(5, ranges))
        self.assertTrue(is_os_id_in_ranges(1, ranges))
        self.assertTrue(is_os_id_in_ranges(10, ranges))
        self.assertFalse(is_os_id_in_ranges(0, ranges))
        self.assertFalse(is_os_id_in_ranges(11, ranges))

    def test_os_id_in_multiple_ranges(self):
        """Test OS ID within multiple ranges."""
        ranges = [(1, 10), (20, 30)]
        self.assertTrue(is_os_id_in_ranges(5, ranges))
        self.assertTrue(is_os_id_in_ranges(25, ranges))
        self.assertFalse(is_os_id_in_ranges(15, ranges))

    def test_os_id_with_negative_range(self):
        """Test OS ID with negative range."""
        ranges = [(-2, 58)]
        self.assertTrue(is_os_id_in_ranges(-1, ranges))
        self.assertTrue(is_os_id_in_ranges(0, ranges))
        self.assertTrue(is_os_id_in_ranges(58, ranges))
        self.assertFalse(is_os_id_in_ranges(-3, ranges))
        self.assertFalse(is_os_id_in_ranges(59, ranges))

    def test_empty_os_id(self):
        """Test empty or None OS ID."""
        ranges = [(1, 10)]
        self.assertFalse(is_os_id_in_ranges(None, ranges))

    def test_empty_ranges(self):
        """Test empty ranges list."""
        self.assertFalse(is_os_id_in_ranges(5, []))

    def test_os_id_with_no_limit_ranges(self):
        """Test OS ID with ranges containing None (no limit) values."""
        # No lower limit
        ranges = [(None, 58)]
        self.assertTrue(is_os_id_in_ranges(0, ranges))
        self.assertTrue(is_os_id_in_ranges(-100, ranges))
        self.assertTrue(is_os_id_in_ranges(58, ranges))
        self.assertFalse(is_os_id_in_ranges(59, ranges))

        # No upper limit
        ranges = [(59, None)]
        self.assertTrue(is_os_id_in_ranges(59, ranges))
        self.assertTrue(is_os_id_in_ranges(1000, ranges))
        self.assertFalse(is_os_id_in_ranges(58, ranges))

        # Mixed ranges
        ranges = [(None, 42), (66, 66), (75, None)]
        self.assertTrue(is_os_id_in_ranges(0, ranges))  # In first range
        self.assertTrue(is_os_id_in_ranges(42, ranges))  # In first range
        self.assertFalse(is_os_id_in_ranges(43, ranges))  # Not in any range
        self.assertTrue(is_os_id_in_ranges(66, ranges))  # In second range
        self.assertFalse(is_os_id_in_ranges(67, ranges))  # Not in any range
        self.assertTrue(is_os_id_in_ranges(75, ranges))  # In third range
        self.assertTrue(is_os_id_in_ranges(1000, ranges))  # In third range



class IsInstallerOsCompatibleTests(TestCase):
    """Test cases for is_installer_os_compatible function."""

    def test_os_deny_blocks_installer(self):
        """Test that OS ID in deny range blocks installer."""
        installer = Mock()
        installer.os_deny = "[-2,58]"
        installer.os_allow = ""

        self.assertFalse(is_installer_os_compatible(installer, 10))  # In deny range
        self.assertFalse(is_installer_os_compatible(installer, -1))  # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 100))  # Outside deny range

    def test_os_allow_restricts_installer(self):
        """Test that OS ID must be in allow range when set."""
        installer = Mock()
        installer.os_allow = "[1,20]"
        installer.os_deny = ""

        self.assertTrue(is_installer_os_compatible(installer, 10))   # In allow range
        self.assertFalse(is_installer_os_compatible(installer, 30))  # Outside allow range
        self.assertFalse(is_installer_os_compatible(installer, 0))   # Outside allow range

    def test_os_deny_takes_precedence(self):
        """Test that os_deny takes precedence over os_allow."""
        installer = Mock()
        installer.os_allow = "[1,100]"
        installer.os_deny = "[10,20]"

        self.assertTrue(is_installer_os_compatible(installer, 5))    # In allow, not in deny
        self.assertFalse(is_installer_os_compatible(installer, 15))  # In both allow and deny
        self.assertTrue(is_installer_os_compatible(installer, 50))   # In allow, not in deny
        self.assertFalse(is_installer_os_compatible(installer, 150)) # Outside allow

    def test_no_restrictions_allows_all(self):
        """Test that installer without OS restrictions allows all OS."""
        installer = Mock()
        installer.os_allow = ""
        installer.os_deny = ""

        self.assertTrue(is_installer_os_compatible(installer, 10))
        self.assertTrue(is_installer_os_compatible(installer, -5))
        self.assertTrue(is_installer_os_compatible(installer, 1000))

    def test_installer_with_no_limit_ranges(self):
        """Test installer compatibility with -2 (no limit) ranges."""
        installer = Mock()

        # Test os_deny with no lower limit
        installer.os_deny = "[-2,58], [67, 70]"
        installer.os_allow = ""
        self.assertFalse(is_installer_os_compatible(installer, 0))    # In deny range
        self.assertFalse(is_installer_os_compatible(installer, 58))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 59))    # Not in deny range
        self.assertFalse(is_installer_os_compatible(installer, 67))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 71))    # Not in deny range

        # Test os_allow with no upper limit
        installer.os_allow = "[59,-2]"
        installer.os_deny = ""
        self.assertFalse(is_installer_os_compatible(installer, 58))   # Outside allow range
        self.assertTrue(is_installer_os_compatible(installer, 59))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 1000))  # In allow range

        # Test complex case
        installer.os_deny = "[-2, 42], [66, 66], [75,75]"
        installer.os_allow = ""
        self.assertFalse(is_installer_os_compatible(installer, -100)) # In deny range
        self.assertFalse(is_installer_os_compatible(installer, 42))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 43))    # Not in deny range
        self.assertFalse(is_installer_os_compatible(installer, 66))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 67))    # Not in deny range
        self.assertFalse(is_installer_os_compatible(installer, 75))   # In deny range
        self.assertTrue(is_installer_os_compatible(installer, 76))    # Not in deny range

    def test_installer_with_decimal_ranges(self):
        """Test that installer handles decimal ranges by skipping them."""
        installer = Mock()

        # Test os_deny with decimal ranges - they should be skipped
        installer.os_deny = "[-2, 10.14]"
        installer.os_allow = ""
        # Since the decimal range is skipped, all OS IDs should be allowed
        self.assertTrue(is_installer_os_compatible(installer, 10))
        self.assertTrue(is_installer_os_compatible(installer, 11))

        # Test mixed decimal and integer ranges
        installer.os_allow = "[10.0.19045,61],[63,65],[71,74],[10.0.22631,78],[80,-2]"
        installer.os_deny = ""
        # Only the valid integer ranges should be used: [63,65], [71,74], [80,-2]
        self.assertFalse(is_installer_os_compatible(installer, 62))   # Not in any allow range
        self.assertTrue(is_installer_os_compatible(installer, 63))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 65))    # In allow range
        self.assertFalse(is_installer_os_compatible(installer, 66))   # Not in any allow range
        self.assertTrue(is_installer_os_compatible(installer, 71))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 74))    # In allow range
        self.assertFalse(is_installer_os_compatible(installer, 75))   # Not in any allow range
        self.assertTrue(is_installer_os_compatible(installer, 80))    # In allow range
        self.assertTrue(is_installer_os_compatible(installer, 1000))  # In allow range (no upper limit)


class EnrichPatchDetectedDateTests(TestCase):
    """
    This is the logic of the patch_detected:

    Patch version has to be higher than the currently installed version of the product on the CAP device.
    If this is not the case, the patch_detected_date is None.

    And then we have 2 cases:
    1. If the patch was available in CyberSmart dashbaord  before CAP was installed, then the patch_detected_date is the date_created of the ProductVersion on that CAP device.
    2. If the CAP product_version was installed before, and the patch was released after that, then the patch_detected_date is the date_modified of the PatchInstaller.
    """

    @freeze_time("2020-01-01")
    def setUp(self):
        self.app_install = AppInstallFactory()
        self.product_version = ProductVersionFactory(
            raw_version="1.0.0",
            major=1,
            minor=0,
            patch=0
        )
        self.installed_product = InstalledProductFactory(app_install=self.app_install)

    @freeze_time("2020-02-01")
    def test_case1_patch_available_before_product_installed(self):
        """Test case 1: Patch available before install -> date should be install date."""
        with freeze_time("2020-01-01"):
            # Create patch installer with modification date before product installation
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )

        with freeze_time("2020-02-01"):
            installed_version = InstalledProductVersionFactory(
                installed_product=self.installed_product,
                product_version=self.product_version
            )

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)

        enriched_results = enrich_installed_software_with_installers(installed_software_list, self.app_install)
        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        self.assertEqual(enriched_item.app_install_product_installer['patch_detected_date'], installed_version.created)

    @freeze_time("2020-02-01")
    def test_case2_product_installed_before_patch_available(self):
        """Test case 2: Install before patch available -> date should be patch modified date."""
        InstalledProductVersionFactory(
            installed_product=self.installed_product,
            product_version=self.product_version
        )

        # Create patch installer with modification date after product installation (July 1st)
        with freeze_time("2020-07-01"):
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            # Retrieve the installer instance to get its modified date
            opswat_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)
        self.assertEqual(installed_software_list.count(), 1)

        # Get the detection date via enrichment
        enriched_results = enrich_installed_software_with_installers(installed_software_list, self.app_install)
        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should be the installer modification date
        self.assertEqual(enriched_item.app_install_product_installer['patch_detected_date'], opswat_installer.modified)

    @freeze_time("2020-06-15")
    def test_returns_none_if_patch_version_not_greater(self):
        """Test patch_detected_date is None if patch version is not greater."""
        # Create the installed product version
        InstalledProductVersionFactory(
            installed_product=self.installed_product,
            product_version=self.product_version
        )

        patch_installer = OpswatPatchInstallerFactory(latest_version="1.0.0")  # Same version
        OpswatProductPatchInstallerFactory(product=self.product_version.product, patch_installer=patch_installer)

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)
        enriched_results = enrich_installed_software_with_installers(installed_software_list, self.app_install)
        enriched_item = enriched_results[0]
        self.assertFalse(hasattr(enriched_item, "app_install_product_installer"))

    @freeze_time("2020-06-15")
    def test_returns_none_if_parse_version_fails(self):
        """Test patch_detected_date is None if version parsing fails."""
        # Create the installed product version
        InstalledProductVersionFactory(
            installed_product=self.installed_product,
            product_version=self.product_version
        )

        patch_installer = OpswatPatchInstallerFactory(latest_version="invalid")
        # Ensure the installer is linked
        OpswatProductPatchInstallerFactory(product=self.product_version.product, patch_installer=patch_installer)

        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software_list = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install)
        enriched_results = enrich_installed_software_with_installers(installed_software_list, self.app_install)
        enriched_item = enriched_results[0]
        self.assertFalse(hasattr(enriched_item, "app_install_product_installer"))


class EnrichInstalledSoftwareWithInstallersTests(UniquenessAndFrequencyViewTestCase):
    """
    Tests for enrich_installed_software_with_installers utility function.
    """

    def setUp(self):
        """Set up test data by calling parent setUp and adding OPSWAT patch installers."""
        super().setUp()

        InstalledSoftwareAppInstallIndividual.refresh()
        self.installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install_2)
        self.assertEqual(self.installed_software.count(), 3)
        # Ensure the patch installer has a higher version than installed Edge (100.0.0.0)
        patch_installer = OpswatPatchInstallerFactory(latest_version="101.0.0.0")
        self.product_patch_installer = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            title="Edge Installer",
            patch_installer=patch_installer
        )
        OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=self.product_patch_installer,
            app_install=self.app_install_2
        )

    def test_enrich_installed_software_with_installers(self):
        """Test that installers are correctly added to installed software."""

        result = enrich_installed_software_with_installers(self.installed_software, self.app_install_2)

        self.assertEqual(len(result), self.installed_software.count())

        edge_sw = next((sw for sw in result if AI2_EDGE.lower() in sw.product.lower()), None)
        safari_sw = next((sw for sw in result if AI2_SAFARI.lower() in sw.product.lower()), None)

        # Verify both items are found
        self.assertIsNotNone(edge_sw, "Edge software not found in result")
        self.assertIsNotNone(safari_sw, "Safari software not found in result")

        # Verify Edge is enriched with installer and scheduled
        self.assertTrue(hasattr(edge_sw, 'app_install_product_installer'))
        self.assertEqual(edge_sw.app_install_product_installer['app_install_product_installer_id'], self.product_patch_installer.id)
        self.assertEqual(edge_sw.app_install_product_installer['title'], "Edge Installer")
        self.assertEqual(edge_sw.app_install_product_installer['is_scheduled'], True)
        self.assertEqual(edge_sw.app_install_product_installer['installer_version'], self.product_patch_installer.patch_installer.latest_version)
        self.assertEqual(edge_sw.app_install_product_installer['patch_detected_date'], self.product_patch_installer.modified)

    def test_no_matching_installers(self):
        """Test behavior when no matching installers are found."""
        OpswatProductPatchInstaller.objects.all().delete()

        result = enrich_installed_software_with_installers(self.installed_software, self.app_install_2)

        # All items should be returned but none should be enriched
        self.assertEqual(len(result), self.installed_software.count())

        # Verify no items have the app_install_product_installer attribute
        for item in result:
            self.assertFalse(hasattr(item, 'app_install_product_installer'))

    def test_fallback_when_no_signature_match(self):
        """Test that when no installer matches the signature for TeamViewer, it falls back to the first installer."""
        # Create TeamViewer vendor and product
        teamviewer_vendor = ProductVendorFactory(
            name="TeamViewer GmbH",
            opswat_id="152"
        )
        teamviewer_product = ProductFactory(
            name="TeamViewer",
            vendor=teamviewer_vendor,
            opswat_id="173"
        )

        # Create TeamViewer version
        teamviewer_version = ProductVersionFactory(
            product=teamviewer_product,
            raw_version="11.0.0",
            major=11,
            minor=0,
            patch=0
        )

        # Create TeamViewer signatures
        signature1 = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 11",
            opswat_id="3342",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        signature2 = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 12",
            opswat_id="3343",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        signature3 = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 13",
            opswat_id="3344",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        # Create app install and installed product with signature3
        app_install = AppInstallFactory()
        installed_product = InstalledProductFactory(app_install=app_install)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=teamviewer_version,
            signature=signature3
        )

        # Create patch installers for different TeamViewer versions
        patch_installer1 = OpswatPatchInstallerFactory(
            product_name="TeamViewer 11",
            latest_version="11.0.59"
        )
        patch_installer2 = OpswatPatchInstallerFactory(
            product_name="TeamViewer 12",
            latest_version="12.0.0"
        )

        # Create product patch installers with signatures 1 and 2 (but not 3)
        product_patch_installer1 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 11 Installer",
            patch_installer=patch_installer1
        )
        product_patch_installer1.signatures.add(signature1)

        product_patch_installer2 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 12 Installer",
            patch_installer=patch_installer2
        )
        product_patch_installer2.signatures.add(signature2)

        # Refresh the materialized view
        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=app_install)

        # Get the enriched software
        result = enrich_installed_software_with_installers(installed_software, app_install)

        # Find the TeamViewer software in the result
        teamviewer_sw = next((sw for sw in result if "teamviewer" in sw.product.lower()), None)
        self.assertIsNotNone(teamviewer_sw, "TeamViewer software not found in result")

        # Verify TeamViewer is enriched with the most recently created installer as fallback
        self.assertTrue(hasattr(teamviewer_sw, 'app_install_product_installer'))

        expected_installer = OpswatProductPatchInstaller.objects.filter(
            product=teamviewer_product
        ).order_by('-patch_installer__created').first()

        self.assertEqual(teamviewer_sw.app_install_product_installer['app_install_product_installer_id'], expected_installer.id)
        self.assertEqual(teamviewer_sw.app_install_product_installer['title'], "TeamViewer 12 Installer")
        self.assertEqual(teamviewer_sw.app_install_product_installer['installer_version'], "12.0.0")

    def test_teamviewer_signature_based_installer_selection(self):
        """Test that TeamViewer installers are selected based on signature when available."""
        # Create TeamViewer vendor and product
        teamviewer_vendor = ProductVendorFactory(
            name="TeamViewer GmbH",
            opswat_id="152"
        )
        teamviewer_product = ProductFactory(
            name="TeamViewer",
            vendor=teamviewer_vendor,
            opswat_id="173"
        )

        # Create TeamViewer version
        teamviewer_version = ProductVersionFactory(
            product=teamviewer_product,
            raw_version="11.0.0",
            major=11,
            minor=0,
            patch=0
        )

        # Create TeamViewer signatures based on the provided data
        teamviewer_signature = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer",
            opswat_id="177",
            support_3rd_party_patch=False
        )

        teamviewer_11_signature = ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 11",
            opswat_id="3342",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        ProductSignatureFactory(
            product=teamviewer_product,
            name="TeamViewer 12",
            opswat_id="3343",
            support_3rd_party_patch=True,
            fresh_installable=1
        )

        # Create app install and installed product
        app_install = AppInstallFactory()
        installed_product = InstalledProductFactory(app_install=app_install)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=teamviewer_version,
            signature=teamviewer_11_signature  # Associate with TeamViewer 11 signature
        )

        # Create patch installers for different TeamViewer versions
        patch_installer_v11 = OpswatPatchInstallerFactory(
            product_name="TeamViewer 11",
            latest_version="11.0.59"
        )
        patch_installer_v15 = OpswatPatchInstallerFactory(
            product_name="TeamViewer",
            latest_version="15.0.0"
        )

        # Create product patch installers with different signatures
        product_patch_installer_v11 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 11 Installer",
            patch_installer=patch_installer_v11
        )
        product_patch_installer_v11.signatures.add(teamviewer_11_signature)

        product_patch_installer_v15 = OpswatProductPatchInstallerFactory(
            product=teamviewer_product,
            title="TeamViewer 15 Installer",
            patch_installer=patch_installer_v15
        )
        product_patch_installer_v15.signatures.add(teamviewer_signature)

        # Refresh the materialized view
        InstalledSoftwareAppInstallIndividual.refresh()
        installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=app_install)

        # Get the enriched software
        result = enrich_installed_software_with_installers(installed_software, app_install)

        # Find the TeamViewer software in the result
        teamviewer_sw = next((sw for sw in result if "teamviewer" in sw.product.lower()), None)
        self.assertIsNotNone(teamviewer_sw, "TeamViewer software not found in result")

        # Verify TeamViewer is enriched with the correct installer based on signature (v11)
        self.assertTrue(hasattr(teamviewer_sw, 'app_install_product_installer'))
        self.assertEqual(teamviewer_sw.app_install_product_installer['app_install_product_installer_id'], product_patch_installer_v11.id)
        self.assertEqual(teamviewer_sw.app_install_product_installer['title'], "TeamViewer 11 Installer")
        self.assertEqual(teamviewer_sw.app_install_product_installer['installer_version'], "11.0.59")


class EnrichInstalledSoftwareOrganisationLevelTests(TestCase):
    """
    Tests for enrich_installed_software_with_installers at organisation level.
    """

    @freeze_time("2020-01-01")
    def setUp(self):
        """Set up test data for organisation-level testing."""
        self.organisation = OrganisationFactory()
        self.user1 = AppUserFactory(organisation=self.organisation)
        self.user2 = AppUserFactory(organisation=self.organisation)
        self.app_install1 = AppInstallFactory(app_user=self.user1)
        self.app_install2 = AppInstallFactory(app_user=self.user2)

        # Create a product version
        self.product_version = ProductVersionFactory(
            raw_version="1.0.0",
            major=1,
            minor=0,
            patch=0
        )

        # Create installed products for both app installs
        self.installed_product1 = InstalledProductFactory(app_install=self.app_install1)
        self.installed_product2 = InstalledProductFactory(app_install=self.app_install2)


    @freeze_time("2020-02-01")
    def test_organisation_level_patch_detected_when_patch_released_after_install(self):
        """Test organisation-level patch_detected when patch is released after installation."""
        # Install the product on both devices
        with freeze_time("2020-01-15"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product1,
                product_version=self.product_version
            )

        with freeze_time("2020-01-20"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product2,
                product_version=self.product_version
            )

        # Create patch installer after both installs
        with freeze_time("2020-01-25"):
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            opswat_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )

        # Refresh materialized view
        InstalledSoftwareOrganisationSummary.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationSummary.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich without app_install
        enriched_results = enrich_installed_software_with_installers(installed_software)

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should use the patch installer modified date (Jan 25)
        self.assertEqual(
            enriched_item.app_install_product_installer['patch_detected_date'],
            opswat_installer.modified
        )

    def test_organisation_level_no_patch_detected_when_no_installs(self):
        """Test that patch_detected is None when there are no installed product versions."""
        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
        OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer
        )

        # Don't create any InstalledProductVersion records
        # For this test, create a mock object to simulate software without install dates
        mock_software = Mock()
        mock_software.id = f"{self.organisation.id}:opswat:{self.product_version.id}"
        mock_software.organisation_id = self.organisation.id
        mock_software.source = "opswat"
        mock_software.source_id = str(self.product_version.id)
        mock_software.product = self.product_version.product.name
        mock_software.version = self.product_version.raw_version
        mock_software.signatures = []

        enriched_results = enrich_installed_software_with_installers([mock_software])

        if enriched_results and hasattr(enriched_results[0], 'app_install_product_installer'):
            # patch_detected_date should be None when no install dates exist
            self.assertIsNone(enriched_results[0].app_install_product_installer.get('patch_detected_date'))

    def test_organisation_level_takes_earliest_install_date_across_multiple_devices(self):
        """Test organisation-level patch_detected uses earliest install date across multiple devices."""
        # Create multiple devices with different install dates
        devices_data = [
            ("2020-01-10", self.installed_product1),
            ("2020-01-15", self.installed_product2),
        ]

        # Create a third device
        user3 = AppUserFactory(organisation=self.organisation)
        app_install3 = AppInstallFactory(app_user=user3)
        installed_product3 = InstalledProductFactory(app_install=app_install3)
        devices_data.append(("2020-01-05", installed_product3))

        earliest_date = None
        for install_date, installed_product in devices_data:
            with freeze_time(install_date):
                ipv = InstalledProductVersionFactory(
                    installed_product=installed_product,
                    product_version=self.product_version
                )
                if not earliest_date or ipv.created < earliest_date:
                    earliest_date = ipv.created

        # Create patch installer before the earliest install
        with freeze_time("2020-01-01"):
            patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
            OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=patch_installer
            )

        # Refresh materialized view
        InstalledSoftwareOrganisationSummary.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationSummary.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich without app_install to trigger organisation-level logic
        enriched_results = enrich_installed_software_with_installers(installed_software)

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should use the earliest install date (Jan 5) since patch was available before
        self.assertEqual(
            enriched_item.app_install_product_installer['patch_detected_date'],
            earliest_date
        )

    def test_organisation_level_enrich_individual_installs(self):
        """Test enriching individual app_install_ids when enrich_individual_installs_on_org_level is True."""
        # Create different OS configurations for each app_install
        OpswatOperatingSystemFactory(app_install=self.app_install1, os_id=10)  # Windows
        OpswatOperatingSystemFactory(app_install=self.app_install2, os_id=50)  # Different OS

        # Install products on both devices
        with freeze_time("2020-01-15"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product1,
                product_version=self.product_version
            )

        with freeze_time("2020-01-20"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product2,
                product_version=self.product_version
            )

        # Create patch installer with OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
        installer = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,20]",  # Only allows OS IDs 1-20
            title="Windows Only Installer"
        )

        # Schedule installer for app_install1 only
        OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=installer,
            app_install=self.app_install1
        )

        # Refresh materialized view
        InstalledSoftwareOrganisationSummary.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationSummary.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich with individual installs enabled
        enriched_results = enrich_installed_software_with_installers(
            installed_software,
            enrich_individual_installs_on_org_level=True
        )

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Check that we have the new attribute
        self.assertTrue(hasattr(enriched_item, 'app_install_product_installers_by_app_install'))
        installers_by_app = enriched_item.app_install_product_installers_by_app_install

        # app_install1 should have the installer (OS compatible and scheduled)
        self.assertIn(self.app_install1.id, installers_by_app)
        app1_installer = installers_by_app[self.app_install1.id]
        self.assertEqual(app1_installer['app_install_product_installer_id'], installer.id)
        self.assertTrue(app1_installer['is_scheduled'])

        # app_install2 should not have the installer (OS not compatible)
        self.assertNotIn(self.app_install2.id, installers_by_app)

    def test_organisation_level_enrich_individual_installs_disabled(self):
        """Test that individual app_install enrichment is not done when parameter is False."""
        # Install products
        InstalledProductVersionFactory(
            installed_product=self.installed_product1,
            product_version=self.product_version
        )

        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="1.1.0")
        OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer
        )

        # Refresh materialized view
        InstalledSoftwareOrganisationSummary.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationSummary.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich with individual installs disabled (default)
        enriched_results = enrich_installed_software_with_installers(installed_software)

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Should have standard installer info
        self.assertTrue(hasattr(enriched_item, 'app_install_product_installer'))

        # Should NOT have per-app_install info
        self.assertFalse(hasattr(enriched_item, 'app_install_product_installers_by_app_install'))

    def test_organisation_level_different_installers_for_aggregate_vs_individual(self):
        """Test that org-level aggregate installer can differ from individual app_install installers."""
        # Create OS configurations: Windows and macOS
        OpswatOperatingSystemFactory(app_install=self.app_install1, os_id=10)  # Windows
        OpswatOperatingSystemFactory(app_install=self.app_install2, os_id=60)  # macOS

        # Install products on both devices
        with freeze_time("2020-01-15"):
            InstalledProductVersionFactory(
                installed_product=self.installed_product1,
                product_version=self.product_version
            )
            InstalledProductVersionFactory(
                installed_product=self.installed_product2,
                product_version=self.product_version
            )

        # Create three installers with different OS restrictions
        # 1. Windows-only installer (older)
        with freeze_time("2020-01-20"):
            windows_patch = OpswatPatchInstallerFactory(latest_version="1.1.0")
            OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=windows_patch,
                os_allow="[1,20]",  # Windows range
                title="Windows Only Installer"
            )

        # 2. macOS-only installer (older)
        with freeze_time("2020-01-21"):
            macos_patch = OpswatPatchInstallerFactory(latest_version="1.1.0")
            OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=macos_patch,
                os_allow="[55,70]",  # macOS range
                title="macOS Only Installer"
            )

        # 3. Universal installer (newest - will be selected for aggregate)
        with freeze_time("2020-01-25"):
            universal_patch = OpswatPatchInstallerFactory(latest_version="1.2.0")
            universal_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=universal_patch,
                # No OS restrictions - works on all platforms
                title="Universal Installer"
            )

        # Refresh materialized view
        InstalledSoftwareOrganisationSummary.refresh()

        # Get organisation-level installed software
        installed_software = InstalledSoftwareOrganisationSummary.objects.filter(
            organisation_id=self.organisation.id
        )

        # Enrich with individual installs enabled
        enriched_results = enrich_installed_software_with_installers(
            installed_software,
            enrich_individual_installs_on_org_level=True
        )

        self.assertEqual(len(enriched_results), 1)
        enriched_item = enriched_results[0]

        # Aggregate should have the universal installer (newest, no OS restrictions)
        self.assertEqual(
            enriched_item.app_install_product_installer['title'],
            "Universal Installer"
        )
        self.assertEqual(
            enriched_item.app_install_product_installer['installer_version'],
            "1.2.0"
        )

        # Individual app_installs should have OS-specific installers
        installers_by_app = enriched_item.app_install_product_installers_by_app_install

        # Windows app_install should have ALL compatible installers, but we pick the newest
        # Both Windows-only and Universal are compatible, Universal is newer
        self.assertIn(self.app_install1.id, installers_by_app)
        self.assertEqual(
            installers_by_app[self.app_install1.id]['app_install_product_installer_id'],
            universal_installer.id  # Universal is newer than Windows-only
        )

        # macOS app_install should also get Universal (newer than macOS-only)
        self.assertIn(self.app_install2.id, installers_by_app)
        self.assertEqual(
            installers_by_app[self.app_install2.id]['app_install_product_installer_id'],
            universal_installer.id  # Universal is newer than macOS-only
        )

        # Now test with a newer OS-specific installer
        with freeze_time("2020-01-30"):
            newer_windows_patch = OpswatPatchInstallerFactory(latest_version="1.3.0")
            newer_windows_installer = OpswatProductPatchInstallerFactory(
                product=self.product_version.product,
                patch_installer=newer_windows_patch,
                os_allow="[1,20]",  # Windows only
                title="Newer Windows Installer"
            )

        # Re-fetch and enrich
        InstalledSoftwareOrganisationSummary.refresh()
        installed_software = InstalledSoftwareOrganisationSummary.objects.filter(
            organisation_id=self.organisation.id
        )
        enriched_results = enrich_installed_software_with_installers(
            installed_software,
            enrich_individual_installs_on_org_level=True
        )

        enriched_item = enriched_results[0]

        # Aggregate should now have the newest Windows installer
        # (even though it's OS-specific, it's the newest overall)
        self.assertEqual(
            enriched_item.app_install_product_installer['title'],
            "Newer Windows Installer"
        )

        # Individual app_installs should reflect OS compatibility
        installers_by_app = enriched_item.app_install_product_installers_by_app_install

        # Windows gets the newer Windows installer
        self.assertEqual(
            installers_by_app[self.app_install1.id]['app_install_product_installer_id'],
            newer_windows_installer.id
        )

        # macOS still gets Universal (Windows installer not compatible)
        self.assertEqual(
            installers_by_app[self.app_install2.id]['app_install_product_installer_id'],
            universal_installer.id
        )


class EnrichInstalledSoftwareDisabledInstallerTests(UniquenessAndFrequencyViewTestCase):
    """
    Tests for ensuring disabled installers are filtered out from enrichment.
    """

    def setUp(self):
        """Set up test data by calling parent setUp and adding both active and disabled patch installers."""
        super().setUp()

        InstalledSoftwareAppInstallIndividual.refresh()
        self.installed_software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=self.app_install_2)
        self.assertEqual(self.installed_software.count(), 3)

        # Create active patch installer for Edge
        active_patch_installer = OpswatPatchInstallerFactory(
            latest_version="101.0.0.0",
            date_disabled=None
        )
        self.active_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            title="Active Edge Installer",
            patch_installer=active_patch_installer
        )

        # Create disabled patch installer for Edge
        disabled_patch_installer = OpswatPatchInstallerFactory(
            latest_version="102.0.0.0",
            date_disabled=timezone.now()
        )
        self.disabled_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            title="Disabled Edge Installer",
            patch_installer=disabled_patch_installer
        )

    def test_disabled_installers_not_included_in_enrichment(self):
        """Test that disabled patch installers are not included in enrichment results."""
        # Create scheduled installer only for the active installer
        OpswatScheduledProductInstallerFactory(
            opswat_product_patch_installer=self.active_product_patch_installer,
            app_install=self.app_install_2
        )

        result = enrich_installed_software_with_installers(self.installed_software, self.app_install_2)

        self.assertEqual(len(result), self.installed_software.count())

        edge_sw = next((sw for sw in result if AI2_EDGE.lower() in sw.product.lower()), None)
        self.assertIsNotNone(edge_sw, "Edge software not found in result")

        # Verify Edge is enriched with the active installer, not the disabled one
        self.assertTrue(hasattr(edge_sw, 'app_install_product_installer'))
        self.assertEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            self.active_product_patch_installer.id
        )
        self.assertEqual(edge_sw.app_install_product_installer['title'], "Active Edge Installer")
        self.assertEqual(edge_sw.app_install_product_installer['installer_version'], "101.0.0.0")

        # Verify the disabled installer is not used
        self.assertNotEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            self.disabled_product_patch_installer.id
        )

    def test_active_installer_preferred_over_disabled_fallback(self):
        """Test that when both active and disabled installers exist, active is preferred."""

        # Create a third installer that would be the most recent by creation date, but is disabled
        newer_disabled_patch_installer = OpswatPatchInstallerFactory(
            latest_version="103.0.0.0",
            date_disabled=timezone.now()
        )

        # Force creation time to be newer
        newer_disabled_product_patch_installer = OpswatProductPatchInstallerFactory(
            product=Product.objects.get(name=AI2_EDGE),
            title="Newer Disabled Edge Installer",
            patch_installer=newer_disabled_patch_installer
        )
        newer_disabled_product_patch_installer.created = timezone.now() + timedelta(minutes=1)
        newer_disabled_product_patch_installer.save()

        result = enrich_installed_software_with_installers(self.installed_software, self.app_install_2)

        edge_sw = next((sw for sw in result if AI2_EDGE.lower() in sw.product.lower()), None)
        self.assertIsNotNone(edge_sw, "Edge software not found in result")

        # Verify Edge is enriched with the active installer, not the newer disabled one
        self.assertTrue(hasattr(edge_sw, 'app_install_product_installer'))
        self.assertEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            self.active_product_patch_installer.id
        )
        self.assertEqual(edge_sw.app_install_product_installer['title'], "Active Edge Installer")
        self.assertEqual(edge_sw.app_install_product_installer['installer_version'], "101.0.0.0")

        self.assertNotEqual(
            edge_sw.app_install_product_installer['app_install_product_installer_id'],
            newer_disabled_product_patch_installer.id
        )


class OsFilteringEnrichmentTests(TestCase):
    """Test cases for OS filtering in the enrich_installed_software_with_installers function."""

    def setUp(self):
        """Set up test data for OS filtering tests."""

        # Create organisation and users
        self.organisation = OrganisationFactory()
        self.user = AppUserFactory(organisation=self.organisation)

        # Create app installs
        self.app_install_windows_10 = AppInstallFactory(app_user=self.user)
        self.app_install_windows_11 = AppInstallFactory(app_user=self.user)
        self.app_install_macos = AppInstallFactory(app_user=self.user)
        self.app_install_linux = AppInstallFactory(app_user=self.user)

        # Create OpswatOperatingSystem instances with different os_id values
        self.os_windows_10 = OpswatOperatingSystemFactory(
            app_install=self.app_install_windows_10,
            os_id=10,  # Within [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS
        )
        self.os_windows_11 = OpswatOperatingSystemFactory(
            app_install=self.app_install_windows_11,
            os_id=11,  # Within [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS
        )
        self.os_macos = OpswatOperatingSystemFactory(
            app_install=self.app_install_macos,
            os_id=100,  # Outside [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_MAC
        )
        self.os_linux = OpswatOperatingSystemFactory(
            app_install=self.app_install_linux,
            os_id=200,  # Outside [-2,58] range
            os_type=OpswatOperatingSystem.OS_TYPE_LINUX
        )

        # Create a product and version
        self.product_version = ProductVersionFactory(
            raw_version="1.0.0",
            major=1,
            minor=0,
            patch=0
        )

        # Create installed products
        self.installed_product_win10 = InstalledProductFactory(app_install=self.app_install_windows_10)
        self.installed_product_win11 = InstalledProductFactory(app_install=self.app_install_windows_11)
        self.installed_product_macos = InstalledProductFactory(app_install=self.app_install_macos)
        self.installed_product_linux = InstalledProductFactory(app_install=self.app_install_linux)

        # Create installed product versions
        InstalledProductVersionFactory(
            installed_product=self.installed_product_win10,
            product_version=self.product_version
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_win11,
            product_version=self.product_version
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_macos,
            product_version=self.product_version
        )
        InstalledProductVersionFactory(
            installed_product=self.installed_product_linux,
            product_version=self.product_version
        )

    def test_installer_with_os_deny_filters_correctly(self):
        """Test that installers with os_deny field filter out incompatible OS."""
        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installer with os_deny="[-2,58]" (denies Windows but allows macOS/Linux)
        installer_with_deny = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_deny="[-2,58]",  # Denies OS IDs up to 58 (no lower limit)
            title="Installer with OS Deny"
        )

        # Refresh materialized view and get installed software for each OS
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test Windows 10 (os_id=10) - should NOT get installer
        software_win10 = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_windows_10
        )
        enriched_win10 = enrich_installed_software_with_installers(software_win10, self.app_install_windows_10)
        self.assertEqual(len(enriched_win10), 1)
        self.assertFalse(hasattr(enriched_win10[0], 'app_install_product_installer'))

        # Test macOS (os_id=100) - should get installer
        software_macos = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos
        )
        enriched_macos = enrich_installed_software_with_installers(software_macos, self.app_install_macos)
        self.assertEqual(len(enriched_macos), 1)
        self.assertTrue(hasattr(enriched_macos[0], 'app_install_product_installer'))
        self.assertEqual(
            enriched_macos[0].app_install_product_installer['app_install_product_installer_id'],
            installer_with_deny.id
        )

    def test_installer_with_os_allow_filters_correctly(self):
        """Test that installers with os_allow field only allow specific OS."""
        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installer with os_allow="[1,20]" (allows only Windows 10 and 11)
        installer_with_allow = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,20]",  # Allows OS IDs from 1 to 20
            title="Installer with OS Allow"
        )

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test Windows 10 (os_id=10) - should get installer
        software_win10 = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_windows_10
        )
        enriched_win10 = enrich_installed_software_with_installers(software_win10, self.app_install_windows_10)
        self.assertEqual(len(enriched_win10), 1)
        self.assertTrue(hasattr(enriched_win10[0], 'app_install_product_installer'))
        self.assertEqual(
            enriched_win10[0].app_install_product_installer['app_install_product_installer_id'],
            installer_with_allow.id
        )

        # Test macOS (os_id=100) - should NOT get installer
        software_macos = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos
        )
        enriched_macos = enrich_installed_software_with_installers(software_macos, self.app_install_macos)
        self.assertEqual(len(enriched_macos), 1)
        self.assertFalse(hasattr(enriched_macos[0], 'app_install_product_installer'))

    def test_installer_with_both_os_allow_and_deny(self):
        """Test installer with both os_allow and os_deny fields."""
        # Create patch installer
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")

        # Create installer with both allow and deny
        # os_allow="[1,100]" - allows 1 to 100
        # os_deny="[10,20]" - denies 10 to 20
        # Result: allows 1-9 and 21-100
        _installer_complex = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,100]",
            os_deny="[10,20]",
            title="Installer with Complex OS Rules"
        )

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test Windows 10 (os_id=10) - should NOT get installer (in deny range)
        software_win10 = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_windows_10
        )
        enriched_win10 = enrich_installed_software_with_installers(software_win10, self.app_install_windows_10)
        self.assertEqual(len(enriched_win10), 1)
        self.assertFalse(hasattr(enriched_win10[0], 'app_install_product_installer'))

        # Test macOS (os_id=100) - should get installer (in allow range but not in deny range)
        software_macos = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_macos
        )
        enriched_macos = enrich_installed_software_with_installers(software_macos, self.app_install_macos)
        self.assertEqual(len(enriched_macos), 1)
        self.assertTrue(hasattr(enriched_macos[0], 'app_install_product_installer'))

        # Test Linux (os_id=200) - should NOT get installer (outside allow range)
        software_linux = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=self.app_install_linux
        )
        enriched_linux = enrich_installed_software_with_installers(software_linux, self.app_install_linux)
        self.assertEqual(len(enriched_linux), 1)
        self.assertFalse(hasattr(enriched_linux[0], 'app_install_product_installer'))

    def test_no_os_restrictions_allows_all(self):
        """Test that installers without OS restrictions work for all OS."""
        # Create patch installer without any OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")
        _installer_universal = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            title="Universal Installer"
        )

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Test all OS - should all get the installer
        for app_install in [self.app_install_windows_10, self.app_install_macos, self.app_install_linux]:
            software = InstalledSoftwareAppInstallIndividual.objects.filter(app_install=app_install)
            enriched = enrich_installed_software_with_installers(software, app_install)
            self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))
            self.assertEqual(enriched[0].app_install_product_installer['title'], "Universal Installer")

    def test_app_install_without_opswat_os_gets_all_installers(self):
        """Test that app_installs without opswat_os don't get OS filtering applied."""
        # Create app install without OS info
        app_install_no_os = AppInstallFactory(app_user=self.user)
        # Explicitly ensure there's no opswat_os
        self.assertFalse(hasattr(app_install_no_os, 'opswat_os'))

        # Create installed product
        installed_product = InstalledProductFactory(app_install=app_install_no_os)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=self.product_version
        )

        # Create installer with strict OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")
        _installer_restricted = OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,10]",  # Very restrictive - only OS IDs 1-10
            title="Restricted Installer"
        )

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Get software for app_install without OS
        software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=app_install_no_os
        )

        # Enrich - should get installer despite restrictive os_allow
        enriched = enrich_installed_software_with_installers(software, app_install_no_os)
        self.assertEqual(len(enriched), 1)
        self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))
        self.assertEqual(enriched[0].app_install_product_installer['title'], "Restricted Installer")

    def test_app_install_with_unknown_os_gets_all_installers(self):
        """Test that app_installs with unknown OS (os_id=-1) don't get OS filtering applied."""
        # Create app install with unknown OS
        app_install_unknown_os = AppInstallFactory(app_user=self.user)
        OpswatOperatingSystemFactory(
            app_install=app_install_unknown_os,
            os_id=-1,  # Unknown OS
            os_type=OpswatOperatingSystem.OS_TYPE_WINDOWS  # Type doesn't matter for unknown OS
        )

        # Create installed product
        installed_product = InstalledProductFactory(app_install=app_install_unknown_os)
        InstalledProductVersionFactory(
            installed_product=installed_product,
            product_version=self.product_version
        )

        # Create installer with strict OS restrictions
        patch_installer = OpswatPatchInstallerFactory(latest_version="2.0.0")
        OpswatProductPatchInstallerFactory(
            product=self.product_version.product,
            patch_installer=patch_installer,
            os_allow="[1,10]",  # Very restrictive - only OS IDs 1-10
            title="Restricted Installer for Known OS"
        )

        # Refresh materialized view
        InstalledSoftwareAppInstallIndividual.refresh()

        # Get software for app_install with unknown OS
        software = InstalledSoftwareAppInstallIndividual.objects.filter(
            app_install=app_install_unknown_os
        )

        # Enrich - should get installer despite restrictive os_allow because os_id=-1 is treated as no OS info
        enriched = enrich_installed_software_with_installers(software, app_install_unknown_os)
        self.assertEqual(len(enriched), 1)
        self.assertTrue(hasattr(enriched[0], 'app_install_product_installer'))
        self.assertEqual(enriched[0].app_install_product_installer['title'], "Restricted Installer for Known OS")


