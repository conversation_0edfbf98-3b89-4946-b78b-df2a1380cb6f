from collections import defaultdict
import operator
from typing import Callable, Optional, List, Union
from datetime import datetime

from appusers.utils import parse_version
from appusers.models import InstalledSoftwareOrganisationSummary, AppInstall
from appusers.models.installed_software import InstalledSoftwareAppInstallIndividual

from django.db.models import Min, Prefetch, QuerySet

from opswat.utils import parse_composite_ids
from opswat.models import ProductVersion, InstalledProductVersion
from opswat.opswat_operating_system import is_installer_os_compatible
from opswat_patch.models import OpswatProductPatchInstaller, OpswatScheduledProductInstaller


def _get_app_install_dict_for_org_enrichment(installed_software: list) -> dict:
    """
    Pre-fetch all AppInstall objects for organization-level enrichment.
    Returns a dict mapping app_install_id to AppInstall object.
    """
    all_app_install_ids = set()
    for app in installed_software:
        if hasattr(app, 'app_install_ids'):
            all_app_install_ids.update(app.app_install_ids)

    if not all_app_install_ids:
        return {}

    app_install_objs = AppInstall.objects.filter(
        pk__in=all_app_install_ids
    ).select_related('opswat_os')

    return {ai.pk: ai for ai in app_install_objs}


def _get_scheduled_installers_for_org(all_app_install_ids: set, product_ids: set) -> dict:
    """
    Pre-fetch all scheduled installers for given app_installs and products.
    Returns a dict with (app_install_id, product_id) tuples as keys.
    """
    if not all_app_install_ids or not product_ids:
        return {}

    scheduled = OpswatScheduledProductInstaller.non_terminal.filter(
        app_install_id__in=all_app_install_ids,
        opswat_product_patch_installer__product_id__in=product_ids
    ).values_list('app_install_id', 'opswat_product_patch_installer__product_id')

    return {(app_install_id, product_id): True for app_install_id, product_id in scheduled}


def enrich_installed_software_with_installers(
    installed_software: Union[QuerySet[InstalledSoftwareOrganisationSummary], QuerySet[InstalledSoftwareAppInstallIndividual], List[Union[InstalledSoftwareOrganisationSummary, InstalledSoftwareAppInstallIndividual]]],
    app_install=None,
    enrich_individual_installs_on_org_level: bool = False
) -> list:
    """
    Enrich installed software with patch installer information.
    Finds matching installers using signatures and adds installer info to each app.

    OS Filtering:
    - If app_install has opswat_os information, filters installers based on OS compatibility
      using os_allow and os_deny fields
    - For backward compatibility: if app_install has no opswat_os, all installers are considered
      compatible (no OS filtering applied)

    Args:
        installed_software: QuerySet or list of installed software objects
        app_install: AppInstall instance for app-level enrichment
        enrich_individual_installs_on_org_level: When True and processing InstalledSoftwareOrganisationSummary,
            enriches each app_install_id with individual installer information
    """
    if not installed_software:
        return []

    # Determine if we're processing app-install level or organisation level data
    is_app_install_level = isinstance(installed_software[0], InstalledSoftwareAppInstallIndividual)

    # Extract product version IDs and signatures from installed software
    product_version_ids = []
    app_signatures_map = {}

    for app in installed_software:
        _product_version_ids, _ = parse_composite_ids(app.id)
        product_version_ids.extend(_product_version_ids)
        if hasattr(app, 'signatures') and app.signatures:
            app_signatures_map[app.id] = app.signatures

    if not product_version_ids:
        return installed_software

    # patch_detected related data helpers
    product_versions = ProductVersion.objects.filter(id__in=product_version_ids).select_related('product')
    product_version_id_to_string = {str(v.id): v.get_version_string() for v in product_versions}
    product_version_id_to_install_date = {}

    # Check if we have an app_install or if we can extract organisation from installed_software
    if is_app_install_level:
        installed_product_versions = InstalledProductVersion.objects.filter(
            product_version_id__in=product_version_ids,
            installed_product__app_install=app_install
        )
        product_version_id_to_install_date = {
            str(ipv.product_version_id): ipv.created for ipv in installed_product_versions
        }
    else:
        # Try to get organisation from installed_software objects
        organisation = None
        for app in installed_software:
            if hasattr(app, 'organisation_id'):
                organisation = app.organisation_id
                break

        if organisation:
            # For organisation-level view, get the earliest install date for each product version
            earliest_installs = InstalledProductVersion.objects.filter(
                product_version_id__in=product_version_ids,
                installed_product__app_install__app_user__organisation_id=organisation,
                installed_product__app_install__app_user__active=True,
                installed_product__app_install__inactive=False
            ).values('product_version_id').annotate(
                earliest_install=Min('created')
            )
            product_version_id_to_install_date = {
                str(ei['product_version_id']): ei['earliest_install'] for ei in earliest_installs
            }

    # Get installers that match product versions and signatures
    # Prefetch only the specific versions we need to avoid loading unnecessary data
    versions_prefetch = Prefetch(
        'product__versions',
        queryset=ProductVersion.objects.filter(id__in=product_version_ids),
        to_attr='prefetched_versions'
    )

    installers = OpswatProductPatchInstaller.objects.active().filter(
        product__versions__in=product_version_ids
    ).select_related('product', 'patch_installer').prefetch_related('signatures', versions_prefetch).order_by('product_id', '-patch_installer__created')

    # Get scheduled installer IDs if app_install is provided
    scheduled_installer_ids = set()
    if is_app_install_level:
        scheduled_installer_ids = set(
            OpswatScheduledProductInstaller.non_terminal.filter(
                app_install=app_install,
                opswat_product_patch_installer__in=installers
            ).values_list('opswat_product_patch_installer_id', flat=True)
        )

    # Create a map of product version ID to best matching installer
    product_version_to_installer = defaultdict(list)
    # Group installers by product version
    for installer in installers:
        # Use the prefetched versions to avoid additional queries
        for version in installer.product.prefetched_versions:
            version_id = str(version.id)
            product_version_to_installer[version_id].append(installer)

    # Check if we have OS information for filtering
    has_os_info = app_install.has_known_opswat_os_id if app_install else False
    os_id = app_install.opswat_os.os_id if has_os_info else None

    # Pre-fetch data for organization-level individual enrichment if needed
    app_install_dict = {}
    scheduled_installers_dict = {}
    if not is_app_install_level and enrich_individual_installs_on_org_level:
        app_install_dict = _get_app_install_dict_for_org_enrichment(installed_software)

        # Collect all product IDs from installers
        all_product_ids = set()
        for installer in installers:
            all_product_ids.add(installer.product_id)

        # Get all app_install_ids
        all_app_install_ids = set(app_install_dict.keys())

        # Pre-fetch scheduled installers
        scheduled_installers_dict = _get_scheduled_installers_for_org(all_app_install_ids, all_product_ids)

    # Enrich apps with installer information
    enriched_apps = []
    for app in installed_software:
        _product_version_ids, _ = parse_composite_ids(app.id)
        if not _product_version_ids:
            enriched_apps.append(app)
            continue

        """
        This block ensures we check for installers in all provided product versions
        associated with the app. If no installers are found for any of the product versions,
        we skip enrichment for that app.
        Otherwise, we proceed with the first matching product version ID that has installers.
        """
        for product_version_id in _product_version_ids:
            all_installers = product_version_to_installer.get(str(product_version_id), [])

            # For backward compatibility: if no OS info available, don't filter by OS
            if has_os_info:
                # Filter installers by OS compatibility only if we have OS information
                matching_installers = [inst for inst in all_installers if is_installer_os_compatible(inst, os_id)]
            else:
                # No OS filtering - all installers are considered compatible
                matching_installers = all_installers
            if matching_installers:
                product_version_id = str(product_version_id)
                break
        else:
            enriched_apps.append(app)
            continue

        # Find best installer: first try to match signatures, then fall back to most recent
        app_signatures = app_signatures_map.get(app.id, [])
        installer = None

        signature_match_found = False
        valid_signature_version_found = False

        if app_signatures:
            for inst in matching_installers:
                installer_signature_ids = [sig.id for sig in inst.signatures.all()]

                if any(sig_id in installer_signature_ids for sig_id in app_signatures):
                    signature_match_found = True
                    if compare_semver_core(app.version, inst.patch_installer.latest_version, operator.lt):
                        valid_signature_version_found = True
                        installer = inst
                        break

            if signature_match_found and not valid_signature_version_found:
                # Signature match found for the app, but no installer has newer version. Don't enrich with installer.
                enriched_apps.append(app)
                continue

        # If no signature match, use most recent installer (already ordered by -patch_installer__created)
        if not installer and matching_installers:
            # Check version for fallback installer as well
            for inst in matching_installers:
                if compare_semver_core(app.version, inst.patch_installer.latest_version, operator.lt):
                    installer = inst
                    break

        # If no valid installer found (version not greater), skip enrichment
        if not installer:
            enriched_apps.append(app)
            continue

        # Get patch detected date
        patch_detected_date = get_patch_detected_date(
            product_version_id,
            product_version_id_to_string,
            product_version_id_to_install_date,
            installer
        )

        # Add installer info to app
        if is_app_install_level:
            app.app_install_product_installer = {
                'app_install_product_installer_id': installer.id,
                'title': installer.title,
                'is_scheduled': installer.id in scheduled_installer_ids,
                'installer_version': installer.patch_installer.latest_version,
                'patch_detected_date': patch_detected_date,
            }
        else:
            # Organisation level - add installer info
            app.app_install_product_installer = {
                'app_install_product_installer_id': installer.id,
                'title': installer.title,
                'is_scheduled': installer.id in scheduled_installer_ids,
                'installer_version': installer.patch_installer.latest_version,
                'patch_detected_date': patch_detected_date,
            }

            # If requested, also enrich individual app_install_ids
            if enrich_individual_installs_on_org_level and hasattr(app, 'app_install_ids'):
                app.app_install_product_installers_by_app_install = {}

                for app_install_id in app.app_install_ids:
                    # Get app_install from pre-fetched dictionary
                    app_install_obj = app_install_dict.get(app_install_id)
                    if not app_install_obj:
                        continue

                    # Get all installers for this product version
                    all_app_installers = product_version_to_installer.get(str(product_version_id), [])

                    # Filter installers by OS compatibility for this specific app_install
                    if app_install_obj.has_known_opswat_os_id:
                        app_os_id = app_install_obj.opswat_os.os_id
                        compatible_installers = [inst for inst in all_app_installers if is_installer_os_compatible(inst, app_os_id)]
                    else:
                        # No OS info or unknown OS - all installers are compatible
                        compatible_installers = all_app_installers

                    if compatible_installers:
                        # Find best installer for this app_install (most recent with valid version)
                        best_installer = None
                        for inst in compatible_installers:
                            if compare_semver_core(app.version, inst.patch_installer.latest_version, operator.lt):
                                best_installer = inst
                                break

                        if best_installer:
                            # Check if scheduled using pre-fetched data
                            is_scheduled = scheduled_installers_dict.get((app_install_id, best_installer.product_id), False)

                            # Simplified dictionary with only necessary fields
                            app.app_install_product_installers_by_app_install[app_install_id] = {
                                'app_install_product_installer_id': best_installer.id,
                                'is_scheduled': is_scheduled,
                            }

        enriched_apps.append(app)

    return enriched_apps

def get_patch_detected_date(product_version_id: str, product_version_id_to_version_string: dict[str, str], product_version_id_to_install_date: dict[str, datetime], installer: OpswatProductPatchInstaller) -> datetime | None:
    patch_detected_date: Optional[datetime] = None
    device_version_string = product_version_id_to_version_string.get(product_version_id)
    installer_version_string = installer.patch_installer.latest_version

    if not device_version_string:
        return None

    parsed_device = parse_version(device_version_string)
    parsed_installer = parse_version(installer_version_string)

    if not parsed_device or not parsed_installer:
        return None
    core_device = parsed_device[:-1]
    core_installer = parsed_installer[:-1]

    # Only proceed if patch version is higher than installed version
    if core_installer <= core_device:
        return None

    installed_date = product_version_id_to_install_date.get(product_version_id)
    installer_modified_date = installer.modified

    if not installed_date:
        return None

    # Case 1: Patch available before install
    if installer_modified_date < installed_date:
        patch_detected_date = installed_date
    # Case 2: Install before patch available
    else:
        patch_detected_date = installer_modified_date
    return patch_detected_date

def compare_semver_core(version1: str, version2: str, op: Callable = operator.eq) -> bool:
    """
    Compare the core components (MAJOR.MINOR.PATCH) of two semantic version strings
    using a specified comparison operator.

    Semantic versions follow the format: MAJOR.MINOR.PATCH[-CHANNEL].
    This function compares the MAJOR, MINOR, and PATCH parts, ignoring channel info.

    Args:
        version1 (str): First semantic version string.
        version2 (str): Second semantic version string.
        op (Callable, optional): Comparison operator. Defaults to operator.eq.

    Returns:
        bool: Result of applying the comparison operator to the core components.
    """
    parsed_v1 = parse_version(version1)
    parsed_v2 = parse_version(version2)

    if not parsed_v1 or not parsed_v2:
        return False

    core_v1 = parsed_v1[:-1]
    core_v2 = parsed_v2[:-1]

    return op(core_v1, core_v2)
