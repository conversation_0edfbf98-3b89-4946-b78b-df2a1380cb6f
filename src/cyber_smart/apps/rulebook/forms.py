import collections

from django import forms
from django.conf import settings
from django.utils.translation import gettext_lazy as _

from common.utils import handle_user_input
from common.validators import safe_char_validator, post_code_validator, pervade_safe_char_validator
from organisations.models import Organisation, OrganisationCertification, get_organisations
from rulebook.models import SurveyQuestion, CertificationVersion, AssessorNote, SurveyAssessment, SurveyResponse
from rulebook.widgets import (
    ChoiceFieldNoValidation, ExpandedRadioWidget, RadioTextareaWidget, ExpandedCheckboxWidget,
    ExpandedRadioCheckboxWidget, OrganisationAddressWidget, CustomClearableFileInput, MoneyWidget, EmailWidget,
    YesNoTextWidget, YesNoCommonReasonsTextWidget, ConsciousWidget, SimpleRadioWidget
)


class ResponseForm(forms.Form):
    """
    Dynamic form that represents a question and answers.
    """
    DEFAULT_WIDGET_PARAMS = {'attrs': {'class': 'form-control question-response-input'}}
    FIELDS = {
        SurveyQuestion.TEXT: (forms.CharField, {
            'label': '',
            'widget': forms.Textarea(attrs={'class': 'form-control question-response-input', 'rows': 2, 'cols': 1}),
            'validators': [pervade_safe_char_validator]
        }),
        SurveyQuestion.INTEGER: (forms.IntegerField, {
            'label': '',
            'widget': forms.NumberInput(
                attrs={"class": "form-control input-only-numbers question-response-input", "pattern": "[0-9]*"}
            )
        }),
        SurveyQuestion.BOOLEAN: (forms.BooleanField, {
            'label': '',
            'widget': forms.CheckboxInput(
                attrs={'class': 'form-control js-switch js-check-change question-response-input'}
            )
        }),
        SurveyQuestion.FLOAT: (forms.FloatField, {
            'label': '',
            'widget': forms.NumberInput(**DEFAULT_WIDGET_PARAMS)
        }),
        SurveyQuestion.DATE: (forms.DateField, {
            'label': '',
            'widget': forms.DateInput(**DEFAULT_WIDGET_PARAMS)
        }),
        SurveyQuestion.DATETIME: (forms.DateTimeField, {
            'label': '',
            'widget': forms.DateTimeInput(**DEFAULT_WIDGET_PARAMS)
        }),
        SurveyQuestion.FILE: (forms.FileField, {
            'label': '',
            'widget': CustomClearableFileInput(attrs={'class': 'form-control file-input question-response-input'})
        }),
        SurveyQuestion.IMAGE: (forms.FileField, {
            'label': '',
            'widget': CustomClearableFileInput(attrs={'class': 'form-control file-input question-response-input'})
        }),
        SurveyQuestion.MONEY: (forms.CharField, {
            'label': '',
            'widget': forms.TextInput(**DEFAULT_WIDGET_PARAMS),
            'validators': [pervade_safe_char_validator]
        })
    }

    def __init__(self, question, *args, **kwargs):
        """
        Initializes form with generated field, applies specific settings for some questions, generates custom attributes
        and initializes question's responses.
        :param question: survey question
        :type question: SurveyQuestion
        :param args: positional arguments
        :type args: tuple
        :param kwargs: keyword arguments
        :type kwargs: dict
        :return: nothing
        :rtype: None
        """
        is_failed = kwargs.pop('is_failed', False)
        self.certification = kwargs.pop("certification", None)
        self.question = question
        if not self.certification:
            raise ValueError('Certification is required when calling ResponseForm')
        super(ResponseForm, self).__init__(*args, **kwargs)
        self._generate_fields(question)
        # specific things for some questions
        self.specific_settings(question, is_failed)
        # generate custom ids for fields
        self._generate_custom_attr(question)
        # initialize fields with response value if response provided
        self._initialize_responses(question, self.certification)

    def clean(self):
        from rulebook.utils import organisation_number_validation
        if not organisation_number_validation(self.certification, self.question, self.cleaned_data):
            self.add_error(
                self.field_name,
                "'None' is only permitted for certain organisation types. A valid registration number is required based on your A1.2 selection."
            )

    def _generate_fields(self, question):
        """
        Generates question field (widget).
        :param question: survey question
        :type question: SurveyQuestion
        :return: nothing
        :rtype: None
        """
        field, params = self.FIELDS[question.response_type]
        params['required'] = False
        self.field_name = "response_{0}_{1}".format(
            question.pk,
            question.order_str,
        )
        self.fields[self.field_name] = field(**params)
        # generate organisation address field
        if question.widget == SurveyQuestion.WIDGET_ORGANISATION_ADDRESS:
            verbose_field_name = {
                'address1': 'Address Line 1',
                'address2': 'Address Line 2',
                'city': 'Town/City',
                'county': 'County',
                'postcode': 'Postcode',
                "country": "Country"
            }
            self._generate_multitext_field(question, verbose_field_name, 'address')
        # generate organisation industry secription field
        elif question.widget == SurveyQuestion.WIDGET_ORGANISATION_TYPE_SELECT:
            self._generate_multitext_field(question, {
                'industry_description': 'Industry Description'
            }, 'industry_description')
        elif question.widget in (SurveyQuestion.WIDGET_ORGANISATION_IT, SurveyQuestion.WIDGET_FULL_NAME):
            if question.widget == SurveyQuestion.WIDGET_ORGANISATION_IT:
                # generate organisation IT field
                verbose_field_name = {
                    'name': 'Name',
                    'role': 'Role'
                }
                field_prefix = "organisation-IT"
            elif question.widget == SurveyQuestion.WIDGET_FULL_NAME:
                # generate full name fields
                verbose_field_name = {
                    "firstname": "First name",
                    "lastname": "Last name"
                }
                field_prefix = "full-name"
            self.fields = {}
            self._generate_multitext_field(question, verbose_field_name, field_prefix)
            self.fields = collections.OrderedDict([(i, self.fields[i]) for i in sorted(self.fields.keys())])

    @staticmethod
    def generate_organisation_address_country_input(question, verbose_field_name, field_name, field_type):
        """
        Generates organisation address country input.
        """
        choices = [(None, "---")]
        # load countries from question pervade compliance fields
        for key, value in question.pervade_compliance_fields.items():
            if "name" in value:
                if value["name"].lower() == "country":
                    choices += [(country, country) for country in value["options"]]
        return forms.ChoiceField(**{
            "choices": choices,
            "label": verbose_field_name[field_name],
            "required": False,
            "widget": forms.Select(
                attrs={
                    "class": f"form-control select2 country-select question-response-input {field_type}-{field_name}"
                }
            )
        })

    def _generate_multitext_field(self, question, verbose_field_name, field_type):
        for field_name in verbose_field_name.keys():
            validators = [pervade_safe_char_validator]
            if field_type == "address" and field_name == "country":
                self.fields[
                    f"response_{question.pk}_{question.order_str}_{field_name}"
                ] = self.generate_organisation_address_country_input(
                    question, verbose_field_name, field_name, field_type
                )
            elif field_type == "industry_description":
                self.fields[
                    f"response_{question.pk}_{question.order_str}_{field_name}"
                ] = forms.CharField(
                    label=_("Industry Description* "),
                    required=False,
                    max_length=500,
                    widget=forms.Textarea(attrs={"class": "form-control industry_description"}),
                    validators=[safe_char_validator]
                )
            else:
                if field_name == "postcode" and self.certification and self.certification.is_uk_postcode_validation_needed:
                    # custom validator for postcode, only for superscript insurance
                    validators = [pervade_safe_char_validator, post_code_validator]
                self.fields[f"response_{question.pk}_{question.order_str}_{field_name}"] = forms.CharField(**{
                    'label': verbose_field_name[field_name],
                    'required': False,
                    'widget': forms.TextInput(
                        attrs={
                            'class': 'form-control question-response-input {0}-{1}'.format(
                                field_type, field_name)
                        }
                    ),
                    'validators': validators
                })

    def _initialize_responses(self, question, certification):
        """
        Initializes form field with responses if they exist.
        :param question: survey questions
        :type question: SurveyQuestion
        :return: nothing
        :rtype: None
        """
        # check if prefetched responses exist
        if hasattr(question, 'responses'):
            responses = question.responses.all()
        else:
            responses = question.responses.filter(survey=certification.survey)
        response = responses[0] if responses else None
        if response and self.fields.get(self.field_name):
            self.fields[self.field_name].initial = response.regular_value

        # initialize organisation address or IT fields
        fields = {
            SurveyQuestion.WIDGET_ORGANISATION_ADDRESS: SurveyQuestion.WIDGET_ORGANISATION_ADDRESS_FIELDS,
            SurveyQuestion.WIDGET_ORGANISATION_IT: SurveyQuestion.WIDGET_ORGANISATION_IT_FIELDS,
            SurveyQuestion.WIDGET_FULL_NAME: SurveyQuestion.WIDGET_FULL_NAME_FIELDS,
            SurveyQuestion.WIDGET_ORGANISATION_TYPE_SELECT: ["industry", "industry_description"]
        }
        if question.widget in fields.keys():
            response_fields = fields[question.widget]
            response_keys_prefix = 'response_{0}_{1}_'.format(question.pk, question.order_str)
            for field in response_fields:
                response = next((r for r in responses if r.code == field), None)
                if response:
                    self.fields['{0}{1}'.format(response_keys_prefix, field)].initial = response.regular_value

    def _generate_custom_attr(self, question):
        """
        Generates custom attributes for widgets.
        :param question: survey question
        :type question: SurveyQuestion
        :return: nothing
        :rtype: None
        """
        for name, field in self.fields.items():
            self.fields[name].widget.attrs['order'] = question.order_str
            self.fields[name].widget.attrs['question-pk'] = question.pk
            self.fields[name].widget.attrs["send_off_values"] = True
            if question.code:
                self.fields[name].widget.attrs["code"] = question.code

    def specific_settings(self, question, is_failed=False):
        """
        Applies custom settings for some questions widgets.
        :param question: survey question
        :type question: SurveyQuestion
        :param is_failed: indicates if the question is a failed one or not
        :type is_failed: bool
        :return: nothing
        :rtype: None
        """
        response = question.responses.first()
        failed_response = question.failed_responses.first()
        widget_type = question.widget

        if question.pervade_title and question.pervade_title.startswith("A2.4 End User Devices"):
            # update the widget attributes
            self.fields[self.field_name].widget.attrs.update(
                {
                    "class": "form-control question-response-input highlighted-textarea",
                    "rows": 2, "cols": 1, "spellcheck": "false"
                }
            )

        if widget_type == SurveyQuestion.WIDGET_ORGANISATION_SEARCH:
            self.fields[self.field_name] = ChoiceFieldNoValidation(
                choices=((response.regular_value, response.regular_value),) if response else (),
                label='',
                required=False,
                widget=forms.Select(attrs={'class': 'form-control select2 organisation-search-widget'})
            )
        elif widget_type == SurveyQuestion.WIDGET_ORGANISATION_TYPE_SELECT:
            self.fields[self.field_name] = forms.ChoiceField(
                choices=Organisation.ORG_CHOICES,
                label='',
                required=False,
                widget=forms.Select(attrs={'class': 'form-control select2 industry-type-widget'})
            )
        elif widget_type in (
                SurveyQuestion.WIDGET_YES_NO_KEEP_OPTIONS, SurveyQuestion.WIDGET_YES_NO_TEXT,
                SurveyQuestion.WIDGET_COMMON_REASONS, SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT
        ):
            if widget_type in (SurveyQuestion.WIDGET_YES_NO_TEXT, SurveyQuestion.WIDGET_COMMON_REASONS,
                               SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT):
                if widget_type == SurveyQuestion.WIDGET_YES_NO_TEXT:
                    widget = YesNoTextWidget
                else:
                    widget = YesNoCommonReasonsTextWidget
                self.fields[self.field_name] = forms.CharField(
                    label="",
                    required=False,
                    widget=widget(
                        question,
                        response,
                        failed_response,
                        attrs={
                            "class": "form-control question-response-input",
                            "optional_text": widget_type == SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT
                        }
                    ),
                    validators=[pervade_safe_char_validator]
                )
            # skip WIDGET_YES_NO_KEEP_OPTIONS widget, we keep there choices to save old responses
            # in real this widget has only yes/no boolean response
        elif widget_type == SurveyQuestion.WIDGET_MONEY:
            self.fields[self.field_name] = forms.CharField(
                label="",
                required=False,
                widget=MoneyWidget(
                    question,
                    response,
                    failed_response,
                    attrs={
                        "class": "form-control question-response-input"
                    }
                ),
                validators=[pervade_safe_char_validator]
            )
        elif widget_type == SurveyQuestion.WIDGET_EMAIL:
            self.fields[self.field_name] = forms.EmailField(
                label="",
                required=False,
                widget=EmailWidget(
                    question,
                    response,
                    failed_response,
                    attrs={
                        "class": "form-control input-only-email question-response-input",
                        "pattern": r"/^[a-zA-Z0-9.!#$%&’*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/"
                    }
                ),
                validators=[pervade_safe_char_validator]
            )
        elif widget_type == SurveyQuestion.WIDGET_CONSCIOUS or question.choices.count():
            widgets = {
                SurveyQuestion.WIDGET_RADIO: ExpandedRadioWidget,
                SurveyQuestion.WIDGET_RADIO_SIMPLE: SimpleRadioWidget,
                SurveyQuestion.WIDGET_CHECKBOX: ExpandedCheckboxWidget,
                SurveyQuestion.WIDGET_RADIO_CHECKBOX: ExpandedRadioCheckboxWidget,
                SurveyQuestion.WIDGET_ORGANISATION_ADDRESS: OrganisationAddressWidget,
                SurveyQuestion.WIDGET_RADIO_TEXTAREA: RadioTextareaWidget,
                SurveyQuestion.MONEY: MoneyWidget,
                SurveyQuestion.WIDGET_CONSCIOUS: ConsciousWidget
            }
            required = False
            if widget_type == SurveyQuestion.WIDGET_CONSCIOUS:
                required = question.text_box_is_required
            question.is_failed = is_failed
            self.fields[self.field_name] = forms.CharField(
                label='',
                required=required,
                widget=widgets.get(widget_type, widgets[SurveyQuestion.WIDGET_RADIO])(
                    question, response, failed_response, attrs={'class': 'form-control'}
                )
            )


class ApprovalEmailForm(forms.Form):
    name = forms.CharField(label='Name', max_length=255, required=True, validators=[safe_char_validator])
    title = forms.CharField(label='Title', max_length=255, required=True, validators=[safe_char_validator])
    email = forms.EmailField(label='Email', required=True)
    secondary_email = forms.EmailField(label='Secondary Email', required=False)

    def clean_name(self):
        name = handle_user_input(self.cleaned_data['name'])
        return name

    def clean_title(self):
        title = handle_user_input(self.cleaned_data['title'])
        return title

    def clean_email(self):
        email = handle_user_input(self.cleaned_data['email'])
        return email

    def clean_secondary_email(self):
        secondary_email = handle_user_input(self.cleaned_data['secondary_email'])
        return secondary_email


class MigrateAnswersForm(forms.Form):
    organisation = forms.ModelChoiceField(
        label=_('Organisation'), queryset=Organisation.objects.filter(is_test=False).order_by('name'), required=False,
        help_text=_('You can select specific organisation. Not required')
    )
    from_version = forms.ModelChoiceField(
        label=_('From (older version)'), required=True,
        queryset=CertificationVersion.objects.all().select_related('type')
    )
    to_version = forms.ModelChoiceField(
        label=_('To (newer version)'), required=True, queryset=CertificationVersion.objects.all().select_related('type')
    )
    cert_status = forms.ChoiceField(
        label=_('Migrate surveys with a specific status'),
        help_text=(
            _('This will only migrate certificates with the selected status')
        ),
        required=False,
        choices=[('', 'All')] + list(OrganisationCertification.STATUSES)
    )
    create_new_survey = forms.BooleanField(
        label=_('Create new survey'),
        help_text=(
            _('Creates and starts new surveys with newer version for organisations that have only older version. '
              'If not checked organisations that don\'t have survey with newer version will be skipped.')
        ),
        required=False
    )
    skip_surveys_with_answers = forms.BooleanField(
        label=_('Skip surveys with answers'),
        help_text=(
            _('Skips organisations that already have new version survey with answers filled. '
              'If not checked existing answers will be overwritten.')
        ),
        required=False
    )

    def clean(self):
        cleaned_data = super(MigrateAnswersForm, self).clean()
        if cleaned_data['from_version'].type.type != cleaned_data['to_version'].type.type:
            raise forms.ValidationError(_('Certifications must be the same type'))
        if cleaned_data['from_version'] == cleaned_data['to_version']:
            raise forms.ValidationError(_('Versions are the same'))
        if cleaned_data['from_version'].version_number > cleaned_data['to_version'].version_number:
            raise forms.ValidationError(_('You cannot migrate answers from newer version to older'))
        if cleaned_data['organisation']:
            if not cleaned_data['organisation'].certifications.filter(version=cleaned_data['from_version']).exists():
                raise forms.ValidationError('"{0}" does not have a certification with selected version "{1}"'.format(
                    cleaned_data['organisation'],
                    cleaned_data['from_version']
                ))
            if cleaned_data['cert_status'] and not cleaned_data['organisation'].certifications.filter(
                    version=cleaned_data['from_version'],
                    status=cleaned_data['cert_status']
            ).exists():
                raise forms.ValidationError(
                    f'"{cleaned_data["organisation"]}" does not have a certification with selected version '
                    f'"{cleaned_data["from_version"]}" in the selected status.'
                )


class CreateAssessorNoteForm(forms.Form):
    question_pk = forms.CharField(required=True, validators=[pervade_safe_char_validator])
    certification_pk = forms.CharField(required=True, validators=[pervade_safe_char_validator])
    text = forms.CharField(required=True)

    def __init__(self, *args, **kwargs):
        self.certification = None
        self.question = None
        self.cleaned_text = None
        self.request = kwargs.pop('request')
        super(CreateAssessorNoteForm, self).__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super(CreateAssessorNoteForm, self).clean()
        if 'text' not in cleaned_data:
            raise forms.ValidationError(_('Assessor note cannot be empty'))
        try:
            self.certification = OrganisationCertification.objects.get(pk=cleaned_data['certification_pk'])
            self.question = SurveyQuestion.objects.filter_insurer_options(self.certification).get(
                pk=cleaned_data['question_pk'],
            )
            self.cleaned_text = handle_user_input(cleaned_data['text'])

            if self.request.user.profile.is_partner and self.request.user.profile.partner.iasme_cb:
                # for cb get all organisations of their clients
                organisation = get_organisations(
                    self.request.user, self.certification.organisation.secure_id, as_queryset=True
                ).first()
            else:
                # for admin staff get all organisations in the platform except cb
                organisation = Organisation.objects.filter(is_test=False).exclude(
                    partner__iasme_cb=True
                ).filter(secure_id=self.certification.organisation.secure_id).first()

            if not organisation:
                raise forms.ValidationError(_('You do not have access to this organisation'))

            if not self.certification.survey:
                raise forms.ValidationError(_('Certification does not have a survey'))

            if self.certification.version.pk != self.question.version.pk:
                raise forms.ValidationError(_('Certification and question have different version'))

        except OrganisationCertification.DoesNotExist:
            raise forms.ValidationError(_('Certification does not exist'))
        except SurveyQuestion.DoesNotExist:
            raise forms.ValidationError(_('Question does not exist'))

        return cleaned_data

    def save(self):
        # get or create survey assessment
        assessment, _ = SurveyAssessment.objects.get_or_create(
            survey=self.certification.survey
        )
        # make all existing notes for given question as re-answered and accepted
        AssessorNote.objects.filter(
            assessment__survey=self.certification.survey,
            question=self.question
        ).update(re_answered=True, answer_accepted=True)
        # update answer as not accepted
        SurveyResponse.objects.filter(
            survey=self.certification.survey,
            question=self.question
        ).update(assessor_acceptance=False)
        # create and return new assessor note
        return AssessorNote.objects.create(
            assessment=assessment,
            topic=self.question.topic,
            question=self.question,
            text=self.cleaned_text
        )


class AcceptAssessorNoteForm(forms.Form):
    question_pk = forms.CharField(required=True, validators=[pervade_safe_char_validator])
    certification_pk = forms.CharField(required=True, validators=[pervade_safe_char_validator])

    def __init__(self, *args, **kwargs):
        self.certification = None
        self.question = None
        self.request = kwargs.pop('request')
        super(AcceptAssessorNoteForm, self).__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super(AcceptAssessorNoteForm, self).clean()
        try:
            self.certification = OrganisationCertification.objects.get(pk=cleaned_data['certification_pk'])
            self.question = SurveyQuestion.objects.filter_insurer_options(self.certification).get(
                pk=cleaned_data['question_pk'],
            )

            if self.request.user.profile.is_partner and self.request.user.profile.partner.iasme_cb:
                # for cb get all organisations of their clients
                organisation = get_organisations(
                    self.request.user, self.certification.organisation.secure_id, as_queryset=True
                ).first()
            else:
                # for admin staff get all organisations in the platform except cb
                organisation = Organisation.objects.filter(is_test=False).exclude(
                    partner__iasme_cb=True
                ).filter(secure_id=self.certification.organisation.secure_id).first()

            if not organisation:
                raise forms.ValidationError(_('You do not have access to this organisation'))

            if not self.certification.survey:
                raise forms.ValidationError(_('Certification does not have a survey'))

            if self.certification.version.pk != self.question.version.pk:
                raise forms.ValidationError(_('Certification and question have different version'))

        except OrganisationCertification.DoesNotExist:
            raise forms.ValidationError(_('Certification does not exist'))
        except SurveyQuestion.DoesNotExist:
            raise forms.ValidationError(_('Question does not exist'))

        return cleaned_data

    def save(self):
        # get or create survey assessment
        assessment, _ = SurveyAssessment.objects.get_or_create(
            survey=self.certification.survey
        )
        notes = AssessorNote.objects.filter(
            assessment__survey=self.certification.survey,
            question=self.question,
        )
        # accept all assessor notes and make them as re-answered
        notes.update(
            answer_accepted=True,
            re_answered=True
        )
        # update survey response as accepted
        SurveyResponse.objects.filter(
            survey=self.certification.survey,
            question=self.question
        ).update(assessor_acceptance=True)
        # return the last one accepted assessor note
        return notes.last()


class SurveyResponseEditForm(forms.Form):
    question_pk = forms.CharField(required=True, validators=[pervade_safe_char_validator])
    response_pk = forms.CharField(required=True, validators=[pervade_safe_char_validator])
    text = forms.CharField(required=True, validators=[pervade_safe_char_validator])
    boolean = forms.BooleanField(required=False)
    update_in_pervade = forms.BooleanField(required=False)

    def __init__(self, *args, **kwargs):
        self.response = None
        self.question = None
        self.certification = None
        self.cleaned_text = None
        self.boolean = None
        self.request = kwargs.pop('request')
        super(SurveyResponseEditForm, self).__init__(*args, **kwargs)

    def clean(self):
        cleaned_data = super(SurveyResponseEditForm, self).clean()
        try:
            self.response = SurveyResponse.objects.get(pk=cleaned_data.get('response_pk'))
            self.certification = self.response.survey.certificate
            self.question = SurveyQuestion.objects.filter_insurer_options(self.certification).get(
                pk=cleaned_data.get('question_pk'),
            )
            self.cleaned_text = handle_user_input(cleaned_data.get('text', ''))
            self.boolean = cleaned_data.get('boolean', None)

            if self.request.user.profile.is_partner and self.request.user.profile.partner.iasme_cb:
                # for cb get all organisations of their clients
                organisation = get_organisations(
                    self.request.user, self.certification.organisation.secure_id, as_queryset=True
                ).first()
            else:
                # for admin staff get all organisations in the platform except cb
                organisation = Organisation.objects.filter(is_test=False).exclude(
                    partner__iasme_cb=True
                ).filter(secure_id=self.certification.organisation.secure_id).first()

            if not organisation:
                raise forms.ValidationError(_('You do not have access to this organisation'))

            if not self.certification.survey:
                raise forms.ValidationError(_('Certification does not have a survey'))

            if self.certification.version.pk != self.question.version.pk:
                raise forms.ValidationError(_('Certification and question have different version'))

            if (
                    self.question.response_type == SurveyQuestion.BOOLEAN
            ) and (
                    self.question.widget == SurveyQuestion.WIDGET_DEFAULT
            ):
                # validate boolean answer
                if self.cleaned_text.lower() not in ("true", "false", "yes", "no", "off", "on"):
                    raise forms.ValidationError(_('The answer is not valid'))

            if self.question.widget == SurveyQuestion.WIDGET_ORGANISATION_TYPE_SELECT:
                if self.cleaned_text not in dict(Organisation.ORG_CHOICES).keys():
                    raise forms.ValidationError(_('Answer is not valid'))

        except SurveyResponse.DoesNotExist:
            raise forms.ValidationError(_('Response does not exist'))
        except OrganisationCertification.DoesNotExist:
            raise forms.ValidationError(_('Certification does not exist'))
        except SurveyQuestion.DoesNotExist:
            raise forms.ValidationError(_('Question does not exist'))

        return cleaned_data

    def save(self):
        assessment, _ = SurveyAssessment.objects.get_or_create(
            survey=self.certification.survey
        )
        response_type = self.question.response_type
        widget_type = self.question.widget

        # save simple text answers with no widget as usual
        if response_type == SurveyQuestion.TEXT and widget_type in (
                SurveyQuestion.WIDGET_DEFAULT,
                SurveyQuestion.WIDGET_ORGANISATION_SEARCH,
                SurveyQuestion.WIDGET_ORGANISATION_TYPE_SELECT,
                SurveyQuestion.WIDGET_ORGANISATION_IT,
                SurveyQuestion.WIDGET_FULL_NAME,
                SurveyQuestion.WIDGET_EMAIL,
                SurveyQuestion.WIDGET_FULL_NAME,
                SurveyQuestion.WIDGET_YES_NO_TEXT,
                SurveyQuestion.WIDGET_COMMON_REASONS,
                SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT,
                SurveyQuestion.WIDGET_ORGANISATION_ADDRESS
        ):
            self.response.choice = None
            self.response.value_text = self.cleaned_text

            self.response.not_applicable = False
            self.response.save()

        if widget_type in (SurveyQuestion.WIDGET_YES_NO_TEXT, SurveyQuestion.WIDGET_COMMON_REASONS,
                           SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT, SurveyQuestion.WIDGET_CONSCIOUS):
            # for this widgets we change only text value, boolean value stays the same
            self.response.choice = None
            self.response.value_text = self.cleaned_text
            self.response.value_boolean = self.boolean
            self.response.not_applicable = False
            self.response.save()

        # save pure "yes" & "no" answers as boolean
        if response_type == SurveyQuestion.BOOLEAN and widget_type != SurveyQuestion.WIDGET_CONSCIOUS:
            self.response.choice = None
            self.response.value_boolean = self.cleaned_text.lower() in ("true", "yes", "on")
            self.response.not_applicable = False
            self.response.save()
        # save numbers
        if response_type == SurveyQuestion.INTEGER:
            self.response.choice = None
            self.response.value_integer = int(self.cleaned_text)
            self.response.not_applicable = False
            self.response.save()
        # save money
        if response_type == SurveyQuestion.MONEY:
            self.response.choice = None
            self.response.value_text = '£'
            self.response.value_money = self.cleaned_text
            self.response.not_applicable = False
            self.response.save()
        # save choices answers as custom answer
        if response_type == SurveyQuestion.TEXT and widget_type in (
                SurveyQuestion.WIDGET_RADIO,
                SurveyQuestion.WIDGET_RADIO_SIMPLE,
                SurveyQuestion.WIDGET_RADIO_TEXTAREA,
        ):
            self.question.other_form = True
            self.response.choice = None
            self.response.value_text = self.cleaned_text
            self.response.not_applicable = False
            self.response.save()
            self.question.save()

        if self.cleaned_data.get("update_in_pervade"):
            # update answers in pervade
            if not settings.IS_TEST:
                from rulebook.pervade.tasks import update_answers_task
                partner_user_pk = self.request.user.partner.pk if getattr(self.request.user, "partner", None) else None
                update_answers_task.delay(self.certification.pk, partner_user_pk)
                self.response.updated_in_pervade = True
                self.response.save()

        return self.response.humanize_response_value


class SelectCertificationForm(forms.Form):
    """
    Certification version
    """
    certification = forms.ModelChoiceField(
        queryset=CertificationVersion.objects.all(),
        widget=forms.Select(attrs={'class': 'form-control'}),
        required=True
    )
    pervade_token = forms.CharField(
        label=_('Pervade token'), required=False, help_text=_("Not required"),
        widget=forms.Textarea(attrs={'class': 'form-control'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not settings.IS_PROD:
            self.fields['add_to_db'] = forms.BooleanField(
                label=_('Replace survey questions details in DB (CE+ only)?'), required=False, widget=forms.CheckboxInput(),
                help_text=_("This will override all existing questions details (e.g. title, pervade ID, etc) for the certificate"),
            )
