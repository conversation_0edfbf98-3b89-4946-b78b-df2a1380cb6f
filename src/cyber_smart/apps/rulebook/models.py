from __future__ import unicode_literals

import logging
import re
import uuid
from datetime import date, datetime
from decimal import Decimal, InvalidOperation

import six
from ckeditor_uploader.fields import RichTextUploadingField
from dateutil.relativedelta import relativedelta
from django.contrib.auth import get_user_model
from django.core.files.base import File
from django.core.validators import validate_email
from django.db import models
from django.db.models import Count, Prefetch, Q, Exists, OuterRef
from django.forms import ValidationError
from django.shortcuts import reverse
from django.utils import timezone
from django.utils.encoding import force_str
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _
from model_utils.models import TimeStampedModel
from versionfield import VersionField

from common.data import EU_EEA_COUNTRIES, UK_COUNTRIES
from common.models import CRUDManager, CRUDSignalMixin
from common.upload_path_creator import (
    upload_file_securely, upload_to_declaration, upload_to_user_certification,
    upload_to_user_insurance, upload_to_user_report,
)
from insurance import insurers
from insurance.models import InsurerMixin
from insurance.utils import get_opt_in_survey_response
from .fields import CINullCharField, NonStrippingCharField
from .managers import SurveyQuestionInsurerManager

logger = logging.getLogger(__name__)

CYBER_ESSENTIALS = 1
CYBER_ESSENTIALS_PLUS = 2
IASME_GOVERNANCE = 3
GDPR = 4
HEALTH_CHECK = 5
CYBERSMART_COMPLETE = 6
IASME_CYBER_ASSURANCE = 7
ASSURANCE_GUARANTEE = 8
CERT_NZ = 9
ESSENTIAL_EIGHT = 10
NIS2 = 11

MIGRATION_ALLOWED_TYPES = [CYBER_ESSENTIALS]
CERTIFICATION_TYPES_WITH_DECLARATION_SIGNING_EMAILS = [CYBER_ESSENTIALS, GDPR, ESSENTIAL_EIGHT]

CYBER_ESSENTIALS_VERBOSE = 'Cyber Essentials'
CYBER_ESSENTIALS_PLUS_VERBOSE = 'Cyber Essentials Plus'
IASME_GOVERNANCE_VERBOSE = 'IASME Governance'
GDPR_VERBOSE = 'GDPR'
HEALTH_CHECK_VERBOSE = "Health Check"
CYBERSMART_COMPLETE_VERBOSE = "CyberSmart Complete"
IASME_CYBER_ASSURANCE_VERBOSE = "IASME Cyber Assurance"
ASSURANCE_GUARANTEE_VERBOSE = "Assurance Guarantee"
CERT_NZ_VERBOSE = "Cert NZ"
ESSENTIAL_EIGHT_VERBOSE = "Essential Eight"
NIS2_VERBOSE = _("NIS2 Guidance Questionnaire")

CERTIFICATE_TYPES = (
    (CYBER_ESSENTIALS, CYBER_ESSENTIALS_VERBOSE),
    (CYBER_ESSENTIALS_PLUS, CYBER_ESSENTIALS_PLUS_VERBOSE),
    (IASME_GOVERNANCE, IASME_GOVERNANCE_VERBOSE),
    (GDPR, GDPR_VERBOSE),
    (HEALTH_CHECK, HEALTH_CHECK_VERBOSE),
    (CYBERSMART_COMPLETE, CYBERSMART_COMPLETE_VERBOSE),
    (IASME_CYBER_ASSURANCE, IASME_CYBER_ASSURANCE_VERBOSE),
    (ASSURANCE_GUARANTEE, ASSURANCE_GUARANTEE_VERBOSE),
    (CERT_NZ, CERT_NZ_VERBOSE),
    (ESSENTIAL_EIGHT, ESSENTIAL_EIGHT_VERBOSE),
    (NIS2, NIS2_VERBOSE)
)

CERTIFICATES = dict(CERTIFICATE_TYPES)

CERTIFICATE_SHORT_TYPES = {
    CYBER_ESSENTIALS: "CE",
    CYBER_ESSENTIALS_PLUS: "CE+",
    IASME_GOVERNANCE: "IASME",
    GDPR: "GDPR",
    HEALTH_CHECK: "HC",
    CYBERSMART_COMPLETE: "CS",
    ASSURANCE_GUARANTEE: "AG",
    CERT_NZ: "CNZ",
    ESSENTIAL_EIGHT: "EE",
    NIS2: "NIS2"
}


class CertificateType(TimeStampedModel):
    """
    This model represents certification type either CE, CE+, IASME, GDPR, NIS2, etc..
    """
    type = models.SmallIntegerField(choices=CERTIFICATE_TYPES)
    logo = models.ImageField(
        upload_to='certification_type_images/', null=True, blank=True,
        help_text='Image that is displayed in frontend.'
    )

    class Meta:
        verbose_name = 'Certificate type'
        verbose_name_plural = 'Certificate types'

    def __unicode__(self):
        return self.get_type_display()

    def __str__(self):
        return self.__unicode__()

    @property
    def logo_url(self):
        return self.logo.url if self.logo else ''


class CertificationSurvey(TimeStampedModel, CRUDSignalMixin):
    """
    This model represents certification survey (questionnaire).
    """
    certificate = models.OneToOneField(
        'organisations.OrganisationCertification', related_name='survey', on_delete=models.CASCADE
    )
    datetime_started = models.DateTimeField()
    passed_percent = models.PositiveIntegerField(verbose_name='Passed percentage', default=0)
    migrated_to_iasme_insurance = models.BooleanField(
        verbose_name="Migrated to IASME insurance", default=False,
        help_text="Indicates whether the survey has been migrated from SuperScript to IASME insurance questions."
    )

    class Meta:
        verbose_name = 'Certification\'s Survey'
        verbose_name_plural = 'Certification\'s Surveys'

    def __unicode__(self):
        return "Survey: {0}".format(
            self.certificate  # pylint: disable=no-member
        )

    def __str__(self):
        return self.__unicode__()

    @property
    def datetime_completed(self):
        if hasattr(self, 'declaration'):
            return self.declaration.declaration_date
        else:
            return self.modified

    def calculate_passed_percent(self):
        """
        Calculates survey passed percentage and saves it.
        :return: passed percentage
        :rtype: int
        """
        from rulebook.utils import get_questionnaire_status_structure

        structure_for_topics = get_questionnaire_status_structure(self.certificate, key_as_pk=True)
        structure_for_passed_percent = get_questionnaire_status_structure(
            self.certificate, key_as_pk=True, skip_not_required=True
        )
        if len(structure_for_topics) > 0:
            percent = int(
                100 * list(structure_for_passed_percent.values()).count(True) / len(structure_for_passed_percent)
            )
        else:
            percent = 0
        # save and return
        self.passed_percent = percent
        self.save()
        return percent

    def get_questions(self):
        qs = SurveyQuestion.objects.none()
        for topic in self.certificate.version.topics.all():
            qs = qs | topic.questions.all()
        return qs

    def create_empty_responses(self):
        if not self.responses.exists():
            SurveyResponse.objects.bulk_create([
                SurveyResponse(
                    survey=self,
                    question_id=question_pk,
                    not_applicable=True
                ) for question_pk in self.get_questions().values_list(
                    'pk', flat=True
                )
            ])

    def get_answer_for_code(
            self, question_code: str, answer_code: str = None, exclude_answer_code: str = None
    ) -> str:
        """
        Retrieves the answer value for a given question code.

        This method filters answers based on the provided `question_code`. If `answer_code` is provided,
        it will further filter to get the specific answer. If `exclude_answer_code` is provided,
        it will exclude that particular answer from the results.

        Args:
            question_code (str): The code of the question to retrieve the answer for.
            answer_code (str, optional): The specific answer code to filter by. Defaults to None.
            exclude_answer_code (str, optional): The answer code to exclude from the results. Defaults to None.

        Returns:
            str: The value of the answer if found, otherwise an empty string.
        """
        query_filters = {"question__code": question_code}
        query_exclusions = {}

        if answer_code:
            query_filters["code"] = answer_code

        if exclude_answer_code:
            query_exclusions["code"] = exclude_answer_code

        answer = self.responses.filter(**query_filters).exclude(**query_exclusions).first()

        return answer.value if answer else ""

    def get_answer_for_pervade(self, question_title: str) -> str:
        """
        Returns answer value for question with given pervade title.
        """
        answer = self.responses.filter(question__pervade_title=question_title).first()
        if answer:
            return answer.value
        return ""



class SurveyDeclaration(TimeStampedModel, CRUDSignalMixin):
    """
    This model represents survey declaration.
    """
    survey = models.OneToOneField(CertificationSurvey, related_name='declaration', on_delete=models.CASCADE)
    declaration_date = models.DateTimeField(help_text='Timestamp of when the declaration was created or updated.')
    declaration_name = models.CharField(max_length=250)
    declaration_job = models.CharField(max_length=250, blank=True)
    declaration_email = models.EmailField(verbose_name='Declaration Email', null=True, blank=True)
    declaration_secondary_email = models.EmailField(verbose_name='Declaration Secondary Email', null=True, blank=True)
    signature = models.FileField(upload_to='signature/', null=True, blank=True)
    pdf = models.FileField(upload_to=upload_to_declaration, null=True, blank=True, max_length=1000)
    html_report = models.TextField(verbose_name='Html report', null=True, blank=True)
    ready_to_be_signed = models.BooleanField(verbose_name='Declaration is ready to be signed', default=True)
    date_signed = models.DateTimeField(null=True, blank=True, help_text='Timestamp of when the declaration was signed or re-signed.')

    class Meta:
        verbose_name = 'Survey\'s Declaration'
        verbose_name_plural = 'Survey\'s Declarations'

    def __unicode__(self):
        return "{} survey [{}] declaration".format(
            self.survey.certificate.organisation.name, self.survey.id  # pylint: disable=no-member
        )

    def __str__(self):
        return self.__unicode__()

    @property
    def html_report_url(self):
        """
        Returns html report url
        :return: url
        :rtype: str
        """
        if self.survey.certificate.is_multiple:  # pylint: disable=no-member
            kwargs = {
                'org_id': self.survey.certificate.organisation.secure_id,  # pylint: disable=no-member
                'type': self.survey.certificate.version.type.type,  # pylint: disable=no-member
                'version_pk': self.survey.certificate.version.pk  # pylint: disable=no-member
            }
        else:
            kwargs = {
                'org_id': self.survey.certificate.organisation.secure_id,  # pylint: disable=no-member
                'type': self.survey.certificate.version.type.type  # pylint: disable=no-member
            }
        return reverse('dashboard:show-certification-html-report', kwargs=kwargs)

    @property
    def submitted_date(self):
        return self.created


class IssuedCertification(TimeStampedModel, CRUDSignalMixin):
    """
    This model stores issued certification files uploaded from Pervade.
    """
    certificate = models.OneToOneField(
        'organisations.OrganisationCertification', related_name='issued_certification', on_delete=models.CASCADE
    )
    date = models.DateTimeField(null=True, blank=True, help_text='When the certificate was issued (certified date).')
    number = models.CharField(
        max_length=250, null=True, blank=True, help_text='Unique identifier for the issued certificate.'
    )
    insurance_number = models.CharField(
        max_length=250, null=True, blank=True, help_text='Unique identifier for the issued evidence of insurance.'
    )
    certificate_file = models.FileField(
        upload_to=upload_to_user_certification, null=True, blank=True, max_length=1000)
    report_file = models.FileField(
        upload_to=upload_to_user_report, null=True, blank=True, max_length=1000)
    insurance_file = models.FileField(
        upload_to=upload_to_user_insurance, null=True, blank=True, max_length=1000,
        help_text='Insurance file coming from Pervade immediately after submit of survey.')
    evidence_of_insurance_file = models.FileField(
        upload_to=upload_to_user_insurance, null=True, blank=True, max_length=1000,
        help_text='Evidence of Insurance file coming through email from IASME.'
        )
    custom_cert_sent = models.BooleanField(
        verbose_name='Custom cert manually sent', default=False, help_text='For internal use'
    )
    custom_cert_date = models.DateTimeField(
        verbose_name='Custom cert date sent', help_text='For internal use', null=True, blank=True
    )
    assessor = models.ForeignKey(
        to=get_user_model(), verbose_name='Assessor', null=True, blank=True, on_delete=models.SET_NULL
    )

    blockmark_certificate_registration_url = models.URLField(
        default='',
        blank=True,
        help_text=(
            "Link to access BlockMark and claim the certificate. Provided by IASME upon"
            " certification completion."
        ),
        max_length=1500
    )
    blockmark_certificate_forwarding_url = models.URLField(
        default='',
        blank=True,
        help_text=(
            "Link to forward the certificate via BlockMark to another recipient, e.g.,"
            " organization director. Provided by IASME upon certification completion."
        ),
        max_length=1500
    )
    blockmark_evidence_of_insurance_registration_url = models.URLField(
        default='',
        blank=True,
        help_text=(
            "Link to access BlockMark and claim the evidence of insurance. Provided by"
            " IASME upon certification completion."
        ),
        max_length=1500
    )
    blockmark_evidence_of_insurance_forwarding_url = models.URLField(
        default='',
        blank=True,
        help_text=(
            "Link to forward the evidence of insurance via BlockMark to another recipient,"
            " e.g., organization director. Provided by IASME upon certification completion."
        ),
        max_length=1500
    )


    objects = CRUDManager()

    class Meta:
        verbose_name = 'Issued Certification'
        verbose_name_plural = 'Issued Certifications'

    def __unicode__(self):
        return "{0} Issued Certification".format(self.certificate.organisation.name)  # pylint: disable=no-member

    def __str__(self):
        return self.__unicode__()

    @property
    def insurer_type_when_issued(self):
        """
        Returns insurer type based on which opt-in question
        has a response.
        For Superscript-
        question number 61
        For IASME -
        2018 - question number 60
        > 2018 - question number 62
        :return:
        insurers.IASME_INSURER,
        insurers.SUPERSCRIPT_INSURER,
        insurers.NO_INSURER if no opt-in response was found,
        or None if there is more than one opt-in response found (inconclusive).
        :rtype: int
        """
        insurer = insurers.NO_INSURER

        if get_opt_in_survey_response(insurers.IASME_INSURER, self.certificate.survey):
            insurer = insurers.SUPERSCRIPT_INSURER
        if get_opt_in_survey_response(insurers.SUPERSCRIPT_INSURER, self.certificate.survey):
            if insurer == insurers.NO_INSURER:
                insurer = insurers.IASME_INSURER
            else:
                # more than one insurer opt-in response found
                insurer = None
        return insurer

    @classmethod
    def get_issued_certificates_for_renewal(cls) -> models.QuerySet['IssuedCertification']:
        """
        Retrieves certificates eligible for renewal - issued approximately 11 months prior to the current date,
        considering any variations due to leap years.
        We are migrating certificates 11 months after the previous certification.
        If a customer was certified on 25/04/2021 and the current date is 25/03/2022
        then this will get their issued certificate, since it has been exactly 11 months.
        """
        # current date
        current_date = timezone.now().date()
        date_11_months_ago = current_date + relativedelta(months=-11)

        date_over_11_months_ago = current_date + relativedelta(months=-11, days=-1)
        issued_certificates = cls.objects.filter(
            # To avoid any missed dates (leap years etc) include the day before.
            # If cert is picked up multiple times nothing will happen
            date__date__lte=date_11_months_ago,
            # get exact date
            date__date__gte=date_over_11_months_ago,
            # cover the previous day too, to be safe
            certificate__version__type__type__in=MIGRATION_ALLOWED_TYPES
        )
        return issued_certificates

    @property
    def either_insurance_file(self):
        """
        Returns the evidence of insurance file if it exists, otherwise returns the insurance file.
        """
        return self.evidence_of_insurance_file or self.insurance_file

    @property
    def blockmark_registration_url(self):
        return self.blockmark_certificate_registration_url

    @property
    def blockmark_forwarding_url(self):
        return self.blockmark_certificate_forwarding_url

class CertificationVersion(TimeStampedModel):
    """
    This model represents certification version e.g 2018, 2019, 2020 etc
    """
    type = models.ForeignKey(
        to=CertificateType, verbose_name='Certificate type', on_delete=models.PROTECT
    )
    version_number = models.FloatField()
    default = models.BooleanField(verbose_name='Default standard version', default=False)
    release_date = models.DateField()
    badges_file_upload = models.FileField(upload_to='certification_version_badges_file_upload/', null=True, blank=True)
    max_minors = models.SmallIntegerField(verbose_name='Maximum minor fails', default=0)
    max_majors = models.SmallIntegerField(verbose_name='Maximum major fails', default=0)
    max_fails = models.SmallIntegerField(verbose_name='Maximum fails', default=0)
    auto_approve = models.BooleanField(verbose_name='Auto approve for issue certification', default=False)
    declaration = models.BooleanField(verbose_name='Declaration page', default=True)
    declaration_text = models.TextField(verbose_name='Declaration text', null=True, blank=True)
    declaration_insurance_text = models.TextField(verbose_name='Declaration insurance text', null=True, blank=True)
    readiness_pack = models.FileField(verbose_name='Readiness pack', upload_to=upload_file_securely, max_length=255,
                                      blank=True, null=True)
    readiness_html = RichTextUploadingField(verbose_name='Readiness report', null=True, blank=True)
    pervade_title = NonStrippingCharField(
        verbose_name='Pervade certification title', null=True, blank=True, max_length=255
    )

    class Meta:
        verbose_name = 'Certification Version'
        verbose_name_plural = 'Certification Versions'

    def __unicode__(self):
        return "v{0} - {1}".format(self.version_number, self.type)

    def __str__(self):
        return self.__unicode__()

    VERSION_TYPES_REQUIRED_IN_ONBOARDING = [CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, GDPR, HEALTH_CHECK]

    def clean(self):
        from rulebook.utils import get_default_versions
        _type = self.type.type
        if (
            _type in self.VERSION_TYPES_REQUIRED_IN_ONBOARDING
            and (self.default is False or self.release_date > timezone.now().date())
        ):
            latest_versions = get_default_versions(type=_type).exclude(id=self.id)
            if latest_versions.count() == 0:
                raise ValidationError('''
                    With these changes, there would be no more default versions for this type.
                    This would break new organisations creation which requires a default certification version to be specified.
                    Please ensure, there is at least one active version for the type and retry.
                    (Note: multiple default versions are allowed, and the one with the closest past release_date will be chosen.)
                ''')


class AppCheck(TimeStampedModel):
    """
    This model represents checks that should be checked on installed applications for security report.
    """
    API = 'API'
    INAPP = 'INA'
    WEB = 'WEB'
    Q_TYPE_CHOICES = (
        (API, 'API Based (Check Command)'),
        (INAPP, 'In App (Custom)'),
        (WEB, 'WEB')
    )
    MOBILE = 'MOBILE'
    DESKTOP = 'DESKTOP'
    PLATFORM_TYPE_CHOICES = (
        ('', 'All Platforms'),
        (MOBILE, 'Mobile'),
        (DESKTOP, 'Desktop'),
    )
    order = models.IntegerField(blank=True, null=True)
    code = models.CharField(max_length=50, unique=True)
    qtype = models.CharField(max_length=3, choices=Q_TYPE_CHOICES, default=API)
    title = models.CharField(max_length=250)
    version = models.ManyToManyField(CertificationVersion, related_name='app_checks')
    tooltip = models.TextField(
        blank=True,
        help_text=_("An in-depth explanation of the check to be shown in Active Protect")
    )
    extra_info = models.TextField(
        blank=True,
        help_text=_("Information to be shown next to check in check and device reports")
    )
    survey_answer = models.TextField(
        blank=True,
        help_text=_("Text to be auto-filled in a survey question if this check passes on all devices")
    )
    summary_guide = RichTextUploadingField(
        verbose_name='Summary guide',
        config_name='default',
        null=True,
        blank=True
    )
    active = models.BooleanField(
        verbose_name='Active',
        default=True,
        help_text=('If not active it will be hidden for desktop/mobile '
                   'apps and web dashboard. Also this will not be taken into account in statistic')
    )

    platform_type = models.CharField(max_length=8, blank=True, choices=PLATFORM_TYPE_CHOICES)

    class Meta:
        verbose_name = 'App Check'
        verbose_name_plural = 'App Checks'
        ordering = ('order',)

    def __unicode__(self):
        return self.title

    def __str__(self):
        return self.__unicode__()

    @property
    def is_trustd_check(self):
        """ If the AppCheck is part of the Trustd Mobile App """
        return hasattr(self, 'indicator_aggregation')


class OperatingSystem(TimeStampedModel):
    """
    This model represents operating system.
    """
    os_id = models.CharField(max_length=20, null=True, blank=True)
    title = models.CharField(max_length=250)
    is_supported = models.BooleanField(default=True)

    class Meta:
        verbose_name = 'Operating System'
        verbose_name_plural = 'Operating Systems'

    def __unicode__(self):
        return self.title

    def __str__(self):
        return self.__unicode__()

    def get_secure_percentage(self, organisation=None, industry=None):
        """
        Returns average secure percentage for this operating system
        :param industry: filter also by organisation industry
        :type industry: str or unicode
        :param organisation: filter also by organisation
        :type organisation: organisations.Organisation instance
        :return: secure percentage or None
        :rtype: float or None
        """
        filters = {
            'inactive': False,
            'app_user__active': True,
            'app_user__enroll': True,
            'app_user__organisation__is_test': False,
            'analytics__latest_pass_percentage__isnull': False
        }
        if industry:
            filters['app_user__organisation__industry'] = industry
        percentages = self.appinstall_set.filter(
            **filters
        ).values_list('analytics__latest_pass_percentage', flat=True)
        if percentages:
            return sum(percentages) / len(percentages)
        else:
            return 0

    def get_insecure_percentage(self, organisation=None, industry=None):
        """
        Returns average insecure percentage for this operating system
        :param industry: filter also by organisation industry
        :type industry: str or unicode
        :param organisation: filter also by organisation
        :type organisation: organisations.Organisation instance
        :return: insecure percentage or None
        :rtype: float or None
        """
        return 100 - self.get_secure_percentage(organisation, industry)


class OSPlatformRelease(TimeStampedModel):
    """
    This model represents operating system release.
    """
    os = models.ForeignKey(OperatingSystem, on_delete=models.CASCADE)
    platform = models.CharField(max_length=250)
    release = models.CharField(max_length=250)
    os_build = models.CharField(verbose_name='OS build number', max_length=250, null=True, blank=True)
    is_required = models.BooleanField(default=False)
    is_supported = models.BooleanField(default=True)

    OS_PLATFORMS = {
        'IOS': 'ios',
        'MACOS': 'darwin',
        'ANDROID': 'android',
        'WINDOWS': 'win32'
    }

    class Meta:
        verbose_name = 'OS Platform Release'
        verbose_name_plural = 'OS Platform Releases'

    def __unicode__(self):
        return "{0} {1}".format(self.platform, self.release)

    def __str__(self):
        return self.__unicode__()

    def is_android(self):
        """
        Returns True if it is an Android operating system release otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.platform == self.OS_PLATFORMS['ANDROID']

    def is_ios(self):
        """
        Returns True if it is an iOS operating system release otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.platform == self.OS_PLATFORMS['IOS']

    def is_macos(self):
        """
        Returns True if it is an MacOS operating system release otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.platform == self.OS_PLATFORMS['MACOS']

    def is_windows(self):
        """
        Returns True if it is an Windows operating system release otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.platform == self.OS_PLATFORMS['WINDOWS']

    @property
    def cpe(self):
        """
        Returns Common Platform Enumeration for this release.
        It then can be used for cve searching.
        :return: Common Platform Enumeration
        :rtype: str or unicode
        """
        if self.platform == self.OS_PLATFORMS['WINDOWS']:
            # windows
            return 'cpe:2.3:o:microsoft:{0}:{1}'.format(self.os.title.replace(' ', '_'), self.release or '-')
        elif self.platform == self.OS_PLATFORMS['MACOS']:
            # macos
            return 'cpe:2.3:o:apple:mac_os_x:{0}'.format(self.release or '-')
        elif self.platform == self.OS_PLATFORMS['ANDROID']:
            # android
            return 'cpe:2.3:o:google:android:{0}'.format(self.release or '-')
        elif self.platform == self.OS_PLATFORMS['IOS']:
            # ios
            return 'cpe:2.3:o:apple:iphone_os:{0}'.format(self.release or '-')
        else:
            # for unknown operating systems
            return '{0}:{1}'.format(self.os.title.replace(' ', '_'), self.release)


class OSCheck(TimeStampedModel):
    name = models.CharField(max_length=50, unique=True)
    supported = models.BooleanField(default=True)
    end_of_life = models.DateField(null=True, blank=True)

    class Meta:
        verbose_name = 'OS Check'
        verbose_name_plural = 'OS Checks'


class OSCheckRule(TimeStampedModel):
    rule_choices = [
        ('platform_contains', 'Platform contains'),
        ('release_contains', 'Release contains'),
        ('build_gte', 'Build greater than or equal to'),
        ('build_contains', 'Build contains'),
        ('version_gte', 'Version greater than or equal to'),
        ('kernel_gte', 'Kernel greater than or equal to'),
        ('manufacturer_list', 'Device manufacturer is in'),
        ('model_list', 'Device model is in'),
    ]
    os_check = models.ForeignKey(OSCheck, on_delete=models.CASCADE, related_name='rules')
    rule = models.CharField(max_length=50, choices=rule_choices)
    value = models.CharField(max_length=128)

    class Meta:
        verbose_name = 'OS Check Rule'
        verbose_name_plural = 'OS Check Rules'


class Command(TimeStampedModel, CRUDSignalMixin):
    """
    This model represents a command that have check_command fix_command and fix_guide.
    check_command - will be executed on user machine to get check result
    fix_command - may be used to fix possible problem
    fix_guide - is a html guide to help user perform needed actions
    """
    app_check = models.ForeignKey(AppCheck, related_name='commands', on_delete=models.CASCADE)
    os = models.ForeignKey(OperatingSystem, on_delete=models.CASCADE)
    check_command = models.TextField()
    expected_response = models.TextField()
    fix_command = models.TextField()
    fix_guide = RichTextUploadingField(blank=True, null=True)
    manual_only = models.BooleanField(default=True)
    min_app_version = VersionField(default="0")

    class Meta:
        verbose_name = 'Command'
        verbose_name_plural = 'Commands'
        ordering = ('app_check',)

    def __unicode__(self):
        return "<command for {0}>".format(self.app_check.title)

    def __str__(self):
        return self.__unicode__()


class BulkSolution(TimeStampedModel):
    """
    This model is not used anymore.
    """
    # todo: remove it
    OS_CHOICE = (
        ('WIN', 'Windows Fix'),
        ('MAC', 'MacOS Fix'),
    )
    app_check = models.ForeignKey(AppCheck, on_delete=models.CASCADE)
    os = models.ManyToManyField(OperatingSystem)
    fix_choice = models.CharField(max_length=3, choices=OS_CHOICE)
    fix_guide = RichTextUploadingField(blank=True, null=True)

    class Meta:
        verbose_name = 'Bulk Solution'
        verbose_name_plural = 'Bulk Solutions'
        ordering = ('app_check',)

    def __unicode__(self):
        return "<bulk_solution for {}>".format(self.app_check.title)

    def __str__(self):
        return self.__unicode__()


class SurveyQuestionTopic(TimeStampedModel):
    """
    This model represents survey topic.
    """
    title = models.CharField(verbose_name='Title', max_length=255)
    order = models.IntegerField(blank=True, null=True)
    description = RichTextUploadingField(verbose_name='description', null=True, blank=True)
    version = models.ForeignKey(
        to=CertificationVersion, related_name='topics', null=True, blank=True, on_delete=models.CASCADE
    )
    iasme_number = models.CharField(verbose_name="IASME number", max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = 'Question Topic'
        verbose_name_plural = 'Questions Topics'
        ordering = ['order']

    def __unicode__(self):
        return '{0}) {1} | {2}'.format(self.order, self.title, self.version)

    def __str__(self):
        return self.__unicode__()


class QuestionWidget(models.Model):
    """
    Model mixin that adds widgets to the survey question model.
    """
    WIDGET_DEFAULT = 0
    WIDGET_RADIO = 1
    WIDGET_CHECKBOX = 2
    WIDGET_RADIO_CHECKBOX = 3
    WIDGET_ORGANISATION_SEARCH = 4
    WIDGET_ORGANISATION_TYPE_SELECT = 5
    WIDGET_ORGANISATION_ADDRESS = 6
    WIDGET_ORGANISATION_IT = 7
    WIDGET_RADIO_TEXTAREA = 8
    WIDGET_YES_NO_KEEP_OPTIONS = 9
    WIDGET_MONEY = 10
    WIDGET_EMAIL = 11
    WIDGET_FULL_NAME = 12
    WIDGET_YES_NO_TEXT = 13
    WIDGET_COMMON_REASONS = 14
    WIDGET_CONSCIOUS = 15
    WIDGET_ORGANISATION_REGISTRATION = 16
    WIDGET_YES_NO_OPTIONAL_TEXT = 17
    WIDGET_RADIO_SIMPLE = 18

    WIDGETS = (
        (WIDGET_DEFAULT, "Default"),
        (WIDGET_RADIO, "Radio"),
        (WIDGET_CHECKBOX, "Checkbox"),
        (WIDGET_RADIO_CHECKBOX, "Radio Checkbox"),
        (WIDGET_ORGANISATION_SEARCH, "Organisation Search"),
        (WIDGET_ORGANISATION_TYPE_SELECT, "Organisation Type Select"),
        (WIDGET_ORGANISATION_ADDRESS, "Organisation Address"),
        (WIDGET_ORGANISATION_IT, "Organisation IT"),
        (WIDGET_RADIO_TEXTAREA, "Radio TextArea"),
        (WIDGET_YES_NO_KEEP_OPTIONS, "Yes/No (keep options)"),
        (WIDGET_MONEY, "Money + Currency"),
        (WIDGET_EMAIL, "Email"),
        (WIDGET_FULL_NAME, "Full name"),
        (WIDGET_YES_NO_TEXT, "Yes/No + Text"),
        (WIDGET_COMMON_REASONS, "Yes/No + Text + Common reasons"),
        (WIDGET_CONSCIOUS, "Conscious widget (no pre filled data)"),
        (WIDGET_ORGANISATION_REGISTRATION, "Organisation Registration Number"),
        (WIDGET_YES_NO_OPTIONAL_TEXT, "Yes/No + Optional text"),
        (WIDGET_RADIO_SIMPLE, "Radio (without Yes/No)"),
    )

    WIDGET_ORGANISATION_ADDRESS_FIELDS = ['address1', 'address2', 'city', 'county', 'postcode', 'country']
    WIDGET_ORGANISATION_IT_FIELDS = ['name', 'role']
    WIDGET_FULL_NAME_FIELDS = ['firstname', 'lastname']

    WIDGETS_TO_FIELDS = {
        WIDGET_ORGANISATION_ADDRESS: WIDGET_ORGANISATION_ADDRESS_FIELDS,
        WIDGET_ORGANISATION_IT: WIDGET_ORGANISATION_IT_FIELDS,
        WIDGET_FULL_NAME: WIDGET_FULL_NAME_FIELDS,
    }

    widget = models.IntegerField(
        verbose_name="Widget", choices=WIDGETS, default=WIDGET_DEFAULT
    )

    # Handle questions for Conscious Widget.
    # This should replace all yes/no + text widgets and other complex logic
    YES = 1
    NO = 2
    ANY = 3
    NA = 4
    COMPLIANT_ANSWERS = (
        (YES, "Yes"),
        (NO, "No"),
        (ANY, "Yes or No (both are compliant)"),
        (NA, "Not applicable"),
    )
    compliant_answer = models.SmallIntegerField(
        verbose_name='Which is the compliant/accepted answer', default=NA, choices=COMPLIANT_ANSWERS
    )
    show_text_box = models.BooleanField(
        verbose_name='Show text box',
        help_text='Show a text box where user can provide further details',
        default=False
    )
    text_box_is_required = models.BooleanField(
        verbose_name='Text box is required to be filled',
        help_text='If text box is required "Please explain" will be shown else it will be "Optional explanation"',
        default=False,
    )

    class Meta:
        abstract = True


class PervadeMixin(models.Model):
    """
    Extends the survey question model with Pervade specific fields and methods.
    """
    pervade_title = NonStrippingCharField(verbose_name='Pervade question title', null=True, blank=True, max_length=255)
    pervade_id = models.CharField(verbose_name='Pervade question id', max_length=255, null=True, blank=True)
    pervade_reverse_value = models.BooleanField(verbose_name='Pervade reverse response value', default=False)
    pervade_save_applicant_notes = models.BooleanField(
        verbose_name='Save Yes/No to answer field and text value to "Applicant Notes"', default=False
    )
    pervade_save_text_to_answer = models.BooleanField(
        verbose_name='Save text value to answer field (regardless of widget)', default=False
    )
    pervade_compliance_fields = models.JSONField(verbose_name="Pervade compliance fields", default=dict, blank=True)
    pervade_ignored = models.BooleanField(
        verbose_name="Pervade ignored", default=False, help_text="Ignore this question when sending to Pervade"
    )

    class Meta:
        abstract = True

    @property
    def pervade_num(self) -> str | float:
        """
        Returns the question number, eg, "A2.6" or question order number.
        """
        if self.pervade_title:
            prefix: str = self.pervade_title.split(" ")[0]
            if prefix and not prefix.isalpha():
                # The CE 2021 questions require an additional dot in the question title, eg, "A2.6." vs "A2.6"
                # this breaks our javascript, so we strip it if it exists.
                return prefix.rstrip(".")
        return self.order


class SurveyQuestion(TimeStampedModel, PervadeMixin, QuestionWidget, InsurerMixin, CRUDSignalMixin):
    """
    This model represents survey question.
    """
    TEXT = 'text'
    INTEGER = 'integer'
    BOOLEAN = 'boolean'
    FLOAT = 'float'
    DATE = 'date'
    DATETIME = 'datetime'
    FILE = 'file'
    IMAGE = 'image'
    MONEY = "money"

    RESPONSE_TYPE_CHOICES = (
        (TEXT, 'Text'),
        (INTEGER, 'Integer'),
        (BOOLEAN, 'True / False'),
        (FLOAT, 'Float'),
        (DATE, 'Date'),
        (DATETIME, 'Datetime'),
        (FILE, 'File'),
        (IMAGE, 'Image'),
        (MONEY, "Money")
    )

    RANKING_NONE = 0
    RANKING_MINOR = 1
    RANKING_MAJOR = 2
    RANKING_FAIL = 3
    RANKING_CHOICES = (
        # This should be None, it just means the answer has no scoring
        (RANKING_NONE, 'Info Only'),
        (RANKING_MINOR, 'Minor'),
        (RANKING_MAJOR, 'Major'),
        (RANKING_FAIL, 'Auto Fail'),
    )

    order = models.FloatField(blank=True, null=True, default=None)
    response_type = models.CharField(
        choices=RESPONSE_TYPE_CHOICES, default=TEXT, max_length=20, verbose_name='Response Type'
    )
    relatives = models.ManyToManyField(
        to='self',
        verbose_name='Question relatives',
        blank=True
    )
    topic = models.ForeignKey(
        to=SurveyQuestionTopic,
        verbose_name='topic',
        related_name='questions',
        null=True, blank=True,
        on_delete=models.CASCADE
    )
    required = models.BooleanField('Required', default=True)
    mandatory_for_parent_choices = models.ManyToManyField(
        verbose_name="Mandatory for parent choices",
        help_text="If any of these choices are selected, this question will be mandatory to answer",
        to="rulebook.SurveyQuestionChoices",
        related_name="mandatory_questions",
        blank=True
    )
    version = models.ForeignKey(to=CertificationVersion, null=True, blank=True, on_delete=models.CASCADE)
    parent = models.ForeignKey('self', blank=True, null=True, related_name='children', on_delete=models.CASCADE)
    parent_2 = models.ForeignKey(
        'self', blank=True, null=True, related_name='children_2', on_delete=models.CASCADE,
        help_text='If the parent_2\'s value is equal to show_children_on_2 value, then this current element will be shown.'
    )
    considered_as_valid = models.BooleanField(
        verbose_name='Response considered as valid if it equals to',
        default=None,
        help_text='''
        Response considered as valid if response converted to boolean equals to True (Yes) or False (No),
        if None (Unknown) this option does not affect to anything.
        ''',
        null=True,
        blank=True
    )
    hide_choices = models.BooleanField(verbose_name='By default hide choices on frontend', default=True)
    other_form = models.BooleanField(
        verbose_name="Custom Input Form", default=True, null=True, blank=True,
        help_text="If checked, the custom text input form will be shown on the frontend."
                  "With a multiple choice widget, it will be shown when a specific choice selected."
                  "You can mark specific choice as 'Custom Input' in the choices section below."
    )
    other_form_label = models.CharField(
        verbose_name="Custom Input Form Label", max_length=255, default="Other",
        help_text="Label for the custom input form."
    )
    related_check = models.ForeignKey(
        to=AppCheck,
        verbose_name='Related check in CAP',
        null=True,
        blank=True,
        related_name='survey_questions',
        on_delete=models.CASCADE
    )

    auto_answer = models.BooleanField(
        default=False,
        help_text="Determines if the answer will be populated automatically from CAP"
    )

    auto_answer_choice = models.ForeignKey(
        verbose_name='Auto answer choice from DESKTOP APP question',
        to='rulebook.SurveyQuestionChoices',
        related_name='auto_answers_questions',
        null=True,
        blank=True,
        on_delete=models.CASCADE
    )
    auto_answer_survey_question = models.ForeignKey(
        to='self',
        verbose_name='Auto answer from another SURVEY question',
        null=True,
        blank=True,
        related_name='auto_answer_questions',
        on_delete=models.CASCADE
    )
    auto_answer_survey_question_boolean = models.BooleanField(
        verbose_name='Auto answer boolean from another SURVEY question', null=True, blank=True
    )
    auto_answer_current_question_boolean = models.BooleanField(
        verbose_name='Auto answer boolean for current SURVEY question', null=True, blank=True
    )
    auto_answer_survey_question_choice = models.ForeignKey(
        to='rulebook.SurveyQuestionChoices',
        verbose_name='Auto answer choice from another SURVEY question',
        null=True,
        blank=True,
        related_name='auto_answer_choices',
        on_delete=models.CASCADE
    )
    auto_answer_current_question_choice = models.ForeignKey(
        to='rulebook.SurveyQuestionChoices',
        verbose_name='Auto answer choice for current SURVEY question',
        null=True,
        blank=True,
        related_name='auto_answer_current_choices',
        on_delete=models.CASCADE
    )
    allow_csv_text_fill = models.BooleanField(
        verbose_name='Allow the user to upload a CSV to populate the text field', default=False
    )
    title = RichTextUploadingField(config_name='html_only')
    tooltip = models.TextField(blank=True, null=True, verbose_name="Cybersmart guidance")
    moreinfo = models.TextField(blank=True, null=True, verbose_name="Official guidance")
    show_children_on = models.BooleanField(verbose_name='Show children on', default=True,
                                           choices=((True, True), (False, False)))
    show_children_on_2 = models.BooleanField(
        verbose_name='Show children on 2', default=False,
        choices=((True, True), (False, False)),
        help_text='If this value is selected, elements that have parent_2 set to the current element will be shown.'
    )
    ranking = models.IntegerField(choices=RANKING_CHOICES, default=RANKING_NONE)
    marking_info = RichTextUploadingField(verbose_name='Pervade marking info', null=True, blank=True)
    insurer = models.SmallIntegerField(
        verbose_name="Insurer",
        default=InsurerMixin.NO_INSURER,
        choices=InsurerMixin.get_survey_question_insurers_choice()
    )
    common_reasons = RichTextUploadingField(verbose_name="Common reasons text", null=True, blank=True, default=None)
    custom_validators = models.JSONField(
        verbose_name="Custom validators", default=dict, blank=True)
    code = CINullCharField(
        verbose_name="Question code",
        max_length=255,
        null=True,
        blank=True,
        help_text="Backend uses this value as connection between db entry and the code."
    )
    max_answers_amount = models.SmallIntegerField(
        verbose_name="Max answers amount",
        default=0,
        help_text="Max amount of answers for this question. If `0` set, no limit will be applied."
                  "This option is only available for multiple choice questions (checkboxes)."
    )
    min_answers_amount = models.SmallIntegerField(
        verbose_name="Min answers amount",
        default=0,
        help_text="Min amount of answers for this question. If `0` set, no limit will be applied."
                  "This option is only available for multiple choice questions (checkboxes)."
    )
    objects = SurveyQuestionInsurerManager()

    class Meta:
        verbose_name = 'Survey Question'
        verbose_name_plural = 'Survey\'s Questions'
        ordering = ('order',)
        unique_together = ("version", "code")

    def __unicode__(self):
        if self.order and self.order.is_integer():
            order = int(self.order)
        else:
            order = self.order
        return '{0} {1} {2}'.format(
            '{0}.'.format(order),  # pylint: disable=no-member
            self.title, self.version
        )

    def __str__(self):
        return self.__unicode__()

    @property
    def failed_custom_choice(self):
        """
        Returns failed response choice object if exists otherwise returns False.
        :return: failed response choice object
        """
        if self.failed_choices.all().count():
            if self.failed_responses.all().count() == 1:
                return not self.failed_responses.all()[0].choice
        return False

    @property
    def order_str(self):
        """
        Returns question order stringified to one decimal place.
        """
        if self.order is None:
            return ""
        return f"{self.order:.1f}"

    @property
    def may_have_multiple_responses(self):
        """
        Return True in case of this question may have multiple responses otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.widget in (self.WIDGET_CHECKBOX, self.WIDGET_RADIO_CHECKBOX, self.WIDGET_ORGANISATION_ADDRESS,
                               self.WIDGET_ORGANISATION_TYPE_SELECT)

    @property
    def can_be_edited_by_assessor(self):
        """
        Returns True if answers for this question can be edited by an assessor.
        :return: True or False
        :rtype: bool
        """
        if self.may_have_multiple_responses and not self.widget == self.WIDGET_ORGANISATION_ADDRESS:
            return False
        elif self.response_type in (self.FILE, self.IMAGE):
            return False
        else:
            return True

    def show_question(self, survey_id: int) -> bool:
        """
        Returns True if question should be shown otherwise returns False.

        If parent is set, it will always be checked first.
        Only after it, parent_2 is checked.
        """
        if survey_id is None:
            raise ValueError("survey_id cannot be None")

        if self.parent:
            if self.mandatory_for_parent_choices.exists():
                return SurveyResponse.objects.filter(
                    question=self.parent,
                    choice__in=self.mandatory_for_parent_choices.all(),
                    survey__pk=survey_id
                ).exists()
            parent_response = self.parent.responses.filter(survey__pk=survey_id).first()
            if parent_response:
                # Can't use bool(value_boolean) as it can be bool(None)
                return parent_response.value_boolean == self.parent.show_children_on
            return False

        if self.parent_2:
            parent_response = self.parent_2.responses.filter(survey__pk=survey_id).first()
            if parent_response:
                # Can't use bool(value_boolean) as it can be bool(None)
                return parent_response.value_boolean == self.parent_2.show_children_on_2
            return False
        return True

    @cached_property
    def get_insurer(self):
        """
        Returns insurer value.
        :return: insurer
        :rtype: int
        """
        return self.insurer

    @property
    def mandatory_choices_pks(self) -> list:
        """
        Returns list of mandatory choices pks.
        """
        return [choice.pk for choice in self.mandatory_for_parent_choices.all()]


class SurveyQuestionChoices(TimeStampedModel):
    """
    This model represents question choice. You see them on survey page as radio buttons
    for different variants of answer.
    """
    question = models.ForeignKey(SurveyQuestion, related_name='choices', on_delete=models.CASCADE)
    value_text = models.TextField('Text', blank=True, null=True)
    value_integer = models.IntegerField('Integer', blank=True, null=True)
    value_boolean = models.BooleanField('Boolean', null=True, blank=True)
    value_float = models.FloatField('Float', blank=True, null=True)
    value_date = models.DateField('Date', blank=True, null=True)
    value_datetime = models.DateTimeField('DateTime', blank=True, null=True)
    value_file = models.FileField(upload_to=upload_file_securely, max_length=255, blank=True, null=True)
    value_image = models.ImageField(upload_to=upload_file_securely, max_length=255, blank=True, null=True)
    value_money = models.DecimalField(
        verbose_name="Money value", max_digits=20, decimal_places=2, null=True, blank=True
    )
    equivalents = models.ManyToManyField(to='self', verbose_name='Choice equivalents', blank=True)
    is_invalid = models.BooleanField('Is Invalid Choice', default=False)
    allow_custom_input = models.BooleanField(verbose_name="Allow Custom Input", default=False)
    code = models.CharField(
        max_length=255,
        blank=True,
        default='',
        help_text=(
            "Unique code of the question choice that can be used to identify it in the"
            " frontend code."
        ),
    )

    def _get_value(self):
        return getattr(self, 'value_{0}'.format(self.question.response_type))

    def _set_value(self, new_value):
        setattr(self, 'value_{0}'.format(self.question.response_type), new_value)

    value = property(_get_value, _set_value)

    class Meta:
        verbose_name = 'Survey\'s Question Choice'
        verbose_name_plural = 'Survey\'s Question Choices'
        ordering = ['id']

    def __unicode__(self):
        return 'Question {0}) {1} | {2}'.format(
            self.question.order_str, self.value, self.question.version  # pylint: disable=no-member
        )

    def __str__(self):
        return self.__unicode__()


class SurveyQuestionFailedChoices(TimeStampedModel):
    """
    This model represents question choice. You see them on survey page as radio buttons
    for different variants of answer.
    We always consider these choices as failed response.
    """
    question = models.ForeignKey(SurveyQuestion, related_name='failed_choices', on_delete=models.CASCADE)
    value_text = models.TextField('Text', blank=True, null=True)
    value_integer = models.IntegerField('Integer', blank=True, null=True)
    value_boolean = models.BooleanField('Boolean', null=True, blank=True)
    value_float = models.FloatField('Float', blank=True, null=True)
    value_date = models.DateField('Date', blank=True, null=True)
    value_datetime = models.DateTimeField('DateTime', blank=True, null=True)
    value_file = models.FileField(upload_to=upload_file_securely, max_length=255, blank=True, null=True)
    value_image = models.ImageField(upload_to=upload_file_securely, max_length=255, blank=True, null=True)
    value_money = models.DecimalField(
        verbose_name="Money value", max_digits=20, decimal_places=2, null=True, blank=True
    )
    equivalents = models.ManyToManyField(to='self', verbose_name='Choice equivalents', blank=True)

    def _get_value(self):
        return getattr(self, 'value_{0}'.format(self.question.response_type))

    def _set_value(self, new_value):
        setattr(self, 'value_{0}'.format(self.question.response_type), new_value)

    value = property(_get_value, _set_value)

    class Meta:
        verbose_name = 'Survey\'s Question Failed Choice'
        verbose_name_plural = 'Survey\'s Question Failed Choices'
        ordering = ['created']

    def __unicode__(self):
        return 'Question {0}) {1} | {2}'.format(
            self.question.order_str, self.value, self.question.version  # pylint: disable=no-member
        )

    def __str__(self):
        return self.__unicode__()


class SurveyResponse(TimeStampedModel):
    """
    This model represents an answer to survey question.
    """
    not_applicable = models.BooleanField('N/A', default=False, help_text='Empty response')
    survey = models.ForeignKey(CertificationSurvey, related_name='responses', on_delete=models.CASCADE)
    question = models.ForeignKey(SurveyQuestion, related_name='responses', on_delete=models.CASCADE)
    choice = models.ForeignKey(
        SurveyQuestionChoices,
        related_name='responses',
        null=True,
        blank=True,
        on_delete=models.CASCADE
    )
    code = models.CharField(verbose_name='Response identify code', max_length=255, blank=True, null=True)
    assessor_acceptance = models.BooleanField(verbose_name='Assessor acceptance', default=True)

    value_text = models.TextField('Value text', blank=True, null=True)
    value_integer = models.IntegerField('Value integer', blank=True, null=True)
    value_boolean = models.BooleanField('Value boolean', null=True, blank=True)
    value_float = models.FloatField('Value float', blank=True, null=True)
    value_date = models.DateField('Value date', blank=True, null=True)
    value_datetime = models.DateTimeField('Value datetime', blank=True, null=True)
    value_file = models.FileField(verbose_name='Value file', upload_to=upload_file_securely, max_length=255, blank=True,
                                  null=True)
    value_image = models.ImageField(verbose_name='Value image', upload_to=upload_file_securely, max_length=255,
                                    blank=True,
                                    null=True)
    value_money = models.DecimalField(
        verbose_name="Money value", max_digits=20, decimal_places=2, null=True, blank=True
    )
    updated_in_pervade = models.BooleanField(
        verbose_name="Updated in Pervade", help_text="Answer was updated in Pervade after certificates being issued",
        default=False
    )

    def _get_value(self):
        return self.get_value

    def _set_value(self, new_value):
        setattr(self, 'value_{0}'.format(self.question.response_type), new_value)

    value = property(_get_value, _set_value)

    def validate_value(self, value):
        validator = getattr(self, '_validate_%s' % self.question.response_type)
        validator(value)

    def _validate_text(self, value):
        if not isinstance(value, six.string_types):
            raise ValidationError(self._get_question_error('Must be str or unicode'))
        if self.question.widget == SurveyQuestion.WIDGET_EMAIL:
            try:
                validate_email(value)
            except ValidationError:
                raise ValidationError(self._get_question_error("Must be a valid email address"))
        if self.question.custom_validators:
            for comparator, data in self.question.custom_validators.items():
                self._validate_custom(comparator, data['comp_value'], data['error_message'], len(str(value)))
        if self.question.widget == SurveyQuestion.WIDGET_ORGANISATION_REGISTRATION:
            if len(value.split(' ')) > 1 or not re.match('(?:.*[0-9]+|none)', value, flags=re.IGNORECASE):
                raise ValidationError(self._get_question_error("Please provide an alphanumeric string without spaces"))

    def _validate_float(self, value):
        try:
            float(value)
        except ValueError:
            raise ValidationError(self._get_question_error('Must be a float'))

    def _validate_integer(self, value):
        try:
            int(value)
        except (ValueError, TypeError):
            raise ValidationError(self._get_question_error('Must be an integer'))

    def _validate_date(self, value):
        if not (isinstance(value, datetime) or isinstance(value, date)):
            raise ValidationError(self._get_question_error('Must be a date or datetime'))

    def _validate_datetime(self, value):
        if not isinstance(value, datetime):
            raise ValidationError(self._get_question_error('Must be a datetime'))

    def _validate_boolean(self, value):
        if not isinstance(value, bool):
            raise ValidationError(self._get_question_error('Must be a boolean'))

    def _validate_file(self, value):
        if value and not isinstance(value, File):
            raise ValidationError(self._get_question_error('Must be a file field'))

    def _validate_image(self, value):
        self._validate_file(value)

    def _validate_money(self, value):
        try:
            Decimal(value)
        except (ValueError, InvalidOperation):
            raise ValidationError(self._get_question_error("Must be a Decimal"))
        else:
            if self.question.custom_validators:
                for comparator, data in self.question.custom_validators.items():
                    self._validate_custom(comparator, data['comp_value'], data['error_message'], float(value))

    def _validate_custom(self, comparator, comp_value, error_message, value):
        """
        Method for using custom validators set in Django admin.
        Example:
            {
                "==": {
                    "comp_value": 100,
                    "error_message": "Value must be 100"
                }
            }
        """
        if comparator in ['==', '!=', '<=', '<', '>=', '>'] and (
                (isinstance(value, int) or isinstance(value, float)) and
                (isinstance(comp_value, int) or isinstance(comp_value, float))
        ):
            COMPARISONS = {
                '==': lambda x, y: x == y,
                '!=': lambda x, y: x != y,
                '<=': lambda x, y: x <= y,
                '<': lambda x, y: x < y,
                '>=': lambda x, y: x >= y,
                '>': lambda x, y: x > y
            }
            result = COMPARISONS[comparator](value, comp_value)
            if not result:
                raise ValidationError(self._get_question_error(error_message))

    def _get_question_error(self, error_message):
        return _('Question ') + str(self.question.pervade_num) + ': ' + _(error_message)

    class Meta:
        verbose_name = 'Survey\'s Response'
        verbose_name_plural = 'Survey\'s Responses'
        ordering = ['question__order']

    def __unicode__(self):
        return '{0} survey [{1}] {2}'.format(
            self.survey.certificate.organisation.name, self.survey.id, self.question  # pylint: disable=no-member
        )

    def __str__(self):
        return self.__unicode__()

    @property
    def regular_value(self):
        """
        Returns success response value.
        We consider response value as success if it's not N/A and not failed response.
        N/A and failed response are the type of response, but we consider them as failed.
        Everything else we consider as success value regardless of what it is: string, integer, file etc
        :return: response value
        :rtype: object
        """
        obj = self.choice if self.choice else self
        if self.question.widget in [self.question.WIDGET_YES_NO_OPTIONAL_TEXT, self.question.WIDGET_CONSCIOUS]:
            if self.value_text:
                return str(self.value_text)
            else:
                return ["No", "Yes"][bool(self.value_boolean)]
        return getattr(obj, 'value_{0}'.format(self.question.response_type))

    @property
    def get_value(self):
        """
        Returns response value regardless of response type either it is failed response,
        not applicable response or regular response.
        :return: response value
        :rtype: object
        """
        if self.not_applicable:
            # in this case response can be:
            # N/A
            # boolean false
            # failed response
            if self.value_boolean is not None:
                try:
                    failed_response = SurveyFailedResponse.objects.filter(survey=self.survey, question=self.question)[0]
                except IndexError:
                    return False
                else:
                    return failed_response.value
            else:
                return 'N/A'
        if self.choice:
            return self.choice.value
        else:
            if self.not_applicable:
                return 'N/A'
            return self.regular_value

    @property
    def get_choice(self):
        return self.choice

    @property
    def humanize_response_value(self):
        """
        Returns question response value converted to humanize format.
        :return: converted value
        :rtype: any type
        """
        from organisations.models import Organisation

        response_type = self.question.response_type
        if self.question.widget == self.question.WIDGET_ORGANISATION_TYPE_SELECT:
            return str(dict(Organisation.ORG_CHOICES).get(self.value, self.value))
        elif self.is_yes_no_with_text_widget or self.is_conscious_widget_with_text:
            if self.value_text:
                return str(self.value_text)
            else:
                return ["No", "Yes"][bool(self.value_boolean)]
        elif response_type == SurveyQuestion.BOOLEAN:
            return ['No', 'Yes'][bool(self.value_boolean)]
        elif response_type == SurveyQuestion.MONEY:
            if self.value is None:
                value = "0.00"
            else:
                value = self.value
            return " ".join([self.value_text or "", str(value)])
        elif response_type == SurveyQuestion.INTEGER:
            return str(self.value)
        elif response_type == SurveyQuestion.FILE or response_type == SurveyQuestion.IMAGE:
            try:
                return self.value.name.split('/')[-1]
            except (AttributeError, IndexError):
                return self.value
        else:
            if isinstance(self.value, bool):
                return ['No', 'Yes'][bool(self.value)]
            else:
                return self.value

    @property
    def is_yes_no_with_text_widget(self):
        """
        Returns True for all widget with 'Yes/No' options.
        """
        return self.question.widget in (SurveyQuestion.WIDGET_YES_NO_TEXT, SurveyQuestion.WIDGET_COMMON_REASONS,
                                        SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT)

    @property
    def is_checkbox_widget(self) -> bool:
        """
        Returns True if widget is using checkbox input and can have multiple answers.
        """
        return self.question.widget in (SurveyQuestion.WIDGET_CHECKBOX, SurveyQuestion.WIDGET_RADIO_CHECKBOX)

    @property
    def is_conscious_widget(self):
        """
        Returns True if widget is conscious widget.
        """
        return self.question.widget == SurveyQuestion.WIDGET_CONSCIOUS

    @property
    def is_conscious_widget_with_text(self):
        """
        Returns True if widget is conscious widget with text box shown.
        """
        return self.is_conscious_widget and self.question.show_text_box

    @property
    def conscious_value_is_compliant(self):
        """
        Returns True if conscious widget has compliant answer selected.
        """
        compliant_answers = {
            self.question.YES: True,
            self.question.NO: False,
        }
        if self.question.compliant_answer == self.question.ANY:
            return True
        return self.value_boolean == compliant_answers.get(self.question.compliant_answer)

    @property
    def is_yes_no_bool_widget(self):
        """
        Returns True for all widget with 'Yes/No' options.
        """
        return self.question.widget is SurveyQuestion.WIDGET_YES_NO_KEEP_OPTIONS

    @property
    def is_simple_radio_widget(self):
        """
        Returns True for simple radio widget.
        """
        return self.question.widget == SurveyQuestion.WIDGET_RADIO_SIMPLE

    @property
    def pervade_answer_value(self):
        """
        Returns response value valid for Pervade System.
        """
        if self.question.widget == self.question.WIDGET_ORGANISATION_ADDRESS:
            # return main organisation address answer
            # rest of the data will be returned in pervade compliance fields
            country = (self.value_text or "").lower()
            # convert country name to either of UK, EU/EEA or Rest of World
            if country in [uk_country.lower() for uk_country in UK_COUNTRIES]:
                country = "UK"
            elif country in [eu_country.lower() for eu_country in EU_EEA_COUNTRIES]:
                country = "EU/EEA"
            else:
                country = "Rest of World"
            return country

        if self.is_yes_no_with_text_widget or self.is_conscious_widget:
            if not self.question.pervade_save_text_to_answer:
                return ["No", "Yes"][
                    bool(not self.value_boolean if self.question.pervade_reverse_value else self.value_boolean)
                ]
        if self.question.pervade_save_applicant_notes:
            if not self.not_applicable:
                value = self.get_value

                if value:
                    return "Yes" if not self.question.pervade_reverse_value else "No"
                else:
                    return "No" if not self.question.pervade_reverse_value else "Yes"
            else:
                return 'No' if not self.question.pervade_reverse_value else 'Yes'

        response_type = self.question.response_type
        if self.question.widget == self.question.WIDGET_ORGANISATION_TYPE_SELECT:
            try:
                value = dict(self.survey.certificate.organisation.ORG_CHOICES)[self.value]  # pylint: disable=no-member
            except KeyError:
                return None
            else:
                return force_str(value)
        if response_type == SurveyQuestion.BOOLEAN:
            return ['No', 'Yes'][
                bool(not self.get_value if self.question.pervade_reverse_value else self.get_value)
            ] if not self.get_value == 'N/A' else self.get_value
        if response_type == SurveyQuestion.FILE or response_type == SurveyQuestion.IMAGE:
            try:
                return self.get_value.name.split('/')[-1]
            except (AttributeError, IndexError):
                return self.get_value
        else:
            value = self.get_value
            if value is False:
                return 'No'
            return value

    @property
    def pervade_applicant_notes_value(self):
        """
        Returns humanized response if pervade_save_applicant_notes is True, otherwise returns blank
        """
        if self.is_checkbox_widget or self.is_simple_radio_widget:
            # send checkbox custom answers as applicant notes
            custom_answer = SurveyResponse.objects.filter(
                survey=self.survey,
                question=self.question,
                choice__isnull=True
            ).first()
            return custom_answer.value_text if custom_answer else ""

        if self.is_yes_no_with_text_widget or self.is_conscious_widget_with_text:
            return str(self.value_text)
        elif self.is_conscious_widget:
            if self.question.pervade_save_applicant_notes:
                list_index = bool(not self.value_boolean if self.question.pervade_reverse_value else self.value_boolean)
                return ['No', 'Yes'][list_index]

        if self.question.pervade_save_applicant_notes:
            response_type = self.question.response_type
            if response_type == SurveyQuestion.BOOLEAN:
                return ['No', 'Yes'][
                    not self.get_value if self.question.pervade_reverse_value else self.get_value
                ] if not any([self.get_value == 'N/A', self.get_value is None]) else 'N/A'
            if response_type == SurveyQuestion.FILE or response_type == SurveyQuestion.IMAGE:
                try:
                    return self.get_value.name.split('/')[-1]
                except (AttributeError, IndexError):
                    return self.get_value
            else:
                return self.get_value
        else:
            return ''

    @property
    def pervade_assessor_notes_value(self):
        """
        Returns assessor note
        """
        if not hasattr(self.survey, "assessment"):
            return ""
        if self.survey.assessment.notes.filter(question=self.question).exists():
            return self.survey.assessment.notes.filter(question=self.question).latest('modified').text
        else:
            return ""

    @property
    def pervade_assessor_score(self):
        """
        Returns assessor score
        """
        if self.question.ranking == SurveyQuestion.RANKING_NONE:
            return 'pass'

        if self.question.widget == self.question.WIDGET_CONSCIOUS:
            if all([
                self.question.compliant_answer not in [SurveyQuestion.ANY, SurveyQuestion.NA],
                self.value_boolean != (self.question.compliant_answer == SurveyQuestion.YES)
            ]):
                answer_val = False
            else:
                answer_val = bool(self.get_value) if not self.not_applicable else False
        else:
            answer_val = bool(self.get_value) if not self.not_applicable else False

        if answer_val:
            return 'pass'
        else:
            if self.question.ranking == SurveyQuestion.RANKING_MINOR:
                # this likely needs changing to match the marking scheme for IASME Governance
                return 'Minor Non-Compliance'
            elif self.question.ranking == SurveyQuestion.RANKING_MAJOR:
                # this likely needs changing to match the marking scheme for IASME Governance
                return 'Major Non-Compliance'
            else:
                return 'auto'

    def get_colour(self):
        if not self.not_applicable:
            if self.value or self.question.ranking == SurveyQuestion.RANKING_NONE:
                return 'Green'
            return 'Red'

        if self.value_boolean is False:
            return 'Red'
        return 'Grey'

    @property
    def pervade_compliance_fields(self) -> dict:
        """
        Returns compliance fields for Pervade System.
        """
        fields = {}
        for key, value in self.question.pervade_compliance_fields.items():
            if "name" in value:
                field_name = value["name"].lower()
                if field_name == "applicant notes":
                    fields[key] = self.pervade_applicant_notes_value
                elif field_name == "assessor score":
                    fields[key] = "Compliant"
                elif field_name == "assessor notes":
                    fields[key] = self.pervade_assessor_notes_value
                else:
                    fields[key] = ""
            else:
                fields[key] = ""
        return fields

    @staticmethod
    def import_debug_answer_value(survey, question):
        """
        Fill survey response with test values for debugging purposes
        (allows for easier QA of the survey responses).
        """
        response, _ = SurveyResponse.objects.get_or_create(
            survey=survey,
            question_id=question.pk
        )
        fields = {
            SurveyQuestion.WIDGET_ORGANISATION_ADDRESS: SurveyQuestion.WIDGET_ORGANISATION_ADDRESS_FIELDS,
            SurveyQuestion.WIDGET_ORGANISATION_IT: SurveyQuestion.WIDGET_ORGANISATION_IT_FIELDS,
            SurveyQuestion.WIDGET_FULL_NAME: SurveyQuestion.WIDGET_FULL_NAME_FIELDS
        }
        if question.widget in fields.keys():
            response_fields = fields[question.widget]
            for field in response_fields:
                if field == "postcode":
                    # valid uk postcode
                    value = "E1 5JL"
                elif field == "country":
                    value = "United Kingdom"
                else:
                    value = "test {0}".format(field)
                SurveyResponse.objects.create(
                    survey=survey,
                    question_id=question.pk,
                    not_applicable=False,
                    code=field,
                    value_text=value
                )
        if question.response_type == SurveyQuestion.TEXT and question.choices.count():
            if question.widget in (
                    SurveyQuestion.WIDGET_RADIO_TEXTAREA,
                    SurveyQuestion.WIDGET_CONSCIOUS
            ):
                response.not_applicable = False
                response.value_text = 'Test'
                response.choice = None
                response.save()
            elif question.widget in (SurveyQuestion.WIDGET_COMMON_REASONS,
                                        SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT):
                response.not_applicable = False
                response.value_text = "Test"
                response.value_boolean = True
                response.choice = None
                response.save()
            elif question.min_answers_amount > 0:
                max_answers_amount = question.max_answers_amount or 1000
                # first update existing answer
                response.not_applicable = False
                response.choice = question.choices.first()
                response.save()
                while SurveyResponse.objects.filter(
                        survey=survey,
                        question_id=question.pk,
                        not_applicable=False
                ).count() < question.min_answers_amount <= max_answers_amount:
                    SurveyResponse.objects.get_or_create(
                        survey=survey,
                        question_id=question.pk,
                        not_applicable=False,
                        choice=question.choices.order_by("?").first()
                    )

            else:
                response.not_applicable = False
                response.choice = question.choices.first()
                response.save()
        elif question.widget in (
                SurveyQuestion.WIDGET_YES_NO_TEXT, SurveyQuestion.WIDGET_YES_NO_KEEP_OPTIONS,
                SurveyQuestion.WIDGET_COMMON_REASONS, SurveyQuestion.WIDGET_YES_NO_OPTIONAL_TEXT,
                SurveyQuestion.WIDGET_CONSCIOUS
        ):
            response.not_applicable = False
            response.value_text = "Test"
            response.value_boolean = True
            response.choice = None
            response.save()
        elif question.response_type == SurveyQuestion.TEXT:
            if question.widget == SurveyQuestion.WIDGET_EMAIL:
                response.not_applicable = False
                response.value_text = "test{0}@cybersmart.co.uk".format(uuid.uuid4().hex[:8])
                response.save()
            elif question.widget == SurveyQuestion.WIDGET_ORGANISATION_REGISTRATION:
                response.not_applicable = False
                response.value_text = "CN001234"
                response.save()
            else:
                response.not_applicable = False
                response.value_text = 'Test'
                response.save()
        elif question.response_type == SurveyQuestion.BOOLEAN:
            response.not_applicable = False
            response.value_boolean = True
            response.save()
        elif question.response_type == SurveyQuestion.INTEGER:
            response.not_applicable = False
            response.value_integer = 10
            response.save()
        elif question.response_type == SurveyQuestion.MONEY:
            response.not_applicable = False
            response.value_money = Decimal("125000")
            response.value_text = "£"
            response.save()


class SurveyFailedResponse(TimeStampedModel):
    """
    This model represents an answer to survey question.
    This response will be always considered as failed.
    """
    survey = models.ForeignKey(CertificationSurvey, related_name='failed_responses', on_delete=models.CASCADE)
    question = models.ForeignKey(SurveyQuestion, related_name='failed_responses', on_delete=models.CASCADE)
    choice = models.ForeignKey(
        SurveyQuestionFailedChoices,
        related_name='failed_responses',
        null=True,
        blank=True,
        on_delete=models.CASCADE
    )

    value_text = models.TextField('Value text', blank=True, null=True)
    value_integer = models.IntegerField('Value integer', blank=True, null=True)
    value_boolean = models.BooleanField('Value boolean', null=True, blank=True)
    value_float = models.FloatField('Value float', blank=True, null=True)
    value_date = models.DateField('Value date', blank=True, null=True)
    value_datetime = models.DateTimeField('Value datetime', blank=True, null=True)
    value_file = models.FileField(verbose_name='Value file', upload_to=upload_file_securely, max_length=255, blank=True,
                                  null=True)
    value_image = models.ImageField(verbose_name='Value image', upload_to=upload_file_securely, max_length=255,
                                    blank=True,
                                    null=True)
    value_money = models.DecimalField(
        verbose_name="Money value", max_digits=20, decimal_places=2, null=True, blank=True
    )

    def _get_value(self):
        obj = self.choice if self.choice else self
        return getattr(obj, 'value_{0}'.format(self.question.response_type))

    def _set_value(self, new_value):
        setattr(self, 'value_{0}'.format(self.question.response_type), new_value)

    value = property(_get_value, _set_value)

    def validate_value(self, value):
        validator = getattr(self, '_validate_%s' % self.question.response_type)
        validator(value)

    def _validate_text(self, value):
        if not isinstance(value, six.string_types):
            raise ValidationError(self._get_question_error('Must be str or unicode'))

    def _validate_float(self, value):
        try:
            float(value)
        except ValueError:
            raise ValidationError(self._get_question_error('Must be a float'))

    def _validate_integer(self, value):
        try:
            int(value)
        except ValueError:
            raise ValidationError(self._get_question_error('Must be an integer'))

    def _validate_date(self, value):
        if not (isinstance(value, datetime) or isinstance(value, date)):
            raise ValidationError(self._get_question_error('Must be a date or datetime'))

    def _validate_datetime(self, value):
        if not isinstance(value, datetime):
            raise ValidationError(self._get_question_error('Must be a datetime'))

    def _validate_boolean(self, value):
        if not isinstance(value, bool):
            raise ValidationError(self._get_question_error('Must be a boolean'))

    def _validate_file(self, value):
        if value and not isinstance(value, File):
            raise ValidationError(self._get_question_error('Must be a file field'))

    def _validate_image(self, value):
        self._validate_file(value)

    def _get_question_error(self, error_message):
        return _('Question ') + str(self.question.order) + ': ' + _(error_message)

    class Meta:
        verbose_name = 'Survey\'s  Failed Response'
        verbose_name_plural = 'Survey\'s Failed Responses'
        ordering = ['question__order']

    def __unicode__(self):
        return '{} survey [{}] {}'.format(
            self.survey.certificate.organisation.name, self.survey.id, self.question  # pylint: disable=no-member
        )

    def __str__(self):
        return self.__unicode__()

    @property
    def get_value(self):
        if self.choice:
            return self.choice.value
        else:
            return self.value

    @property
    def get_choice(self):
        return self.choice


class SurveyAssessment(TimeStampedModel):
    """
    This model represents survey assessment process. We store here assessment status for each survey
    """
    IN_PROGRESS = 0
    SENT_BACK = 1
    RE_ANSWERED = 2
    VERIFIED = 3
    CERTIFICATE_PREVIEW = 4
    CERTIFICATE_DOWNLOAD = 5
    FINISHED = 6

    STATUSES = (
        (IN_PROGRESS, 'Assessment in progress'),
        (SENT_BACK, 'Sent back to survey'),
        (RE_ANSWERED, 'Re-answered'),
        (VERIFIED, 'Verified'),
        (CERTIFICATE_PREVIEW, 'Certificate preview'),
        (CERTIFICATE_DOWNLOAD, 'Certificate download'),
        (FINISHED, 'Finished')
    )

    survey = models.OneToOneField(to=CertificationSurvey, related_name='assessment', on_delete=models.CASCADE)
    status = models.SmallIntegerField(verbose_name='Assessment status', choices=STATUSES, default=IN_PROGRESS)

    class Meta:
        verbose_name = 'Survey Assessment'
        verbose_name_plural = 'Survey\'s Assessments'

    def __unicode__(self):
        return '{0}, Status: {1}'.format(self.survey, self.get_status_display())

    def __str__(self):
        return self.__unicode__()

    @property
    def is_in_progress(self):
        """
        Returns True if assessment is in progress otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.IN_PROGRESS

    @property
    def is_sent_back(self):
        """
        Returns True if some survey questions were sent back to survey for clarification otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.SENT_BACK

    @property
    def is_re_answered(self):
        """
        Returns True if sent back questions were re-answered and submitted again otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.RE_ANSWERED

    @property
    def is_verified(self):
        """
        Returns True if assessment is verified otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.VERIFIED

    @property
    def is_certificate_preview(self):
        """
        Returns True if assessment is certificate preview otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.CERTIFICATE_PREVIEW

    @property
    def is_certificate_download(self):
        """
        Returns True if assessment is certificate download otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.CERTIFICATE_DOWNLOAD

    @property
    def is_finished(self):
        """
        Returns True if assessment is finished otherwise returns False
        :return: True or False
        :rtype: bool
        """
        return self.status == self.FINISHED

    def set_in_progress(self):
        """
        Sets assessment status to in progress.
        :return: Nothing
        :rtype: None
        """
        self.status = self.IN_PROGRESS
        self.save()

    def set_sent_back(self):
        """
        Sets assessment status to sent back.
        :return: Nothing
        :rtype: None
        """
        self.status = self.SENT_BACK
        self.save()

    def set_re_answered(self):
        """
        Sets assessment status to re-answered.
        :return: Nothing
        :rtype: None
        """
        self.status = self.RE_ANSWERED
        self.save()

    def set_verified(self):
        """
        Sets assessment status to verified.
        :return: Nothing
        :rtype: None
        """
        self.status = self.VERIFIED
        self.save()

    def set_certificate_preview(self):
        """
        Sets assessment status to certificate preview.
        :return: Nothing
        :rtype: None
        """
        self.status = self.CERTIFICATE_PREVIEW
        self.save()

    def set_certificate_download(self):
        """
        Sets assessment status to certificate download.
        :return: Nothing
        :rtype: None
        """
        self.status = self.CERTIFICATE_DOWNLOAD
        self.save()

    def set_finished(self):
        """
        Sets assessment status to finished.
        :return: Nothing
        :rtype: None
        """
        self.status = self.FINISHED
        self.save()

    def has_notes(self):
        """
        Returns True if this assessment has notes from assessor otherwise returns False.
        :return: True or False
        :rtype: bool
        """
        return self.notes.all().exists()

    @property
    def is_response_overdue(self):
        return (timezone.now() - self.modified).total_seconds() / 60 / 60 >= 72 if self.modified else False


class AssessorNote(TimeStampedModel):
    """
    This model represents assessor note that will be sent back to user as clarification for some answers.
    """
    assessment = models.ForeignKey(to=SurveyAssessment, related_name='notes', on_delete=models.CASCADE)
    topic = models.ForeignKey(to=SurveyQuestionTopic, related_name='assessor_notes', on_delete=models.CASCADE)
    question = models.ForeignKey(to=SurveyQuestion, related_name='assessor_notes', on_delete=models.CASCADE)
    text = models.TextField(verbose_name='Note text')
    re_answered = models.BooleanField(
        verbose_name='Re-answered', help_text='Question was re-answered taking onto account assessor note',
        default=False
    )
    answer_accepted = models.BooleanField(verbose_name='Answer accepted by assessor', default=False)

    class Meta:
        verbose_name = 'Assessor Note'
        verbose_name_plural = 'Assessor Notes'

    def __unicode__(self):
        return 'Status: {0}, Question: {1}, Comment: {2}'.format(
            self.assessment.get_status_display(),
            self.question.title,
            self.text
        )

    def __str__(self):
        return self.__unicode__()


class FixGuide(TimeStampedModel):
    """
    This model represents fix guide for a specific AppCheck.
    """
    app_check = models.ForeignKey(AppCheck, related_name='fix_guides', on_delete=models.CASCADE)
    operating_systems = models.ManyToManyField(OperatingSystem, related_name='fix_guides')
    steps_to_fix = RichTextUploadingField(
        help_text='HTML guide to help users perform needed steps in order to fix the failing AppCheck.',
        verbose_name='Steps to fix'
    )

    class Meta:
        verbose_name = 'Fix Guide'
        verbose_name_plural = 'Fix Guides'

    def __str__(self):
        return f'Fix Guide for "{self.app_check}"'



def get_questionnaire_queryset(certification, only_with_notes=False, include_not_applicable=False):
    """
    Returns questionnaire queryset with prefetched data.
    :param certification: certification
    :type certification: OrganisationCertification
    :param only_with_notes: returns only those topics and questions that have notes from an assessor
    :type only_with_notes: bool
    :param include_not_applicable: if True includes not applicable responses otherwise ignores them
    :type include_not_applicable: bool
    :return: questionnaire queryset
    :rtype: SurveyQuestionTopic queryset
    """
    topic_filters = {
        'version': certification.version,
        'questions_count__gt': 0
    }

    not_applicable_filter = {
        'not_applicable': False
    }
    if include_not_applicable:
        del not_applicable_filter['not_applicable']

    if only_with_notes:
        topic_filters['pk__in'] = list(set(list(
            AssessorNote.objects.filter(
                assessment__survey=certification.survey, re_answered=False, answer_accepted=False
            ).values_list('topic__pk', flat=True)
        )))

    questions_filters = {
        'version__organisationcertification': certification,
    }
    if only_with_notes:
        # only those questions that have notes from an assessor
        questions_filters['pk__in'] = AssessorNote.objects.filter(
            assessment__survey=certification.survey,
            re_answered=False,
            answer_accepted=False,
        ).values_list(
            'question__pk', flat=True
        )
        # and those questions children
        children = SurveyQuestion.objects.filter_insurer_options(certification).filter(
            parent__pk__in=questions_filters['pk__in'],
        ).values_list(
            'pk', flat=True
        )

        questions_filters['pk__in'] = list(set(list(questions_filters['pk__in']) + list(children)))

    # if organisation has opted in for the CE 100k R&R toolbox insurance previous to the start of the survey,
    # then we need to hide the opt-in question and set to True
    questions_excludes = {}
    if certification.is_cyber_essentials and certification.organisation.has_ce_100k_r_and_r_toolbox_insurance:
        if not certification.insurance_opt_in_survey_value:
            # update insurance opt-in response for the survey
            opt_in_response = get_opt_in_survey_response(certification.SUPERSCRIPT_INSURER, certification.survey)
            opt_in_response.value_boolean = True
            opt_in_response.not_applicable = False
            opt_in_response.save()

    queryset = SurveyQuestionTopic.objects.prefetch_related(
        Prefetch('assessor_notes', AssessorNote.objects.filter(assessment__survey=certification.survey))
    ).annotate(questions_count=Count('questions', filter=Q(
        questions__insurer__in=certification.get_insurer_options()
    ))).filter(
        **topic_filters
    ).prefetch_related(
        Prefetch(
            'questions', SurveyQuestion.objects.filter_insurer_options(certification).filter(
                **questions_filters
            ).exclude(
                **questions_excludes
            ).order_by(
                'order', 'created'
            ).annotate(
                has_trigger_option=Exists(
                    SurveyQuestionChoices.objects.filter(
                        question=OuterRef('pk'),
                        allow_custom_input=True
                    )
                )
            ).prefetch_related(
                Prefetch('assessor_notes', AssessorNote.objects.filter(assessment__survey=certification.survey))
            ).prefetch_related(
                Prefetch('assessor_notes', AssessorNote.objects.filter(
                    assessment__survey=certification.survey,
                    answer_accepted=False
                ), to_attr='not_accepted_assessor_notes')
            ).prefetch_related(
                'auto_answer_questions'
            ).prefetch_related(
                Prefetch(
                    'responses', SurveyResponse.objects.filter(
                        survey=certification.survey,
                        **not_applicable_filter
                    ).select_related('choice', 'choice__question').order_by('created')
                )
            ).prefetch_related(
                Prefetch(
                    'responses', SurveyResponse.objects.filter(
                        survey=certification.survey,
                        choice__isnull=True,
                        **not_applicable_filter
                    ), to_attr='has_custom_answer'
                )
            ).prefetch_related(
                Prefetch(
                    'responses', SurveyResponse.objects.filter(
                        survey=certification.survey,
                        assessor_acceptance=True,
                        **not_applicable_filter
                    ), to_attr='accepted_responses'
                )
            ).prefetch_related(
                Prefetch(
                    'failed_choices', SurveyQuestionFailedChoices.objects.filter(
                        question__version__organisationcertification=certification
                    )
                )
            ).prefetch_related(
                Prefetch(
                    'failed_responses', SurveyFailedResponse.objects.filter(
                        survey__certificate=certification
                    )
                )
            ).prefetch_related(
                Prefetch(
                    'choices', SurveyQuestionChoices.objects.filter(
                        question__version__organisationcertification=certification
                    ).prefetch_related(
                        'mandatory_questions', 'auto_answer_choices'
                    ).prefetch_related(
                        Prefetch(
                            'responses', SurveyResponse.objects.filter(
                                survey=certification.survey,
                                **not_applicable_filter
                            )
                        )
                    )
                )
            ).prefetch_related(
                'parent', Prefetch(
                    'parent__responses', SurveyResponse.objects.filter(
                        survey=certification.survey,
                        **not_applicable_filter
                    ).select_related('choice')
                ), 'children'
            ).prefetch_related(
                'parent_2', Prefetch(
                    'parent_2__responses', SurveyResponse.objects.filter(
                        survey=certification.survey,
                        **not_applicable_filter
                    ).select_related('choice')
                ), 'children_2'
            )
            .prefetch_related(
                Prefetch(
                    'mandatory_for_parent_choices', SurveyQuestionChoices.objects.filter(
                        question__version__organisationcertification=certification
                    ).prefetch_related(
                        Prefetch(
                            'responses', SurveyResponse.objects.filter(
                                survey=certification.survey,
                                **not_applicable_filter
                            )
                        )
                    )
                )
            )
        )
    ).order_by("order")

    if certification.organisation.insurance_tab_should_be_hidden:
        queryset = queryset.exclude(title="Insurance")
    return queryset


def get_re_answered_questions_queryset(certification, include_not_applicable=False):
    """
    Returns questionnaire queryset with prefetched data for re answered questions on CertOS dashboard.
    :param certification: certification
    :type certification: OrganisationCertification
    :param include_not_applicable: if True includes not applicable responses otherwise ignores them
    :type include_not_applicable: bool
    :return: questionnaire queryset
    :rtype: SurveyQuestionTopic queryset
    """
    re_answered_questions_with_notes = list(SurveyQuestion.objects.filter_insurer_options(certification).filter(
        version__organisationcertification=certification,
        pk__in=AssessorNote.objects.filter(
            assessment__survey=certification.survey, re_answered=True, answer_accepted=False
        ).values_list(
            'question__pk', flat=True
        )
    ).values_list('pk', flat=True))
    # and those questions children
    children = list(SurveyQuestion.objects.filter_insurer_options(certification).filter(
        parent__pk__in=re_answered_questions_with_notes,
        pk__in=AssessorNote.objects.filter(
            assessment__survey=certification.survey, re_answered=True
        ).values_list(
            'question__pk', flat=True
        )
    ).values_list(
        'pk', flat=True
    ))

    not_applicable_filter = {
        'not_applicable': False
    }
    if include_not_applicable:
        del not_applicable_filter['not_applicable']

    return SurveyQuestion.objects.filter_insurer_options(certification).filter(
        pk__in=list(set(re_answered_questions_with_notes + children))
    ).order_by(
        'order', 'created'
    ).prefetch_related(
        Prefetch('assessor_notes', AssessorNote.objects.filter(assessment__survey=certification.survey))
    ).prefetch_related(
        Prefetch('assessor_notes', AssessorNote.objects.filter(
            assessment__survey=certification.survey,
            answer_accepted=False
        ), to_attr='not_accepted_assessor_notes')
    ).prefetch_related(
        'auto_answer_questions'
    ).prefetch_related(
        Prefetch(
            'responses', SurveyResponse.objects.filter(
                survey=certification.survey,
                **not_applicable_filter
            ).select_related('choice', 'choice__question')
        )
    ).prefetch_related(
        Prefetch(
            'responses', SurveyResponse.objects.filter(
                survey=certification.survey,
                assessor_acceptance=True,
                **not_applicable_filter
            ), to_attr='accepted_responses'
        )
    ).prefetch_related(
        Prefetch(
            'responses', SurveyResponse.objects.filter(
                survey=certification.survey,
                choice__isnull=True,
                **not_applicable_filter
            ), to_attr='has_custom_answer'
        )
    ).prefetch_related(
        Prefetch(
            'failed_choices', SurveyQuestionFailedChoices.objects.filter(
                question__version__organisationcertification=certification
            )
        )
    ).prefetch_related(
        Prefetch(
            'failed_responses', SurveyFailedResponse.objects.filter(
                survey__certificate=certification
            )
        )
    ).prefetch_related(
        Prefetch(
            'choices', SurveyQuestionChoices.objects.filter(
                question__version__organisationcertification=certification
            ).prefetch_related(
                'mandatory_questions', 'auto_answer_choices'
            ).prefetch_related(
                Prefetch(
                    'responses', SurveyResponse.objects.filter(
                        survey__certificate=certification,
                        **not_applicable_filter
                    )
                )
            )
        )
    ).prefetch_related(
        'parent', Prefetch(
            'parent__responses', SurveyResponse.objects.filter(
                survey__certificate=certification,
                **not_applicable_filter
            ).select_related('choice')
        ), 'children'
    ).prefetch_related(
        Prefetch(
            'mandatory_for_parent_choices', SurveyQuestionChoices.objects.filter(
                question__version__organisationcertification=certification
            ).prefetch_related(
                Prefetch(
                    'responses', SurveyResponse.objects.filter(
                        survey__certificate=certification,
                        **not_applicable_filter
                    )
                )
            )
        )
    )
