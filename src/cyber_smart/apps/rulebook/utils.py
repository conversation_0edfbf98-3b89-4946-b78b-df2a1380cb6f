from __future__ import unicode_literals

import importlib
import logging
import os
import re
import shutil
import subprocess
import traceback
from io import BytesIO
from shutil import rmtree

import sentry_sdk
from django import forms
from django.conf import settings
from django.contrib.staticfiles import finders
from django.core.files import File
from django.core.validators import validate_email
from django.db import transaction
from django.db.models import Count, Prefetch, Q, Exists, OuterRef
from django.db.models.query import QuerySet
from django.http.request import HttpRequest
from django.shortcuts import Http404
from django.template.loader import render_to_string
from django.utils import timezone
from django.utils.html import strip_tags
from django.utils.translation import ngettext_lazy
from fdfgen import forge_fdf
from packaging.version import parse as parse_version
from pypdf import PdfWriter, PdfReader
from reportlab.lib.utils import ImageReader
from reportlab.pdfgen import canvas

from api.common.tasks import create_app_user_log
from appusers.models import AppInstall
from common.utils import handle_user_input
from common.validators import post_code_validator
from dashboard.utils import get_applications_responses
from insurance.utils import get_insurance_question
from operating_systems.utils import get_organisation_unsupported_os_releases
from organisations.models import OrganisationCertification, Organisation
from rulebook.fixtures.responses import RESPONSE_CODE_INDUSTRY_DESCRIPTION
from rulebook.forms import ResponseForm
from rulebook.models import (
    OSPlatformRelease,
    CertificationVersion,
    SurveyResponse,
    SurveyQuestion,
    CYBER_ESSENTIALS,
    SurveyFailedResponse,
    SurveyQuestionTopic,
    SurveyDeclaration,
    GDPR,
    CYBER_ESSENTIALS_PLUS,
    OperatingSystem,
    AssessorNote,
    SurveyQuestionFailedChoices,
    SurveyQuestionChoices,
)
from rulebook.questions import (
    QUESTION_CODE_ORGANISATION_NAME, QUESTION_CODE_ORGANISATION_ADDRESS, QUESTION_CODE_ORGANISATION_INDUSTRY,
    QUESTION_CODE_ORGANISATION_WEBSITE, QUESTION_CODE_ORGANISATION_SIZE, QUESTION_CODE_REGISTRATION_NUMBER,
    QUESTION_CODE_INSURANCE_EMAIL_2018, QUESTION_CODE_INSURANCE_EMAIL, QUESTION_CODE_EQUIPMENT_2018,
    QUESTION_CODE_EQUIPMENT_DESKTOP, QUESTION_CODE_EQUIPMENT_MOBILE, QUESTION_CODE_ORGANISATION_TYPE,
)

logger = logging.getLogger(__name__)


class SkipQuestion(Exception):
    """
    This exception will be raised every time a question should be skipped without any handling.
    """
    pass


def match_os(app_user_id, platform, release, hostname, caption, device_id, os_build=None):
    """
    Returns operating system for passed platform and release.
    If operating system release is not found creates a new one.
    :param app_user_id: app install user id
    :type app_user_id: int
    :param platform: operating system platform
    :type platform: str or unicode
    :param release: operating system release number
    :type release: str or unicode
    :param hostname: operating system hostname
    :type hostname: str or unicode
    :param caption: humanized operating system name
    :type caption: str or unicode
    :param device_id: app install device id
    :type device_id: str or unicode
    :param os_build: operating system build number
    :type os_build: str or unicode
    :return: operating system
    :rtype rulebook.OperatingSystem
    """
    os_pr = OSPlatformRelease.objects.filter(platform=platform, release=release).first()
    if os_pr:
        if os_pr.os.is_supported:
            return os_pr.os
        else:
            # async
            create_app_user_log.delay(
                app_user_id=app_user_id,
                platform=platform,
                release=release,
                hostname=hostname,
                caption=caption,
                device_id=device_id,
                error_text="Unsupported OS version"
            )
            return os_pr.os
    else:
        # async
        create_app_user_log.delay(
            app_user_id=app_user_id,
            platform=platform,
            release=release,
            hostname=hostname,
            caption=caption,
            device_id=device_id,
            error_text="New OS release"
        )

        # platform_release_modified = semver part 1 and 2 for win32 and semver part 1 for all others
        # e.g. win32_6.1 / darwin_18 / android_4
        version_info = release.split(".")
        if platform == "win32" and len(version_info) > 1:
            release_parsed = version_info[0] + "." + version_info[1]
        else:
            release_parsed = version_info[0]
        platform_release_modified = platform + "_" + release_parsed

        # handle Windows 11 as it has a release of 10.0.x
        if platform == 'win32' and parse_version(release) >= parse_version('10.0.22000'):
            platform_release_modified = 'windows_11'

        operating_system, _ = OperatingSystem.objects.get_or_create(
            os_id=platform_release_modified,
            defaults={
                'title': release
            }
        )
        OSPlatformRelease.objects.create(os=operating_system, platform=platform, release=release, os_build=os_build)
        return operating_system
        # raise exceptions.ValidationError(_(
        #     "We're testing compatibility with operating system and will have checks live shortly"
        # ))


def get_default_versions(type=CYBER_ESSENTIALS):
    """
    Returns the default versions of certificate with passed type.
    :param type: certificate type
    :type type: int
    :return: last certificate
    :rtype CertificationVersion queryset
    """
    return CertificationVersion.objects.filter(
        type__type=type,
        release_date__lte=timezone.now(),
        created__lte=timezone.now(),
        default=True
    )

def get_latest_version(type=CYBER_ESSENTIALS):
    """
    Returns the last version of certificate with passed type.
    :param type: certificate type
    :type type: int
    :return: last certificate
    :rtype CertificationVersion instance
    """
    return get_default_versions(type).order_by('-release_date').first()

def handle_yes_no_text_widget(question, request, certification):
    """
    Handles questions that have Yes/No & Text widget.
    :param question: survey question
    :type question: rulebook.models.SurveyQuestion instance
    :param request: http request
    :type request: django.http.HttpRequest instance
    :param certification: organisation certification
    :type certification: organisations.models.OrganisationCertification instance
    :return: nothing
    :rtype: None
    """
    key = "response_{0}_{1}".format(question.pk, question.order_str)
    values = request.POST.getlist(key)

    try:
        *boolean_value, text_value = values
        boolean_value = bool(boolean_value)
        text_value = str(text_value)
    except ValueError:
        # if we got not correct data structure just delete all responses that belongs to this question
        # including question's children
        remove_all_question_responses(question, certification)
        return

    form = ResponseForm(question=question, data={key: text_value}, files=request.FILES, certification=certification)
    if not form.is_valid():
        raise forms.ValidationError(form.errors)
    cleaned_value = form.cleaned_data.get(key, text_value)
    """
    We have an ability to change questions from multiple responses to single response.
    Then there is can be a situation when questions that supposed to handle only single response have multiple responses
    because their type was changed from multiple to single response.
    To avoid this situation we need to leave only the last response and the rest of them we remove.
    """
    try:
        response, created = SurveyResponse.objects.get_or_create(
            survey=certification.survey,
            question_id=question.pk
        )
    except SurveyResponse.MultipleObjectsReturned:
        first = SurveyResponse.objects.filter(
            survey=certification.survey,
            question_id=question.pk
        ).first()
        response = first
        # remove other responses
        SurveyResponse.objects.filter(
            survey=certification.survey,
            question_id=question.pk
        ).exclude(pk=first.pk).delete()
    """
    ************************************
    """
    try:
        response._validate_text(text_value)
        response._validate_boolean(boolean_value)
    except forms.ValidationError as error:
        raise forms.ValidationError(error)
    else:
        response.value_boolean = boolean_value
        response.value_text = handle_user_input(cleaned_value)
        response.choice = None
        response.not_applicable = False
        response.save()
        # if we have success response then we should delete failed responses if they are exist
        remove_all_question_responses(question, certification, failed=True)


def handle_conscious_widget(question, request, certification):
    """
    Handles questions that have CONSCIOUS widget.
    :param question: survey question
    :type question: rulebook.models.SurveyQuestion instance
    :param request: http request
    :type request: django.http.HttpRequest instance
    :param certification: organisation certification
    :type certification: organisations.models.OrganisationCertification instance
    :return: nothing
    :rtype: None
    """
    key = f"response_{question.pk}_{question.order_str}"

    if question.show_text_box:
        try:
            boolean_value, text_value = request.POST.getlist(key)
            boolean_value = boolean_value == "on"
            text_value = str(text_value)
            data = {key: text_value}
        except ValueError:
            # if we got not correct data structure just delete all responses that belongs to this question
            # including question's children
            remove_all_question_responses(question, certification)
            return
    else:
        # we're using getlist()[0] because for widget w/o text box there's an additional hidden field
        # to allow for recognition of lack of value (get() gets the last value of given key)
        incoming_value = request.POST.getlist(key)[0]
        boolean_value = incoming_value == "on" if incoming_value != "" else None
        data = {}

    form = ResponseForm(question=question, data=data, files=request.FILES, certification=certification)
    if not form.is_valid():
        raise forms.ValidationError(form.errors)

    if boolean_value is None and not question.show_text_box:
        # None value means we need to delete all responses that belong to this question
        # including question's children
        remove_all_question_responses(question, certification)
        return

    dummy_response = SurveyResponse(survey=certification.survey, question_id=question.pk)
    dummy_response._validate_boolean(boolean_value)

    if question.show_text_box:
        dummy_response._validate_text(text_value)
        cleaned_value = form.cleaned_data.get(key, text_value)

    # remove other responses
    SurveyResponse.objects.filter(
        survey=certification.survey,
        question_id=question.pk
    ).delete()
    SurveyFailedResponse.objects.filter(
        survey=certification.survey,
        question_id=question.pk
    ).delete()

    defaults = {
        "value_boolean": boolean_value,
        "choice": None,
    }
    if question.show_text_box:
        defaults["value_text"] = handle_user_input(cleaned_value)
    SurveyResponse.objects.update_or_create(
        survey=certification.survey,
        question_id=question.pk,
        defaults=defaults
    )


def handle_radio_questions(question, request, certification):
    """
    Handles questions that can have only one response but a few choices.
    :param question: survey question
    :type question: rulebook.models.SurveyQuestion instance
    :param request: http request
    :type request: django.http.HttpRequest instance
    :param certification: organisation certification
    :type certification: organisations.models.OrganisationCertification instance
    :return: nothing
    :rtype: None
    """
    # first handle failed responses
    if question.failed_choices.all().count():
        key = 'failed_response_{0}_{1}'.format(question.pk, question.order_str)
        _, _, question_pk, question_order = key.split('_')
        values = request.POST.getlist(key)
        if len(values) > 0 and values[0] != '':
            # we can have 2 types of values for this type of responses
            # first - choice answer
            # second - custom answer
            try:
                if len(values) > 1:
                    choice, custom_answer = values
                    choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
                    custom_answer = normalize_value(
                        question.response_type, key, custom_answer, request.POST, request.FILES
                    )
                else:
                    choice = values[0]
                    custom_answer = None
                    choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
            except ValueError:
                # if we got not correct data structure just delete all responses that belongs to this question
                # including question's children
                remove_all_question_responses(question, certification, failed=True)
                # exit
                return

            # value can be represented as custom answer or one of choices
            value = custom_answer if choice == 'other' else choice

            # prevent response as custom answer if it disabled for a question
            if choice == 'other' and not question.other_form:
                value = None

            if value is None:
                # None value means we need to delete all responses that belong to this question
                # including question's children
                remove_all_question_responses(question, certification, failed=True)
                # exit()
                return

            # if value is not None let's validate it and save if it's valid
            form = ResponseForm(question=question, data={key: value}, files=request.FILES, certification=certification)
            if not form.is_valid():
                raise forms.ValidationError(form.errors)
            cleaned_value = form.cleaned_data.get(key, value)

            """
            We have an ability to change questions from multiple responses to single response.
            Then there is can be a situation when questions that supposed to handle only single response have multiple
            responses because their type was changed from multiple to single response.
            To avoid this situation we need to leave only the last response and the rest of them we remove.
            """
            try:
                response, created = SurveyFailedResponse.objects.get_or_create(
                    survey=certification.survey,
                    question_id=question_pk
                )
            except SurveyFailedResponse.MultipleObjectsReturned:
                first = SurveyFailedResponse.objects.filter(
                    survey=certification.survey,
                    question_id=question_pk
                ).first()
                response, created = first, False
                # remove other responses
                SurveyFailedResponse.objects.filter(
                    survey=certification.survey,
                    question_id=question_pk
                ).exclude(pk=first.pk).delete()
            """
            ************************************
            """

            if choice == 'other' and custom_answer:
                # if custom response
                try:
                    response.validate_value(cleaned_value)
                except forms.ValidationError as error:
                    raise forms.ValidationError(error)
                else:
                    response.value = handle_user_input(cleaned_value)
                    response.choice = None
                    response.save()
            else:
                # if choice response
                filters = {'value_{0}'.format(question.response_type): cleaned_value}
                choices = question.failed_choices.filter(**filters)
                if choices.count():
                    response.choice = choices[0]
                    response.save()
                else:
                    # if there is not choice with passed value remove responses
                    remove_all_question_responses(question, certification, failed=True)
    # handle success responses
    key = 'response_{0}_{1}'.format(question.pk, question.order_str)
    _, question_pk, question_order = key.split('_')
    values = request.POST.getlist(key)

    # we can have 3 types of values for this type of questions
    # first - radio button which shows/hides additional choices
    # second - choice itself
    # third - custom answer
    try:
        if len(values) == 3:
            radio_button, choice, custom_answer = values
            radio_button = normalize_value(SurveyQuestion.BOOLEAN, key, radio_button, request.POST, request.FILES)
            choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
            custom_answer = normalize_value(question.response_type, key, custom_answer, request.POST, request.FILES)
        elif len(values) == 2:
            if question.hide_choices:
                radio_button, choice = values
                custom_answer = None
                radio_button = normalize_value(SurveyQuestion.BOOLEAN, key, radio_button, request.POST, request.FILES)
                choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
            else:
                # in case if choices are shown by default, then radio button is not presented
                choice, custom_answer = values
                radio_button = 'on'
                choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
                custom_answer = normalize_value(question.response_type, key, custom_answer, request.POST, request.FILES)
        elif len(values) == 1:
            # in case if choices are shown by default, then radio button is not presented
            # and no other form
            choice = values[0]
            custom_answer = None
            radio_button = 'on'
            choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
        else:
            # if we got not correct data structure just delete all responses that belongs to this question
            # including question's children
            remove_all_question_responses(question, certification, not_applicable_as_false=True)
            # exit
            return
    except ValueError:
        # if we got not correct data structure just delete all responses that belongs to this question
        # including question's children
        remove_all_question_responses(question, certification)
        # exit
        return

    # value can be represented as custom answer or one of choices
    value = custom_answer if choice == 'other' else choice

    # prevent response as custom answer if it disabled for a question
    if choice == 'other' and not question.other_form:
        value = None

    # if radio widget is selected as false then delete all responses that belongs to this question
    # including question's children
    if radio_button == 'off' and question.hide_choices:
        remove_all_question_responses(question, certification, not_applicable_as_false=True)
        # exit
        return

    if value is None:
        # None value means we need to delete all responses that belong to this question
        # including question's children
        remove_all_question_responses(question, certification, not_applicable_as_false=True)
        # exit()
        return

    # if value is not None let's validate it and save if it's valid
    form = ResponseForm(question=question, data={key: value}, files=request.FILES, certification=certification)
    if not form.is_valid():
        raise forms.ValidationError(form.errors)
    cleaned_value = form.cleaned_data.get(key, value)
    """
    We have an ability to change questions from multiple responses to single response.
    Then there is can be a situation when questions that supposed to handle only single response have multiple responses
    because their type was changed from multiple to single response.
    To avoid this situation we need to leave only the last response and the rest of them we remove.
    """
    try:
        response, created = SurveyResponse.objects.get_or_create(
            survey=certification.survey,
            question_id=question_pk
        )
    except SurveyResponse.MultipleObjectsReturned:
        first = SurveyResponse.objects.filter(
            survey=certification.survey,
            question_id=question_pk
        ).first()
        response = first
        # remove other responses
        SurveyResponse.objects.filter(
            survey=certification.survey,
            question_id=question_pk
        ).exclude(pk=first.pk).delete()
    """
    ************************************
    """
    if choice == 'other' and custom_answer:
        # if custom response
        try:
            response.validate_value(cleaned_value)
        except forms.ValidationError as error:
            raise forms.ValidationError(error)
        else:
            response.value = handle_user_input(cleaned_value)
            response.choice = None
            response.not_applicable = False
            response.save()
            # if we have success response then we should delete failed responses if they are exist
            remove_all_question_responses(question, certification, failed=True)
    else:
        # if choice response
        filters = {'value_{0}'.format(question.response_type): cleaned_value}
        choices = question.choices.filter(**filters)
        if choices.count():
            response.choice = choices[0]
            response.not_applicable = False
            response.save()
            # for radio simple widget also create custom answer
            if custom_answer and question.WIDGET_RADIO_SIMPLE:
                SurveyResponse.objects.create(
                    survey=certification.survey,
                    question=question,
                    choice=None,
                    not_applicable=False,
                    value_text=custom_answer
                )
            # if we have success response then we should delete failed responses if they are exist
            remove_all_question_responses(question, certification, failed=True)
        else:
            # if there is not choice with passed value remove responses
            remove_all_question_responses(question, certification)


def handle_checkbox_questions(question, request, certification):
    """
    Handles questions that can have multiple responses.
    :param question: survey question
    :type question: rulebook.models.SurveyQuestion instance
    :param request: http request
    :type request: django.http.HttpRequest instance
    :param certification: organisation certification
    :type certification: organisations.models.OrganisationCertification instance
    :return: nothing
    :rtype: None
    """
    def get_checkbox_answers_count(question: SurveyQuestion, request: HttpRequest) -> int:
        """
        Returns number of answers for given question that use checkbox widget.
        """
        answers_count = 0
        for answer_option in question.choices.all():
            answer_key = f"response_{question.pk}_{question.order_str}_{answer_option.pk}"
            answer_value = normalize_value(
                question.response_type, answer_key, request.POST.get(answer_key), request.POST, request.FILES
            )
            if answer_value:
                answers_count += 1
        return answers_count

    def max_answers_violated(question: SurveyQuestion, request: HttpRequest) -> bool:
        """
        Checks if number of answers is violated allowed number of answers.
        """
        if not question.max_answers_amount:
            return False
        answers_count = get_checkbox_answers_count(question, request)
        return answers_count > question.max_answers_amount

    def min_answers_violated(question: SurveyQuestion, request: HttpRequest) -> bool:
        """
        Checks if number of answers is violated allowed number of answers.
        """
        if not question.min_answers_amount:
            return False
        answers_count = get_checkbox_answers_count(question, request)
        return answers_count < question.min_answers_amount

    def create_na(certification, question):
        """
        Creates one N/A (not applicable) responses for a question in case of question doesn't have any responses.
        :param question: survey question
        :type question: rulebook.models.SurveyQuestion instance
        :param certification: organisation certification
        :type certification: organisations.models.OrganisationCertification instance
        :param question:
        :return:
        """
        if not SurveyResponse.objects.filter(survey=certification.survey, question_id=question.pk).count():
            # create N/A response
            fields = {
                'survey': certification.survey,
                'question_id': question.pk,
                'not_applicable': True
            }
            if question.widget == SurveyQuestion.WIDGET_RADIO_CHECKBOX:
                fields['value_boolean'] = False
            SurveyResponse.objects.create(
                **fields
            )

    if question.failed_choices.all().count() and question.widget == SurveyQuestion.WIDGET_RADIO_CHECKBOX:
        # first handle failed responses
        key = 'failed_response_{0}_{1}'.format(question.pk, question.order_str)
        _, _, question_pk, question_order = key.split('_')
        values = request.POST.getlist(key)
        if len(values) > 0 and values[0] != '':
            # we can have 2 types of values for this type of responses
            # first - choice answer
            # second - custom answer
            try:
                if len(values) > 1:
                    choice, custom_answer = values
                    choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
                    custom_answer = normalize_value(
                        question.response_type, key, custom_answer, request.POST, request.FILES
                    )
                else:
                    choice = values[0]
                    custom_answer = None
                    choice = normalize_value(question.response_type, key, choice, request.POST, request.FILES)
            except ValueError:
                # if we got not correct data structure just delete all responses that belongs to this question
                # including question's children
                remove_all_question_responses(question, certification, failed=True)
                # exit
                return

            # value can be represented as custom answer or one of choices
            value = custom_answer if choice == 'other' else choice

            # prevent response as custom answer if it disabled for a question
            if choice == 'other' and not question.other_form:
                value = None

            if value is None:
                # None value means we need to delete all responses that belong to this question
                # including question's children
                remove_all_question_responses(question, certification, failed=True)
                # exit()
                return

            # if value is not None let's validate it and save if it's valid
            form = ResponseForm(question=question, data={key: value}, files=request.FILES, certification=certification)
            if not form.is_valid():
                raise forms.ValidationError(form.errors)
            cleaned_value = form.cleaned_data.get(key, value)

            response, created = SurveyFailedResponse.objects.get_or_create(
                survey=certification.survey,
                question_id=question_pk,
            )

            if choice == 'other' and custom_answer:
                # if custom response
                try:
                    response.validate_value(cleaned_value)
                except forms.ValidationError as error:
                    raise forms.ValidationError(error)
                else:
                    response.value = handle_user_input(cleaned_value)
                    response.choice = None
                    response.save()
            else:
                # if choice response
                filters = {'value_{0}'.format(question.response_type): cleaned_value}
                choices = question.failed_choices.filter(**filters)
                if choices.count():
                    response.choice = choices[0]
                    response.save()
                else:
                    # if there is not choice with passed value remove responses
                    remove_all_question_responses(question, certification, failed=True)

    if question.widget == SurveyQuestion.WIDGET_RADIO_CHECKBOX:
        key = 'response_{0}_{1}'.format(question.pk, question.order_str)
        _, question_pk, question_order = key.split('_')
        values = request.POST.getlist(key)
        # if radio button is off remove all responses
        if not values and question.hide_choices:
            remove_all_question_responses(
                question, certification, not_applicable_as_false=True, multiple=True, multiple_na=True
            )
            return

    # handle success responses
    # first of all delete all existing responses to this question
    remove_all_question_responses(question, certification, not_applicable_as_false=True, multiple=True)

    if max_answers_violated(question, request):
        raise forms.ValidationError(
            ngettext_lazy(
                f"{question.pervade_num} You can select only %(limit)s answer for this question.",
                f"{question.pervade_num} You can select up to %(limit)s answers for this question.",
                question.max_answers_amount
            ) % {"limit": question.max_answers_amount}
        )
    if min_answers_violated(question, request):
        raise forms.ValidationError(
            ngettext_lazy(
                f"{question.pervade_num} You must select at least %(limit)s answer for this question.",
                f"{question.pervade_num} You must select at least %(limit)s answers for this question.",
                question.min_answers_amount
            ) % {"limit": question.min_answers_amount}
        )

    for choice in question.choices.all():
        # handle choices
        key = 'response_{0}_{1}_{2}'.format(
            question.pk,
            question.order_str,
            choice.pk
        )
        value = normalize_value(question.response_type, key, request.POST.get(key), request.POST, request.FILES)
        if value is None:
            continue

        # if value is not None let's validate it and save if it's valid
        form = ResponseForm(question=question, data={key: value}, files=request.FILES, certification=certification)
        if not form.is_valid():
            raise forms.ValidationError(form.errors)
        cleaned_value = form.cleaned_data.get(key, value)

        response, created = SurveyResponse.objects.get_or_create(
            survey=certification.survey,
            question_id=question.pk,
            choice_id=choice.pk
        )
        try:
            response.validate_value(cleaned_value)
        except forms.ValidationError as error:
            raise forms.ValidationError(error)
        else:
            response.value = handle_user_input(cleaned_value)
            response.not_applicable = False
            response.save()
            remove_all_question_responses(question, certification, failed=True)

    # handle custom answer
    if question.other_form:
        choice = question.choices.filter(allow_custom_input=True).first()
        if choice:
            # if there is a choice with custom answer enabled handle it
            def get_choice_checkbox_name() -> str:
                """
                Returns checkbox name for custom answer.
                """
                choice = question.choices.filter(allow_custom_input=True).first()
                if choice:
                    # if there is a choice with custom answer enabled return checkbox name for it
                    return f"response_{question.pk}_{question.order_str}_{choice.pk}"
                else:
                    # else return checkbox name for "Other" choice
                    return f"response_{question.pk}_{question.order_str}_other"

            # handle it only in case of other form is enabled for a question
            checkbox_key = get_choice_checkbox_name()
            key = 'response_{0}_{1}_{2}'.format(
                question.pk,
                question.order_str,
                'other_value'
            )
            try:
                value = normalize_value(question.response_type, key, request.POST.get(key), request.POST, request.FILES)
            except SkipQuestion:
                create_na(certification, question)
                return
            if not value and request.POST.get(checkbox_key):
                # if checkbox is checked but value is empty raise validation error
                form.add_error(
                    form.field_name,
                    f"If you select \"{choice.value_text}\" you must provide a custom answer."
                )
                # uncheck choice checkbox by deleting choice response
                SurveyResponse.objects.filter(
                    survey=certification.survey,
                    question_id=question.pk,
                    choice_id=choice.pk
                ).delete()
                raise forms.ValidationError(form.errors)
            if request.POST.get(checkbox_key) is None:
                # None value means we need to delete all custom responses that belong to this question
                SurveyResponse.objects.filter(
                    survey=certification.survey,
                    question_id=question.pk,
                    choice__isnull=True
                ).delete()
                # in this case we don't need to execute the code below
                create_na(certification, question)
                return

            # if value is not None let's validate it and save if it's valid
            form = ResponseForm(question=question, data={key: value}, files=request.FILES, certification=certification)
            if not form.is_valid():
                create_na(certification, question)
                raise forms.ValidationError(form.errors)
            cleaned_value = form.cleaned_data.get(key, value)

            response, created = SurveyResponse.objects.get_or_create(
                survey=certification.survey,
                question_id=question.pk,
                choice=None
            )
            try:
                response.validate_value(cleaned_value)
            except forms.ValidationError as error:
                create_na(certification, question)
                raise forms.ValidationError(error)
            else:
                response.value = handle_user_input(cleaned_value)
                response.not_applicable = False
                response.save()

    # handle organisation address input fields
    fields = {
        SurveyQuestion.WIDGET_ORGANISATION_ADDRESS: SurveyQuestion.WIDGET_ORGANISATION_ADDRESS_FIELDS,
        SurveyQuestion.WIDGET_ORGANISATION_IT: SurveyQuestion.WIDGET_ORGANISATION_IT_FIELDS,
        SurveyQuestion.WIDGET_FULL_NAME: SurveyQuestion.WIDGET_FULL_NAME_FIELDS
    }
    if question.widget in fields.keys():
        response_fields = fields[question.widget]
        response_keys_prefix = 'response_{0}_{1}_'.format(question.pk, question.order_str)
        keys = ['{0}{1}'.format(response_keys_prefix, field) for field in response_fields]

        for key in keys:
            try:
                value = normalize_value(question.response_type, key, request.POST.get(key), request.POST, request.FILES)
            except SkipQuestion:
                continue

            if value is None or request.POST.get(key) is None:
                # None value means we need to delete a response for this input
                SurveyResponse.objects.filter(
                    survey=certification.survey,
                    question_id=question.pk,
                    choice__isnull=True,
                    code=key.split('_')[-1]
                ).delete()
                # in this case we don't need to execute the code below
                continue
            # if value is not None let's validate it and save if it's valid
            form = ResponseForm(question=question, data={key: value}, files=request.FILES, certification=certification)
            if not form.is_valid():
                raise forms.ValidationError(form.errors)
            cleaned_value = form.cleaned_data.get(key, value)

            response, created = SurveyResponse.objects.get_or_create(
                survey=certification.survey,
                question_id=question.pk,
                choice=None,
                code=key.split('_')[-1]
            )
            try:
                response.validate_value(cleaned_value)
            except forms.ValidationError as error:
                response.delete()
                raise forms.ValidationError(error)
            else:
                response.value = handle_user_input(cleaned_value)
                response.not_applicable = False
                response.save()

    # if after handling question doesn't have any responses we need to create one (N/A)
    create_na(certification, question)


def handle_single_value_questions(question, request, certification, question_key = None, code = None):
    """
    Handles questions that can have only one response and have no choices.
    :param question: survey question
    :type question: rulebook.models.SurveyQuestion instance
    :param request: http request
    :type request: django.http.HttpRequest instance
    :param certification: organisation certification
    :param question_key: optional question key
    :param code: optional response code
    :type certification: organisations.models.OrganisationCertification instance
    :return: nothing
    :rtype: None
    """
    key = question_key
    if key is None:
        key = f'response_{question.pk}_{question.order_str}'
    question_pk = question.pk
    try:
        value = normalize_value(question.response_type, key, request.POST.get(key), request.POST, request.FILES)
    except SkipQuestion:
        return

    filters = Q(survey=certification.survey) & Q(question_id=question_pk)
    if code is not None:
        filters &= Q(code=code)

    if value is None:
        # None value means we need to delete all responses that belong to this question
        fields = {
            'not_applicable': True,
        }
        if question.response_type == SurveyQuestion.BOOLEAN:
            fields['value_boolean'] = False
        SurveyResponse.objects.filter(filters).update(**fields)
        # in this case we don't need to execute the code below
        return

    # if value is not None let's validate it and save if it's valid
    form = ResponseForm(question=question, data={key: value}, files=request.FILES, certification=certification)
    if not form.is_valid():
        raise forms.ValidationError(form.errors)
    cleaned_value = form.cleaned_data.get(key, value)
    """
    We have an ability to change questions from multiple responses to single response.
    Then there is can be a situation when questions that supposed to handle only single response have multiple responses
    because their type was changed from multiple to single response.
    To avoid this situation we need to leave only the last response and the rest of them we remove.
    """
    try:
        response, created = SurveyResponse.objects.get_or_create(
            survey=certification.survey,
            question_id=question_pk,
            code=code,
        )
    except SurveyResponse.MultipleObjectsReturned:
        first = SurveyResponse.objects.filter(filters).first()
        response = first
        # remove other responses
        SurveyResponse.objects.filter(filters).exclude(pk=first.pk).delete()
    """
    ************************************
    """
    try:
        response.validate_value(cleaned_value)
    except forms.ValidationError as e:
        response.delete()
        raise forms.ValidationError(e)
    else:
        response.value = handle_user_input(cleaned_value)
        response.not_applicable = False
        response.save()
        # also save company name for trial organisations
        if question.widget == SurveyQuestion.WIDGET_ORGANISATION_SEARCH:
            if certification.organisation.is_trial:
                certification.organisation.name = response.value
                certification.organisation.save()


def handle_org_type_questions(question, request, certification):
    """
    Handles questions that can have only one response and have no choices.
    :param question: survey question
    :type question: rulebook.models.SurveyQuestion instance
    :param request: http request
    :type request: django.http.HttpRequest instance
    :param certification: organisation certification
    :type certification: organisations.models.OrganisationCertification instance
    :return: nothing
    :rtype: None
    """
    industry_description_key = f'response_{question.pk}_{question.order_str}_industry_description'
    handle_single_value_questions(question, request, certification)
    handle_single_value_questions(question, request, certification, industry_description_key, RESPONSE_CODE_INDUSTRY_DESCRIPTION)


def handle_questions_with_parent_yes_no_radio_button(question, certification, is_parent_2=False):
    # validate questions that depend on parent Yes/No radio button
    # get parent question response
    response = SurveyResponse.objects.filter(
        not_applicable=False,
        question=question.parent if not is_parent_2 else question.parent_2, survey__certificate=certification
    ).first()
    parent_value = response.value_boolean if response else None

    if not is_parent_2 and question.parent.show_children_on == bool(parent_value):
        return False

    # Can't user bool() because parent_value can be None
    if is_parent_2 and question.parent_2.show_children_on_2 == parent_value:
        return False

    # create not applicable response
    """
    We have an ability to change questions from multiple responses to single response.
    Then there is can be a situation when questions that supposed to handle only single response
    have multiple responses because their type was changed from multiple to single response.
    To avoid this situation we need to leave only the last response and the rest of them we remove.
    """
    try:
        response, _ = SurveyResponse.objects.get_or_create(
            survey=certification.survey,
            question=question
        )
    except SurveyResponse.MultipleObjectsReturned:
        first = SurveyResponse.objects.filter(
            survey=certification.survey,
            question=question
        ).first()
        response = first
        # remove other responses
        SurveyResponse.objects.filter(
            survey=certification.survey,
            question=question
        ).exclude(pk=first.pk).delete()
    """
    ************************************
    """
    response.not_applicable = True
    response.value_boolean = None
    response.save()
    return True


def normalize_value(response_type, key, value, post, files):
    """
    Normalize values that come from post request.
    :param response_type: question response type
    :type response_type: rulebook.models.SurveyQuestion.RESPONSE_TYPE_CHOICES element
    :param key: post key for question response value
    :type key: str, unicode
    :param value: response value
    :type value: any type
    :param post: request post
    :type post: django.http.HttpRequest.POST instance
    :param files: request files
    :type files: django.http.HttpRequest.FILES instance
    :return: normalized value
    :rtype: str, int, None
    """
    # by default if radio widget is selected as false it doesn't send this value to the server
    # so in this case we should assume that user selected false on radio widget
    # in our case django expects empty string as false value for radio
    if response_type == SurveyQuestion.BOOLEAN:
        if key not in post:
            return ''
        else:
            return value
    # for text widget we assume that if value is empty string then we need to set this field to None
    # and eventually delete all responses that belongs to this question
    if response_type == SurveyQuestion.TEXT:
        if value == '':
            return None
        else:
            return value

    if response_type == SurveyQuestion.FILE or response_type == SurveyQuestion.IMAGE:
        clear_file = key + '-clear'
        if key not in files:
            if clear_file in post:
                return None
            else:
                raise SkipQuestion()

        else:
            return files.get(key)

    if response_type == SurveyQuestion.FLOAT:
        if value == '':
            return None
        else:
            return value

    # for other types just return value from POST
    return value


def remove_all_question_responses(
        question, certification, failed=False, not_applicable_as_false=False, multiple=False, multiple_na=False
):
    """
    Deletes all responses that belongs to this question
    including responses to question's children.
    :param question: survey question
    :type question: rulebook.models.SurveyQuestion
    :param certification: organisation certification
    :type certification: organisations.models.OrganisationCertification instance
    :param failed: remove failed responses only
    :type failed: bool
    :param not_applicable_as_false: save boolean value with not_applicable
    :type not_applicable_as_false: bool
    :param multiple: question assumes multiple responses
    :type multiple: bool
    :param multiple_na: after removing all responses create one N/A response
    :type multiple_na: bool
    :return: nothing
    :rtype: None
    """
    # question children's id
    questions_ids = list(question.children.all().values_list('pk', flat=True))
    # add parent id
    questions_ids.append(question.pk)

    if failed:
        SurveyFailedResponse.objects.filter(
            survey=certification.survey,
            question_id__in=questions_ids,
        ).delete()
        return

    if not multiple:
        # handle questions that can have only one response
        fields = {
            'not_applicable': True
        }
        if not_applicable_as_false:
            fields['value_boolean'] = False
        # first try to update
        existing_responses = SurveyResponse.objects.filter(
            survey=certification.survey,
            question_id__in=questions_ids,
        )
        existing_responses.update(**fields)
        # in case of some responses don't exist create them
        for question_id in set(questions_ids) - set(existing_responses.values_list('pk', flat=True)):
            if not SurveyResponse.objects.filter(
                survey=certification.survey,
                question_id=question_id,
                **fields
            ).first():
                SurveyResponse.objects.create(
                    survey=certification.survey,
                    question_id=question_id,
                    **fields
                )
    else:
        # handle questions that can have multiple responses
        # in this case we remove all responses and create one not applicable response
        SurveyResponse.objects.filter(
            survey=certification.survey,
            question_id__in=questions_ids,
        ).delete()
        if multiple_na:
            # create N/A response
            fields = {
                'survey': certification.survey,
                'question_id': question.pk,
                'not_applicable': True
            }
            if not_applicable_as_false:
                fields['value_boolean'] = False
            SurveyResponse.objects.create(
                **fields
            )


class SurveyTopicStat:
    """
    Creates a map where a key is either topic title or topic primary key and value is a dict with:
    - `total` - total number of questions that need to be answered
    - `answered` - number of answered questions
    - `left` - number of questions that left to answer
    - `order` - topic order
    Example:
    {
        1: {
            "total": 10,
            "answered": 5,
            "left": 5,
            "order": 1
        },
        2: {
            "total": 10,
            "answered":9,
            "left": 1,
            "order": 2
        }
    }

    or if main_key_as_title is True then it will be:

    {
        "Organisation": {
            "total": 10,
            "answered": 5,
            "left": 5,
            "order": 1
        },
        "Scope of Assessment": {
            "total": 10,
            "answered":9,
            "left": 1,
            "order": 2
        }
    """
    def __init__(self, certification: OrganisationCertification, main_key_as_title=False) -> None:
        """
        Initialises SurveyTopicStat instance.
        """
        self.certification = certification
        self.topics = {}
        self.main_key_as_title = main_key_as_title

    def _get_main_key(self, question: SurveyQuestion) -> str | int:
        """
        Returns main key for the map.
        If `main_key_as_title` is True, then it will be topic title, otherwise topic primary key.
        """
        if self.main_key_as_title:
            return question.topic.title
        else:
            return question.topic.pk

    def _update_topic(self, question: SurveyQuestion) -> None:
        """
        Updates topic info.
        """
        if hasattr(question, 'responses'):  # if responses are prefetched
            is_answered = bool(question.responses.all())
        else:
            is_answered = question.responses.filter(not_applicable=False).exists()

        if self._get_main_key(question) in self.topics:
            self._update_topic_counters(is_answered, question)
        else:
            self._add_new_topic(is_answered, question)

    def _add_new_topic(self, is_answered: bool, question: SurveyQuestion) -> None:
        """
        Adds a new topic to the map.
        """
        self.topics[self._get_main_key(question)] = {
            "total": 1,
            "answered": 1 if is_answered else 0,
            "left": 1 if not is_answered else 0,
            "order": question.topic.order
        }

    def _update_topic_counters(self, is_answered: bool, question: SurveyQuestion) -> None:
        """
        Updates existing topic counters.
        """
        topic = self.topics[self._get_main_key(question)]
        topic["total"] += 1
        if is_answered:
            # if a question is answered increase answered counter
            topic["answered"] += 1
        else:
            # if a question is not answered increase left counter
            topic["left"] += 1

    def get(self) -> dict:
        """
        Returns topics map.
        """
        self._process_questions()
        # sort topics by order and return
        return dict(sorted(self.topics.items(), key=lambda item: item[1]["order"]))

    def _parent_2_yes_or_no_is_not_applicable(self, question: SurveyQuestion) -> bool:
        """
        Checks if parent 2 Yes/No radio button is not applicable.
        """
        if not question.parent_2:
            return False
        # first get parent 2 question answer
        if response := SurveyResponse.objects.filter(
            not_applicable=False,
            question=question.parent_2, survey__certificate=self.certification
        ).first():
            parent_value = response.value_boolean
        else:
            parent_value = None

        if question.parent_2.show_children_on_2 != parent_value:
            # if parent 2 answer is not the same as show_children_on value, then skip this question
            return True
        return False

    @property
    def _questions_queryset(self) -> QuerySet:
        """
        Returns questions queryset.
        """
        return SurveyQuestion.objects.filter_insurer_options(self.certification).filter(
            version=self.certification.version,
            topic__isnull=False
        ).prefetch_related(
            Prefetch(
                'mandatory_for_parent_choices', SurveyQuestionChoices.objects.filter(
                    question__version__organisationcertification=self.certification
                ).prefetch_related(
                    Prefetch(
                        'responses', SurveyResponse.objects.filter(
                            survey__certificate=self.certification,
                            not_applicable=False
                    )
                    )
                )
            )
        ).order_by("order").select_related("parent", "topic").prefetch_related(
            Prefetch("responses", SurveyResponse.objects.filter(
                survey=self.certification.survey, not_applicable=False
            ))
        )

    def _process_questions(self) -> None:
        """
        Processes questions queryset and updates topics map.
        """
        questions = self._questions_queryset

        parent_responses = SurveyResponse.objects.filter(
            question__in=questions.values('parent'),
            not_applicable=False,
            survey__certificate=self.certification
        ).select_related('choice')
        parent_choice_response_lookup = {
            (r.question_id, r.choice_id): r for r in parent_responses
        }
        parent_response_lookup = {
            r.question_id: r for r in parent_responses
        }
        for question in questions:
            if question.parent:
                # If parent Yes/No radio button is not applicable then skip this question
                if question.parent_id in parent_response_lookup:
                    parent_value = parent_response_lookup[question.parent_id].value_boolean
                else:
                    parent_value = None
                if question.parent.show_children_on != parent_value:
                    continue

                # Checks if parent choice is not applicable.
                # In this case, we need to skip this question as it depends on parent choice.
                any_parent_choice_is_applicable = any((question.parent_id, choice.id) in parent_choice_response_lookup
                           for choice in question.mandatory_for_parent_choices.all())
                if not any_parent_choice_is_applicable:
                    continue
            elif question.parent_2:
                if self._parent_2_yes_or_no_is_not_applicable(question):
                    continue
            self._update_topic(question)


def get_topics_status_structure(certification, structure, include_required=False, recognize_incomplete=False):
    """
    Returns questionnaire topics status.
    By default questions without ranking excluded from passed and failed containers.
    But containers named passed_total and failed_total include questions without ranking.
    {
        'passed': [topic.pk, ...],
        'passed_total': [topic.pk, ...], # including without_ranking
        'failed': [topic.pk, ...],
        'failed_total': [topic.pk, ...], # including without_ranking
        'without_ranking': [topic.pk, ...]
    }
    :param certification: organisation certification
    :type certification: organisations.OrganisationCertification
    :param structure: questionnaire structure
    :type structure: get_questionnaire_status_structure response
    :param include_required: includes required questions (without ranking) to passed and failed containers
    :type include_required: bool
    :param recognize_incomplete: if True then topics with non-answered questions are treated separately as incomplete
    :type recognize_incomplete: bool
    :return: topics status
    :rtype: dict
    """
    topics = SurveyQuestionTopic.objects.filter(
        version=certification.version
    ).prefetch_related(Prefetch(
        'questions', SurveyQuestion.objects.filter_insurer_options(certification).filter(
            version=certification.version,
        ), to_attr='prefetched_questions'
    ))
    failed_question_ids = {k for k, v in structure.items() if (not recognize_incomplete and not v) or (recognize_incomplete and v is False)}
    incomplete_question_ids = {k for k, v in structure.items() if recognize_incomplete and v is None}

    topic_status = {}
    for topic in topics:
        topic_questions = set(q.pk for q in topic.prefetched_questions)
        has_failed = bool(topic_questions & failed_question_ids)
        has_incomplete = bool(topic_questions & incomplete_question_ids)
        topic_questions_count = len(topic.prefetched_questions)

        questions_without_ranking_count = len([q for q in topic.prefetched_questions if q.ranking == SurveyQuestion.RANKING_NONE])
        questions_without_ranking_and_not_required_count = len([q for q in topic.prefetched_questions if q.ranking == SurveyQuestion.RANKING_NONE and not q.required])

        topic_status[topic.pk] = {
            'passed': (recognize_incomplete and not has_incomplete and not has_failed) or (not recognize_incomplete and not has_failed),
            'incomplete': has_incomplete and not has_failed,
            'failed': has_failed,
            'without_ranking': questions_without_ranking_count == topic_questions_count,
            'without_ranking_and_not_required': questions_without_ranking_and_not_required_count == topic_questions_count
        }

    without_ranking = [pk for pk, status in topic_status.items() if status['without_ranking']]
    without_ranking_and_not_required = [pk for pk, status in topic_status.items() if status['without_ranking_and_not_required']]
    excluded = without_ranking_and_not_required if include_required else without_ranking

    failed_total = [pk for pk, status in topic_status.items() if status['failed']]
    failed = list(set(failed_total) - set(excluded))

    incomplete_total = [pk for pk, status in topic_status.items() if status['incomplete']]
    incomplete = list(set(incomplete_total) - set(excluded))

    passed_total = [pk for pk, status in topic_status.items() if status['passed']]
    passed = list(set(passed_total) - set(excluded))

    return {
        'passed': passed,
        'passed_total': passed_total,
        'failed': failed,
        'failed_total': failed_total,
        'incomplete': incomplete,
        'incomplete_total': incomplete_total,
        'without_ranking': without_ranking,
        'without_ranking_and_not_required': without_ranking_and_not_required
    }


def get_questionnaire_status_structure(
        certification,
        answered=None,
        key_as_pk=False,
        skip_without_ranking=False,
        skip_not_required=False,
        acceptance=False,
        no_response_as_none=False,
):
    """
    Returns questionnaire structure with responded and other questions.
    {question number or question pk: True or False} True if a question has response, False if it doesn't
    :param certification: organisation certification
    :type certification: organisations.OrganisationCertification
    :param answered: if True returns only answered questions, if False returns only not answered questions
    :type answered: bool or None
    :param key_as_pk: use question's pk as key instead of question order
    :type: bool
    :param skip_without_ranking: skips questions without ranking
    :type skip_without_ranking: bool
    :param skip_not_required: skips not required questions
    :type skip_not_required: bool
    :param acceptance: considers answers that were not accepted by an assessor as failed (if True)
    :type acceptance: bool
    :param no_response_as_none: if True then questions without responses are saved as None, otherwise as False
    :type no_response_as_none: bool
    :return: questionnaire structure
    :rtype: dict {int: bool}
    """
    structure = {}

    if hasattr(certification.survey, 'assessment') and certification.survey.assessment.is_sent_back:
        notes_filter = {'pk__in': AssessorNote.objects.filter(
            assessment__survey=certification.survey, re_answered=False, answer_accepted=False
        ).values_list('question_id', flat=True).distinct('pk')}
    else:
        notes_filter = {}

    questions = SurveyQuestion.objects.filter_insurer_options(certification).filter(
        version=certification.version,
    ).order_by(
        'order'
    ).select_related(
        'parent'
    ).prefetch_related(
        Prefetch(
            'responses', SurveyResponse.objects.filter(
                survey=certification.survey, not_applicable=False
            )
        )
    ).prefetch_related(
        Prefetch(
            'responses', SurveyResponse.objects.filter(
                survey=certification.survey,
                not_applicable=False,
                assessor_acceptance=True
            ), to_attr='accepted_responses'
        )
    ).prefetch_related(
        Prefetch(
            'mandatory_for_parent_choices', SurveyQuestionChoices.objects.filter(
                question__version__organisationcertification=certification
            ).prefetch_related(
                Prefetch(
                    'responses', SurveyResponse.objects.filter(
                        survey__certificate=certification,
                        not_applicable=False
                    )
                )
            )
        )
    ).prefetch_related(
        'parent', Prefetch(
            'parent__responses', SurveyResponse.objects.filter(
                survey__certificate=certification, not_applicable=False
            ).select_related('choice')
        )
    ).filter(**notes_filter).annotate(
        response_has_invalid_choice=Exists(
            SurveyResponse.objects.filter(
                question=OuterRef('pk'),
                survey=certification.survey,
                choice__is_invalid=True,
            )
        )
    )

    if skip_not_required:
        questions = questions.exclude(required=False)
    elif skip_without_ranking:
        questions = questions.exclude(ranking=SurveyQuestion.RANKING_NONE)

    parent_responses = SurveyResponse.objects.filter(
        question__in=questions.values('parent'),
        not_applicable=False,
        survey__certificate=certification
    ).select_related('choice')

    parent_response_lookup = {
        (r.question_id, r.choice_id): r for r in parent_responses
    }

    for question in questions:
        dict_key = question.pk if key_as_pk else question.order_str
        if question.parent:
            # validate questions that depend on parent question choice answer
            if question.mandatory_for_parent_choices.exists():
                # check if parent question has choice answer that required to show this child question
                if not any((question.parent_id, choice.id) in parent_response_lookup
                           for choice in question.mandatory_for_parent_choices.all()):
                    # if it doesn't, skip this question
                    continue
            else:
                # validate questions that depend on parent Yes/No radio button
                parent_value = next(
                    (r.value_boolean for r in question.parent.responses.all() if r.survey_id == certification.survey.id),
                    None
                )
                # skip this question, it's not required. Note that we can't use bool() here since parent_value can be None
                if question.parent.show_children_on != parent_value:
                    continue
        elif question.parent_2:
            response = question.parent_2.responses.filter(survey=certification.survey).first()
            parent_value = response.value_boolean if response else None
            if question.parent_2.show_children_on_2 != parent_value:
                continue
        # validate rest of questions
        responses_count = question.responses.count()
        if no_response_as_none is True and responses_count == 0:
            structure[dict_key] = None
        elif (
                not responses_count
                or (responses_count < 2 and question.widget
                    in (SurveyQuestion.WIDGET_ORGANISATION_IT, SurveyQuestion.WIDGET_FULL_NAME))
                or (question.widget in (
                        SurveyQuestion.WIDGET_RADIO,
                        SurveyQuestion.WIDGET_RADIO_SIMPLE,
                        SurveyQuestion.WIDGET_RADIO_TEXTAREA,
                        SurveyQuestion.WIDGET_CONSCIOUS
                    ) and question.response_has_invalid_choice)
                or question.widget == SurveyQuestion.WIDGET_ORGANISATION_ADDRESS and (question.responses.filter(code__in=[
                    'address1', 'postcode', "country"
                ]).count() < 3)):
            # if no response then question is failed
            structure[dict_key] = False
        else:
            structure[dict_key] = determine_answer_compliance(certification, question)
        # if acceptance is True and assessor didn't accept this question then its answers considered as failed
        if acceptance:
            if not len(question.accepted_responses):
                structure[dict_key] = False
    if answered is not None:
        if answered:
            return {k: v for k, v in structure.items() if v}
        else:
            return {k: v for k, v in structure.items() if not v}
    return structure


def answer_is_compliant(question) -> bool:
    """
    Checks if the answer for given question is compliant or not.
    """
    answer = question.responses.first()
    if all([
        question.compliant_answer not in [SurveyQuestion.ANY, SurveyQuestion.NA],
        answer.value_boolean != (question.compliant_answer == SurveyQuestion.YES)
    ]):
        return False
    return True


def determine_answer_compliance(certification, question):
    """
    Determines if answer for passed question is compliant or not.
    :param certification: organisation certification
    :type certification: OrganisationCertification
    :param question: survey question
    :type question: SurveyQuestion
    :return: True or False
    :rtype: bool
    """
    return all([
        insurance_validation(certification, question),
        yes_no_text_widget_validation(question),
        answer_is_compliant(question),
        operating_system_validation(certification, question)
    ])


def organisation_number_validation(certification: OrganisationCertification, question: SurveyQuestion, data: dict) -> bool:
    """
    Does additional validation for A1.3 Organisation Number (registration number) question
    depending on answer given in A1.2 Organisation Type question.
    """
    if not (
        certification.is_cyber_essentials and
        certification.version_number > 2024 and
        question.widget == SurveyQuestion.WIDGET_ORGANISATION_REGISTRATION and
        question.code == QUESTION_CODE_REGISTRATION_NUMBER
    ):
        return True

    values = list(data.values())
    answer = values[0] if values else None
    org_type_answer = certification.survey.get_answer_for_code(QUESTION_CODE_ORGANISATION_TYPE)
    if not answer or not org_type_answer:
        return False

    if re.match('none', answer, flags=re.IGNORECASE):
        return any(org_type in org_type_answer for org_type in [
            'OTH - Other', 'SOC - Other', 'PRT - Other', 'SOL - Sole', 'GOV - Government'
        ])

    return True


def operating_system_validation(certification, question) -> bool:
    """
    Does additional validation for A2.4 End User Devices question
    If answer contains unsupported OS release then it's not compliant.
    """
    if certification.is_cyber_essentials and certification.version_number >= 2022:
        if question.pervade_title and question.pervade_title.startswith("A2.4 End User Devices"):
            answer = question.responses.first()
            if answer and isinstance(answer.value, str):
                releases = [release.strip().lower() for release in answer.value.split(",") if release]
                unsupported_releases = get_organisation_unsupported_os_releases(certification.organisation)
                for release in releases:
                    for unsupported_release in unsupported_releases:
                        if unsupported_release.lower() in release.lower():
                            return False
    return True


def insurance_validation(certification, question):
    """
    Does additional validation for some question answers
    that will be used for insurance opt-in.
    :param certification: organisation certification
    :type certification: OrganisationCertification
    :param question: survey question
    :type question: SurveyQuestion
    :return: True or False
    :rtype: bool
    """
    if certification.is_superscript_insurance:
        # validate post code
        if question.widget == SurveyQuestion.WIDGET_ORGANISATION_ADDRESS:
            if certification.is_uk_postcode_validation_needed:
                post_code_response = question.responses.filter(code="postcode").first()
                if not post_code_response:
                    return False
                else:
                    try:
                        post_code_validator(post_code_response.value)
                    except forms.ValidationError:
                        return False
                    else:
                        return True
        # validate email
        if question.widget == SurveyQuestion.WIDGET_EMAIL:
            email_response = question.responses.all().first()
            if not email_response:
                return False
            else:
                try:
                    validate_email(email_response.value)
                except forms.ValidationError:
                    return False
                else:
                    return True
        return True
    else:
        return True


def yes_no_text_widget_validation(question):
    """
    Does additional validation for questions with Yes/No & Text widget.
    :param question: survey question with prefetched related data
    :type question: SurveyQuestion
    :return: if question answer is consider as passed or not
    :rtype: bool
    """
    if question.widget in (SurveyQuestion.WIDGET_YES_NO_TEXT, SurveyQuestion.WIDGET_COMMON_REASONS):
        if not question.responses.first().value_text:
            return False
        else:
            return True
    return True

def _format_device_to_str(platform, release, vendor, count):
    return f'{platform} - {release} - {vendor} [x{count}]'

def actualize_initial_responses(organisation, certification, return_values=False):
    """
    Actualizes some initial responses with actual data.
    :param organisation: organisation
    :type organisation: organisations.Organisation
    :param certification: organisation certification
    :type certification: organisation.OrganisationCertification
    :param return_values: returns updated responses values.
    :type return_values: bool
    :return: nothing
    :rtype: None
    """
    from appusers.utils import PLATFORM_DARWIN, MOBILE_PLATFORMS, MOBILE_PLATFORMS_DISPLAY

    updated_values = {}

    if certification.type == CYBER_ESSENTIALS:
        # for CE higher than 2018 the equipment answer should contain only laptops, computers and servers
        # for CE 2018 and lower the answer should also include tablets and mobile devices
        code = QUESTION_CODE_EQUIPMENT_2018 if certification.version_number <= 2018 else QUESTION_CODE_EQUIPMENT_DESKTOP
        equipment_question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
            code=code,
            version__organisationcertification=certification,
        )).first()

        # if CE version is higher than 2018 then exclude tables and mobile devices from "equipment_desktop" answer
        exclude = Q(os_info__isnull=True)
        if certification.version_number > 2018:
            exclude |= Q(platform__in=MOBILE_PLATFORMS)

        # if CE version is 2023 or higher, then exclude servers from "equipment_desktop" answer
        if certification.version_number >= 2023:
            exclude |= Q(device_type=AppInstall.SERVER)

        if equipment_question:
            # os_info_release - os release name
            # os_release - os version
            # if there is a duplicate with stable release, then do not include the beta release
            # Desktop is formatted like:
            # {os_info__release} - {os_release} - {machine_vendor} - [{counter}]
            # e.g: Windows 10 - 12H2 - Dell [x1]
            # e.g. Sonoma - 14 - Apple [x1] (note the major version of the release)
            apps_id = [app.pk for app in organisation.active_app_installs.exclude(exclude) if not (app.get_duplicate() and app.is_beta)]
            devices = list(AppInstall.objects.filter(
                pk__in=apps_id
            ).exclude(exclude).values('os_info__release', 'os_release').annotate(
                counter=Count('os_info')
            ).values('platform', 'os_info__release', 'os_release', 'machine_vendor', 'counter'))

            if devices:
                response, _ = SurveyResponse.objects.get_or_create(
                    survey=certification.survey,
                    question=equipment_question
                )

                devices_list = []
                for device in devices:
                    # if platform is darwin, get the major version of the release
                    formatted_os_release = device['os_release']
                    if device['platform'] == PLATFORM_DARWIN:
                        try:
                            formatted_os_release = parse_version(formatted_os_release).major
                        except Exception:
                            pass

                    devices_list.append(
                        _format_device_to_str(
                            device['os_info__release'],
                            formatted_os_release,
                            device['machine_vendor'],
                            device['counter']
                        )
                    )

                response.value_text = ', '.join(devices_list)
                response.not_applicable = False
                response.save()

                updated_values[code] = response.value_text

        # for CE higher than 2018 "equipment_mobile" answer should contain only tablets and mobile devices
        if certification.version_number > 2018:
            mobile_equipment_question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
                code=QUESTION_CODE_EQUIPMENT_MOBILE,
                version__organisationcertification=certification,
            )).first()

            if mobile_equipment_question:
                mobile_apps_id = organisation.active_app_installs.filter(platform__in=MOBILE_PLATFORMS).values_list('pk', flat=True)
                mobile_app_installs = AppInstall.objects.filter(
                    pk__in=mobile_apps_id
                ).values('machine_vendor', 'platform', 'release').annotate(counter=Count('release'))
                if mobile_app_installs:
                    response, _ = SurveyResponse.objects.get_or_create(
                        survey=certification.survey,
                        question=mobile_equipment_question
                    )
                    devices_list = []
                    for device in mobile_app_installs:
                        # CE currently only requires the platform and major version number
                        try:
                            formatted_release = parse_version(device['release']).major
                        except Exception:
                            formatted_release = ''
                        platform_display = MOBILE_PLATFORMS_DISPLAY.get(device['platform'], '')
                        if not platform_display:
                            logger.error(f"Unknown mobile platform: {device['platform']}")
                        devices_list.append(
                            _format_device_to_str(
                                platform_display,
                                formatted_release,
                                device['machine_vendor'] or 'Unknown vendor',
                                device['counter']
                            )
                        )

                    response.value_text = ', '.join(devices_list)
                    response.not_applicable = False
                    response.save()

                    updated_values[QUESTION_CODE_EQUIPMENT_MOBILE] = response.value_text

        if return_values:
            return updated_values


def create_initial_responses(request, organisation, certification):
    """
    Creates initial responses for some questions based on organisation data.
    :return: nothing
    :rtype: None
    """
    if certification.type == CYBER_ESSENTIALS:
        # create responses based on starling account
        if organisation.legal_company_name:
            # create a response for question 1 of CE
            question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
                code=QUESTION_CODE_ORGANISATION_NAME,
                version__organisationcertification=certification,
            )).first()
            if question:
                response, _ = SurveyResponse.objects.get_or_create(
                    survey=certification.survey,
                    question=question
                )
                response.value_text = organisation.legal_company_name
                response.not_applicable = False
                response.save()
        if organisation.legal_company_number:
            # create a response for registration number question
            question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
                code=QUESTION_CODE_REGISTRATION_NUMBER,
                version__organisationcertification=certification,
            )).first()
            if question:
                response, _ = SurveyResponse.objects.get_or_create(
                    survey=certification.survey,
                    question=question
                )
                response.value_text = organisation.legal_company_number
                response.not_applicable = False
                response.save()

        # create responses for company address question
        question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
            code=QUESTION_CODE_ORGANISATION_NAME,
            version__organisationcertification=certification,
        )).first()
        if question:
            if organisation.address.legal_company_address_line1:
                response = SurveyResponse.objects.create(
                    survey=certification.survey,
                    question=question
                )
                response.value_text = organisation.address.legal_company_address_line1
                response.not_applicable = False
                response.code = 'address1'
                response.save()
            if organisation.address.legal_company_address_line2:
                response = SurveyResponse.objects.create(
                    survey=certification.survey,
                    question=question
                )
                response.value_text = organisation.address.legal_company_address_line2
                response.not_applicable = False
                response.code = 'address2'
                response.save()
            if organisation.address.legal_company_address_town:
                response = SurveyResponse.objects.create(
                    survey=certification.survey,
                    question=question
                )
                response.value_text = organisation.address.legal_company_address_town
                response.not_applicable = False
                response.code = 'city'
                response.save()
            if organisation.address.legal_company_address_postcode:
                response = SurveyResponse.objects.create(
                    survey=certification.survey,
                    question=question
                )
                response.value_text = organisation.address.legal_company_address_postcode
                response.not_applicable = False
                response.code = 'postcode'
                response.save()

        # create response for company industry question
        question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
            code=QUESTION_CODE_ORGANISATION_INDUSTRY,
            version__organisationcertification=certification,
        )).first()
        if question:
            response, _ = SurveyResponse.objects.get_or_create(
                survey=certification.survey,
                question=question
            )
            response.value_text = organisation.industry or ''
            response.not_applicable = False
            response.save()
            if response.value_text == "OTHE" and organisation.industry_description:
                # create response for industry description question
                response, _ = SurveyResponse.objects.get_or_create(
                    survey=certification.survey,
                    question=question,
                    code=RESPONSE_CODE_INDUSTRY_DESCRIPTION
                )
                response.value_text = organisation.industry_description
                response.not_applicable = False
                response.save()
        # create response for company website question
        question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
            code=QUESTION_CODE_ORGANISATION_WEBSITE,
            version__organisationcertification=certification,
        )).first()
        if question and not request.user.profile.has_trial_email:
            response, _ = SurveyResponse.objects.get_or_create(
                survey=certification.survey,
                question=question
            )
            try:
                possible_website = organisation.get_organisation_creator.email.split('@')[1]
            except (AttributeError, IndexError):
                possible_website = ''
            response.value_text = possible_website
            response.not_applicable = False
            response.save()
        # create response for company size question
        question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
            code=QUESTION_CODE_ORGANISATION_SIZE,
            version__organisationcertification=certification,
        )).first()
        if question:
            response, _ = SurveyResponse.objects.get_or_create(
                survey=certification.survey,
                question=question
            )
            org_size_to_text_value = Organisation.get_pervade_organisation_size_dict()
            choice_size_to_pk = {choice.value.split(' ')[0]: choice.pk for choice in question.choices.all()}

            try:
                size = choice_size_to_pk[org_size_to_text_value[organisation.size]]
            except KeyError:
                size = None
            else:
                response.choice_id = size
                response.not_applicable = False
                response.value_text = None
                response.save()
        # create response for insurance email question
        question_code = QUESTION_CODE_INSURANCE_EMAIL_2018 if certification.version_number == 2018\
            else QUESTION_CODE_INSURANCE_EMAIL
        question = SurveyQuestion.objects.filter_insurer_options(certification).filter(Q(
            code=question_code,
            version__organisationcertification=certification,
        )).first()

        if question:
            response, _ = SurveyResponse.objects.get_or_create(
                survey=certification.survey,
                question=question
            )
            response.value_text = request.user.email
            response.not_applicable = False
            response.save()

    # create responses for some questions that have auto_answer option enabled and all conditions are passed
    # if an organisation has app installs
    if AppInstall.objects.filter(app_user__organisation=organisation, inactive=False).count():
        for sq in SurveyQuestion.objects.filter_insurer_options(certification).filter(
                version__organisationcertification=certification,
                auto_answer=True,
                related_check__isnull=False,
        ):
            _, failed_responses = get_applications_responses(
                org_pk=organisation.pk,
                order=('app_check__pk', 'report__app_install', '-report__modified', '-modified'),
                distinct=('app_check__pk', 'report__app_install'),
                fail_values=('app_check__pk',)
            )
            # if organisation doesn't have failed responses with this application question which is linked
            # to the current loop survey question then we can create a survey response
            if sq.related_check not in failed_responses.values_list('app_check__pk', flat=True):
                # handle simple questions
                if sq.widget == SurveyQuestion.WIDGET_DEFAULT and not sq.auto_answer_choice:
                    # currently supported only boolean questions
                    if sq.response_type == SurveyQuestion.BOOLEAN:
                        response, _ = SurveyResponse.objects.get_or_create(
                            survey=certification.survey,
                            question=sq
                        )
                        response.value_boolean = True
                        response.not_applicable = False
                        response.save()
                # handle question with choices
                else:
                    # supported all types of choices
                    response, _ = SurveyResponse.objects.get_or_create(
                        survey=certification.survey,
                        question=sq
                    )
                    response.choice = sq.auto_answer_choice
                    response.not_applicable = False
                    response.save()


def get_questionnaire_html_report(request, organisation, type, version_pk=None):
    """
    Returns html report for questionnaire.
    :param request: http request
    :type request: django.http.HttpRequest
    :param organisation: organisation instance
    :type organisation: organisations.Organisation
    :param type: certification type
    :param version_pk: certification version primary key
    :type version_pk: int
    :type type: int
    :return: html report
    :rtype: string
    """
    certification = organisation.get_latest_certificate(type=type, version_pk=version_pk)
    if not certification:
        raise Http404()

    if not hasattr(certification, 'survey'):
        # if survey not started yet
        context = {
            'certification': certification,
            'organisation': organisation
        }
    else:
        # if survey already started
        context = {
            'organisation': organisation,
            'certification': certification,
            'topics': SurveyQuestionTopic.objects.annotate(q=Count('questions')).filter(
                q__gt=0, version=certification.version
            ).prefetch_related(
                Prefetch('questions', SurveyQuestion.objects.filter_insurer_options(certification).filter(
                    version__organisationcertification=certification,
                ).order_by('order').prefetch_related(Prefetch(
                    'failed_choices', SurveyQuestionFailedChoices.objects.filter(
                        question__version__organisationcertification=certification
                    )
                )).prefetch_related(Prefetch('failed_responses', SurveyFailedResponse.objects.filter(
                    survey__certificate=certification
                ))).prefetch_related(Prefetch('choices', SurveyQuestionChoices.objects.filter(
                    question__version__organisationcertification=certification
                ).prefetch_related(
                    'mandatory_questions'
                ).prefetch_related(Prefetch('responses', SurveyResponse.objects.filter(
                    survey__certificate=certification, not_applicable=False
                ))))).prefetch_related(
                    'parent', Prefetch(
                        'parent__responses', SurveyResponse.objects.filter(
                            survey__certificate=certification, not_applicable=False
                        )
                    ), 'children',
                ).prefetch_related(Prefetch(
                    'mandatory_for_parent_choices', SurveyQuestionChoices.objects.filter(
                        question__version__organisationcertification=certification
                    ).prefetch_related(
                        Prefetch('responses', SurveyResponse.objects.filter(
                            survey__certificate=certification, not_applicable=False
                        ))
                    )
                )))
            ).prefetch_related(Prefetch(
                'questions__responses', SurveyResponse.objects.filter(
                    survey=certification.survey,
                    not_applicable=False
                )
            )),
        }
        structure = get_questionnaire_status_structure(certification, key_as_pk=True)
        context['topics_status_structure'] = get_topics_status_structure(certification, structure)
    return render_to_string('dashboard/certification/html_report.html', context, request)


def get_pervade_api_survey(certification_pk):
    """
    Returns responses for passed certification.
    :param certification_pk: certification primary key
    :type certification_pk: int or str
    :return: survey responses, all question names
    :rtype: tuple
    """
    certification = OrganisationCertification.objects.get(pk=certification_pk)

    if certification.version_number == 2022:
        modern_version = "2022"
    elif 2022 < certification.version_number < 2023:
        modern_version = "2022_5"
    elif certification.version_number >= 2025:
        modern_version = "2025"
    elif certification.version_number >= 2023:
        modern_version = "2023"
    else:
        modern_version = None

    if modern_version and certification.type in [CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS]:
        # for modern versions of cyber essentials and cyber essentials plus we use new function to get answers
        if certification.is_cyber_essentials:
            try:
                SurveyAnswers = importlib.import_module(
                    f"rulebook.pervade.v{modern_version}.cyber_essentials.data"
                ).SurveyAnswers
            except ModuleNotFoundError:
                # if module not found, fallback to last defined version
                from rulebook.pervade.v2023.cyber_essentials.data import SurveyAnswers
        elif certification.is_cyber_essentials_plus:
            SurveyAnswers = importlib.import_module(
                f"rulebook.pervade.v{modern_version}.cyber_essentials_plus.data"
            ).SurveyAnswers
        else:
            from rulebook.pervade.vcommon.data import BaseSurveyAnswers as SurveyAnswers
        answers = SurveyAnswers(certification.survey.pk).get()
        return answers, [answer.get("name") for answer in answers if answer.get("name")]

    def wrap_checkbox_answer_in_list(survey_response: SurveyResponse, answer_value: str):
        """
        Wraps question answers that use checkbox widget in list.
        It also ignores answers that does not have choice value set (like "Other" answers).
        Example: "answer value" -> ["answer value"]
        """
        if survey_response.question.widget in [SurveyQuestion.WIDGET_CHECKBOX, SurveyQuestion.WIDGET_RADIO_CHECKBOX]:
            if not survey_response.choice:
                return []
            return [answer_value]
        return answer_value

    responses = [
        {
            'name': answer.question.pervade_title,
            'id': answer.question.pervade_id,
            'Applicant Notes': answer.pervade_applicant_notes_value,
            "fields": answer.pervade_compliance_fields,
            'notes': answer.pervade_assessor_notes_value,
            'Assessor Notes': answer.pervade_assessor_notes_value,
            'score': answer.pervade_assessor_score,
            'answer': wrap_checkbox_answer_in_list(answer, str(answer.pervade_answer_value))
        } for answer in SurveyResponse.objects.filter(
            Q(code__isnull=True) | Q(code='country', question__pervade_title__icontains="Organisation Address"),
            survey__certificate_id=certification_pk,
            question__version=certification.version,
            question__pervade_title__isnull=False,
        ).exclude(question__pervade_title='').exclude(
            question__pervade_title__startswith="A3.2", question__pervade_title__endswith="Cyber Insurance"
        ).exclude(question__pervade_ignored=True)
    ]
    # we exclude "A3.2 Cyber Insurance" from above list because this will be added by the code below
    firewall_services_yes = False
    # combine multiple responses into single response
    combined_responses = {}
    for r in responses:
        if not r['id']:
            del r['id']
        # if notes is not set, remove this field (do not overwrite Pervade added values)
        if not r['notes']:
            del r['notes']
        if r['name'] not in combined_responses:
            combined_responses[r['name']] = r
        else:
            if all(
                    [
                        certification.is_cyber_essentials,
                        certification.version_number >= 2023,
                        r["name"] == "A1.8 Reason for Certification"
                    ]
            ):
                # for CE2023 Montpellier A1.8 first answer should go as main answer
                # and second one should go as "Secondary Reason" in compliance fields
                # that's why we ignore second answer here and transform existing answer from list to string
                if isinstance(combined_responses[r['name']]['answer'], list):
                    combined_responses[r['name']]['answer'] = combined_responses[r['name']]['answer'][0]
            elif all(
                [
                    certification.is_cyber_essentials,
                    certification.version_number >= 2023,
                    r["name"] == "A5.7 External Service Brute Force"
                ]
            ):
                # for this question second answer is custom answer which should be added to compliance fields
                # that's why we ignore it here
                ...
            else:
                combined_responses[r['name']]['answer'] += r['answer']
            # also merge applicant notes
            combined_responses[r['name']]['Applicant Notes'] += r['Applicant Notes']
        if r['name'] == 'Firewall Services':
            # since 'No' is a reversed value the actual value is 'Yes'
            if r['answer'] == 'No':  # means if 'Yes' on frontend
                firewall_services_yes = True
        if r["name"].endswith("Organisation Address"):
            r["fields"].update(get_pervade_address_compliance_fields(certification_pk))
        if r["name"].endswith("Organisation Number"):
            r.update(get_pervade_organisation_entity_type(certification_pk))
    # override this value if 'Firewall Services' == 'Yes'
    if firewall_services_yes:
        combined_responses['Services Enabled']['answer'] = 'N/A'
        combined_responses['Services Enabled']['score'] = 'pass'
    combined_responses = list(combined_responses.values())

    if certification.is_cyber_essentials_plus:
        # for CE Plus we also need to add some CE questions
        if certification.organisation.ce_certification:
            ce_answers, _ = get_pervade_api_survey(certification.organisation.ce_certification.pk)
            for answer in ce_answers:
                if answer["name"] in [
                    "A1.1 Organisation Name", "A1.2 Organisation Type", "A1.3 Organisation Number",
                    "A1.4 Organisation Address"
                ]:
                    combined_responses.append(answer)
        # for CE Plus we don't need any extra questions, so just return the responses
        return combined_responses, [r['name'] for r in combined_responses if 'name' in r]

    # add some needed fields for the final submit
    if certification.version.version_number < 2022:
        extra_answers = [
            {'name': 'Acceptance', 'score': 'pass', 'answer': 'I accept'},
            {
                "id": "2dd9c926e3c40525fdd774303626be4d9f789cc1be2daff980432c71a559eced",
                "score": "pass",
                "answer": "Yes"
            },
        ]
    else:
        extra_answers = [
            {
                "name": "Acceptance",
                "id": "918b0612d068fdac355d7354209377cf7c2b7dc2352119d291d881cc7036567e",
                "score": "pass",
                "answer": "I accept"
            }
        ]
    final_result = combined_responses + extra_answers

    # disable insurance if insurer is Superscript
    if certification.is_superscript_insurance:
        insurance = False
        # also add 3.1 manually for iasme
        # insurance opted out
        if certification.version.version_number < 2021:
            final_result.append({
                "name": "A3.1 Head Office",
                "id": "de6a8d11da96b66c8d7f4516b15266b563df1c2a848478e2c549bfbd031f6929",
                "Applicant Notes": "",
                "score": "pass",
                "answer": "No"}
            )
        elif certification.version.version_number == 2021:
            final_result.append({
                "name": "A3.1. Head Office",
                "id": "55ab1c5f791f8ec9a1490df76d5e09c4615f53a173a661806c00d91340d102bf",
                "Applicant Notes": "",
                "score": "pass",
                "answer": "No"}
            )
        elif 2022 <= certification.version.version_number < 2023:
            final_result.append({
                "name": "A3.1 Head Office",
                "id": "043e606f3ad2454ff116bb97f0ef6a3e5a1020dd9f912f513b4d2825e5a6cd9d",
                "Applicant Notes": "",
                "score": "pass",
                "answer": "No"}
            )
        elif certification.version.version_number >= 2022.5:
            final_result.append({
                "name": "A3.1 Head Office",
                "id": "e0e26fcddc194f86948eb1501d5dfa4f7f0d18747bd67b4759cbc6c079d0abdf",
                "Applicant Notes": "",
                "score": "pass",
                "answer": "No"}
            )
    else:
        insurance = certification.insurance_opt_in_survey_value

    if insurance:
        if certification.version.version_number == 2018:
            final_result.append({'name': 'Insurance Opt-Out', 'score': 'pass', 'answer': 'Opt-In'})
        elif 2018 < certification.version.version_number < 2021:
            final_result.append({'id': 'ea2cc16337efa9d9b432cd6ef29da5b6e259424f81df97bc83acec8e6dd084ee',
                                 'name': 'A3.2 Cyber Insurance', 'score': 'pass',
                                 'answer': 'Opt-In'})
        elif certification.version.version_number == 2021:
            final_result.append({
                "id": "254369ff4ebcae4935f84778e39e06911c10b436ca3c38a68a43a39feef761bc",
                "name": "A3.2. Cyber Insurance",
                "score": "pass",
                "answer": "Opt-In"}
            )
        elif 2022 <= certification.version.version_number < 2023:
            final_result.append({
                "id": "cb0ce63bb336c0578c0e4442d94feb000bf18cc5ee9005c5e2d8eea19e9e47eb",
                "name": "A3.2 Cyber Insurance",
                "score": "pass",
                "answer": "Opt-In"}
            )
        elif certification.version.version_number >= 2022.5:
            final_result.append({
                "id": "5c2c408fffc546a795c0c1328895c4946ccdab8f7066b65f2d59593e4cd703e2",
                "name": "A3.2 Cyber Insurance",
                "score": "pass",
                "answer": "Opt-In"}
            )
    else:
        if certification.version.version_number == 2018:
            final_result.append({'name': 'Insurance Opt-Out', 'score': 'pass', 'answer': 'Opt-Out'})
        elif 2018 < certification.version.version_number < 2021:
            final_result.append({'id': 'ea2cc16337efa9d9b432cd6ef29da5b6e259424f81df97bc83acec8e6dd084ee',
                                 'name': 'A3.2 Cyber Insurance', 'score': 'pass',
                                 'answer': 'Opt-Out'})
        elif certification.version.version_number == 2021:
            final_result.append({
                "id": "254369ff4ebcae4935f84778e39e06911c10b436ca3c38a68a43a39feef761bc",
                "name": "A3.2. Cyber Insurance",
                "score": "pass",
                "answer": "Opt-Out"}
            )
        elif 2022 <= certification.version.version_number < 2023:
            final_result.append({
                "id": "cb0ce63bb336c0578c0e4442d94feb000bf18cc5ee9005c5e2d8eea19e9e47eb",
                "name": "A3.2 Cyber Insurance",
                "score": "pass",
                "answer": "Opt-Out"}
            )
        elif certification.version.version_number >= 2022.5:
            insurance_question = get_insurance_question(certification)
            final_result.append({
                "id": insurance_question.pervade_id,
                "name": insurance_question.pervade_title,
                "score": "pass",
                "answer": "Opt-Out"}
            )
    # add signed declaration if exist
    try:
        SurveyDeclaration.objects.filter(survey__certificate_id=certification_pk, pdf__isnull=False).exclude(pdf='')[0]
    except IndexError:
        pass
    else:
        if insurance:
            if certification.version.version_number > 2017:
                if certification.version.version_number == 2018:
                    # Cyber Essentials 2018
                    final_result += [
                        {
                            'id': '455f9ed6cc08ff7e251539ef117cfe92403741241dc76a7d87a668345aa6348a',
                            'name': 'Cyber Insurance Declarations Signed',
                            'answer': 'Yes',
                            'Applicant Notes': 'Insurance PDF attached',
                            'evidencetype': 'upload',
                            'evidencename': 'Cyber Insurance Declaration',
                            'evidencefileid': 'declaration'
                        }
                    ]
                if certification.version.version_number == 2019:
                    # Cyber Essentials 2019
                    final_result += [
                        {
                            'id': '7b1e7bf3a022f542094fe04749ea7e86683eeeb7e91419e3711ad1ace9d2b047',
                            'name': 'Cyber Insurance Declaration Signed',
                            'answer': 'Yes',
                            'Applicant Notes': 'Insurance PDF attached',
                            'evidencetype': 'upload',
                            'evidencename': 'Cyber Insurance Declaration',
                            'evidencefileid': 'declaration'
                        },
                        {
                            'id': '4ec5574e1ff83647e0660354b2101c691a32df62f4bee6fb8b4e9a62ce3c780b',
                            'name': 'Cyber Insurance Declaration Signed',
                            'answer': 'Yes',
                            'Applicant Notes': 'Insurance PDF attached',
                            'evidencetype': 'upload',
                            'evidencename': 'Cyber Insurance Declaration',
                            'evidencefileid': 'declaration'
                        }
                    ]
                if certification.version.version_number < 2022:
                    # Cyber Essentials 2020 - 2021
                    final_result += [
                        {
                            'id': '8b6b19860a427083c48c7406141fd0d6c3047355e916033aaeb5bde53e7469b2',
                            'name': 'Cyber Declaration Signed',
                            'answer': 'Digitally signed',
                            'Applicant Notes': '',
                            'evidencetype': 'upload',
                            'evidencename': 'Cyber Insurance Declaration',
                            'evidencefileid': 'declaration',
                            'Name': certification.survey.declaration.declaration_name,
                            'Role': certification.survey.declaration.declaration_job
                        }
                    ]
        else:
            if certification.version.version_number >= 2018:
                if certification.version.version_number == 2018:
                    # Cyber Essentials 2018
                    final_result += [
                        {
                            'id': '2704ec74317b8860373827efb3d0dbb069ca340b81636569f2dbe179166287b2',
                            'name': 'Cyber Declarations Signed',
                            'answer': 'Yes',
                            'Applicant Notes': 'PDF attached',
                            'evidencetype': 'upload',
                            'evidencename': 'Cyber Declaration',
                            'evidencefileid': 'declaration'
                        }
                    ]
                if certification.version.version_number < 2022:
                    # Cyber Essentials 2019 - 2021
                    final_result += [
                        {
                            'id': '8b6b19860a427083c48c7406141fd0d6c3047355e916033aaeb5bde53e7469b2',
                            'name': 'Cyber Declaration Signed',
                            'answer': 'Digitally signed',
                            'Applicant Notes': '',
                            'evidencetype': 'upload',
                            'evidencename': 'Cyber Declaration',
                            'evidencefileid': 'declaration',
                            'Name': certification.survey.declaration.declaration_name,
                            'Role': certification.survey.declaration.declaration_job
                        }
                    ]

    # Add the responsible person information for Q15 Cyber Essentials > 2018
    if certification.type in (CYBER_ESSENTIALS, GDPR) and certification.version.version_number > 2018:
        q15_responses_name = SurveyResponse.objects.filter(
            survey__certificate_id=certification_pk,
            question__widget=SurveyQuestion.WIDGET_ORGANISATION_IT,
            code='name').first()
        q15_responses_role = SurveyResponse.objects.filter(
            survey__certificate_id=certification_pk,
            question__widget=SurveyQuestion.WIDGET_ORGANISATION_IT,
            code='role').first()
        if q15_responses_name and q15_responses_role:
            compliance_field_id = None
            for key, value in q15_responses_name.question.pervade_compliance_fields.items():
                if "name" in value and value["name"] == "Responsible Person Role":
                    compliance_field_id = key
            final_result.append(
                {
                    'id': q15_responses_name.question.pervade_id,
                    'name': q15_responses_name.question.pervade_title,
                    'Applicant Notes': q15_responses_name.pervade_applicant_notes_value,
                    'score': q15_responses_name.pervade_assessor_score,
                    'answer': q15_responses_name.value_text,
                    "fields": {compliance_field_id: q15_responses_role.value_text}
                })

    # add some specific answers for Cyber Essentials 2022 (Evendine) and higher
    if certification.type == CYBER_ESSENTIALS and certification.version.version_number >= 2022:
        final_result.append(
            {
                "id": "7594b389f6e0d71c769d552b4fb391dc5b1130c7f43c7e0806c78d30372e0ad6",
                "name": "All Answers Approved",
                "answer": "Yes",
                "score": "pass"
            }
        )
    if certification.type == GDPR and certification.version.version_number >= 2022:
        declaration = certification.survey.declaration
        final_result.append(
            {
                "id": "cb5728ad6a384dd129d5de8ee5a2cf6153e7382859b0b0bc5a31ad374e69c057",
                "name": "Cyber Declaration E-Signature",
                "answer": "<EMAIL>",
                "Name": declaration.declaration_name,
                "Role": declaration.declaration_job,
                "fields": {
                    "62d8c8da36f9c93ab2ce8752b671732d619a45451bbcf2a7c0542aaf364c1032": declaration.declaration_name,
                    "bfa04f07a22772edbde3c46de48c369d9b3fce7b122f5964117d3004f3fe2f47": declaration.declaration_job
                }
            }
        )

    if certification.type == GDPR:
        # extend GDPR answers with CE answers
        ce_answers, titles = get_pervade_api_survey(certification.organisation.ce_certification.pk)
        final_result += ce_answers

    return final_result, [r['name'] for r in final_result if 'name' in r]


def get_pervade_address_response(certification_pk):
    """
    Returns the address response for a certification.
    :param certification_pk: organisation certification ID
    :type certification_pk: int
    :return: certification's response for the address question
    :rtype: dict
    """
    response = {}
    address_queryset = SurveyResponse.objects.filter(
        survey__certificate_id=certification_pk,
        question__pervade_title__isnull=False
    )

    address1 = address_queryset.filter(code='address1').first()
    response['Address Line 1'] = address1.value if address1 else ''

    address2 = address_queryset.filter(code='address2').first()
    response['Address Line 2'] = address2.value if address2 else ''

    city = address_queryset.filter(code='city').first()
    response['Town/City'] = city.value if city else ''

    county = address_queryset.filter(code='county').first()
    response['County'] = county.value if county else ''

    postcode = address_queryset.filter(code='postcode').first()
    response['Postcode'] = postcode.value if postcode else ''

    return response


def get_pervade_address_compliance_fields(certification_pk: int) -> dict:
    """
    Returns the address compliance fields for a certification.
    """
    answers = SurveyResponse.objects.filter(
        survey__certificate_id=certification_pk,
        question__code=QUESTION_CODE_ORGANISATION_ADDRESS
    )

    # get each answer separately
    address1 = answers.filter(code="address1").first()
    address2 = answers.filter(code="address2").first()
    city = answers.filter(code="city").first()
    county = answers.filter(code="county").first()
    postcode = answers.filter(code="postcode").first()
    country = answers.filter(code="country").first()

    # get address question
    question = address1.question if address1 else address2.question if address2 else city.question if city else None

    address = {}

    field_name_and_value = {
        "address line 1": address1.value if address1 else "",
        "address line 2": address2.value if address2 else "",
        "town/city": city.value if city else "",
        "county": county.value if county else "",
        "postcode": postcode.value if postcode else "",
        "country": country.value if country else ""
    }

    # fill the final address dict with the data where a key is a compliance field ID and value is the user answer
    if question:
        for key, value in question.pervade_compliance_fields.items():
            if "name" in value and value["name"].lower() in field_name_and_value:
                address[key] = field_name_and_value.get(value["name"].lower())

    return address


def get_pervade_organisation_entity_type(certification_pk):
    """
    Returns the address response for a certification.
    :param certification_pk: organisation certification ID
    :type certification_pk: int
    :return: certification's response for the address question
    :rtype: dict
    """
    response = {}
    organisation_entity_type = SurveyResponse.objects.filter(
        survey__certificate_id=certification_pk,
        question__pervade_title__endswith='Organisation Number - Organisation Type'
    ).first()

    response['Organisation Type'] = organisation_entity_type.value if organisation_entity_type else ''

    return response


class PDFGenerationError(Exception):
    """Custom exception for PDF generation errors"""
    pass


class GenerateDeclaration:
    """
    Generates a declaration PDF for a given survey declaration and insurance status.
    """

    def __init__(self, declaration, insurance):
        self.declaration = declaration
        self.insurance = insurance
        self.external_uuid = declaration.survey.certificate.organisation.external_uuid
        # pdf templates
        if self.insurance:
            self.pdf_template = finders.find("pdf/insurance_declaration2018c.pdf")
        else:
            self.pdf_template = finders.find("pdf/no_insurance_declaration2018b.pdf")

    def add_signature_to_pdf(self, pre_final_pdf_path, final_pdf_path):
        img = BytesIO()
        img_canvas = canvas.Canvas(img)
        # Draw signature on Canvas and save PDF in buffer
        try:
            img_path = ImageReader(self.declaration.signature.file)
            height = 190 if self.insurance else 257
            img_canvas.drawImage(img_path, 120, height, 200, 60)  # at (120, 190) with size 200x60
            img_canvas.save()
        except EOFError:
            # sometimes for some files it raises this error when trying to read the file first time
            # so if it happens we try to read it one more time (because then it works :D)
            img_path = ImageReader(self.declaration.signature.file)
            height = 190 if self.insurance else 257
            img_canvas.drawImage(img_path, 120, height, 200, 60)  # at (120, 190) with size 200x60
            img_canvas.save()
        finally:
            # Use PyPDF to merge the image-PDF into the template
            with open(pre_final_pdf_path, 'rb') as pdf_file:
                pdf_bytes = pdf_file.read()
            page = PdfReader(BytesIO(pdf_bytes)).pages[0]
            img.seek(0)
            overlay = PdfReader(img).pages[0]
            page.merge_page(overlay)
            output = PdfWriter()
            output.add_page(page)
            with open(final_pdf_path, 'wb') as pdf_file:
                output.write(pdf_file)

    def get_fields(self):
        """
        Returns the fields that will be filled in the PDF.
        :return: list of tuples with field names and their values
        :rtype: list of tuples
        """
        return [
            ('company_name', self.declaration.survey.certificate.organisation.name),
            ('signer_name', self.declaration.declaration_name),
            ('signer_title', self.declaration.declaration_job),
            ('date', self.declaration.declaration_date)
        ]

    def run(self):
        data_dir = os.path.join(settings.STATIC_ROOT, 'pdf', 'data')
        try:
            # check if pdftk server is installed
            if not shutil.which('pdftk'):
                raise PDFGenerationError('Pdftk server is not installed. Can\'t generate pdf.')

            # users data directory
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            fields = self.get_fields()
            fdf = forge_fdf('', fields, [], [], [])
            fdf_path = os.path.join(
                data_dir,
                f'form-declaration-{self.external_uuid}.fdf'
            )
            pre_final_pdf_path = os.path.join(
                data_dir,
                f'declaration-without-signature-{self.external_uuid}.pdf'
            )
            final_pdf_path = f'declaration-signed-{self.external_uuid}.pdf'
            with open(fdf_path, 'wb') as fdf_file:
                fdf_file.write(fdf)

            # merge fdf form with pdf file
            command = f'pdftk {self.pdf_template} fill_form {fdf_path} output {pre_final_pdf_path} flatten'
            status = subprocess.call(command, shell=True)

            if status != 0:
                raise PDFGenerationError(f'pdftk server exit with status code: {status}')

            # insert signature
            if self.declaration.survey.certificate.version.declaration:
                if not self.declaration.signature:
                    raise PDFGenerationError(f'Declaration {self.declaration.id} has no signature')
                self.add_signature_to_pdf(pre_final_pdf_path, final_pdf_path)

            # save pdf to declaration
            with open(final_pdf_path, 'rb') as pdf_file:
                self.declaration.pdf = File(pdf_file)
                self.declaration.save()

        except Exception as error:
            sentry_sdk.set_extra("Stack Trace", traceback.format_exc())
            sentry_sdk.capture_message(
                message=f'Cannot generate declaration pdf for id: {self.declaration.pk}. ({error})',
                level="error",
            )
        # remove data folder
        rmtree(data_dir, ignore_errors=True)


def forbid_declaration_submit_for_trial_accounts(partner):
    """
    Returns True if passed partner counted as trial thus it doesn't have permissions to submit declaration
    :return: True or False
    :rtype: bool
    """
    return partner.trial_account


def repair_pdf(temp_buffer):
    """
    Fixing any malformed pdf using pdftocairo package.

    temp_buffer: StringIO buffer
    return: StringIO buffer
    """
    # Make sure we're at the start of the file
    temp_buffer.seek(0)

    process = subprocess.Popen(
        ['pdftocairo', '-pdf', '-', '-'],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    stdout, stderr = process.communicate(temp_buffer.read())

    return BytesIO(stdout)


def generate_csv_organisation_survey(topics, survey_id, escaped=True):
    text = u'Question number, Question, Answer, State\n'
    for topic in topics:
        for q in topic.questions.all():
            if q.show_question(survey_id):
                responses = ""
                if q.responses.exists():
                    responses = ', '.join([response.humanize_response_value or "" for response in q.responses.all()])
                text += u'"{}","{}","{}","{}"\n'.format(
                    q.pervade_num, q.title, responses, q.responses.first().get_colour() if q.responses.exists() else 'Grey')
    if escaped:
        text = text.replace('\\', '\\\\')
    return strip_tags(text)


def get_pervade_contributor_address(organisation):
    """
    Returns the contributor's based on the address response for the latest CE certification.
    :param organisation: organisation
    :type organisation: Organisation
    :return: CE certification's response for the address question
    :rtype: dict
    """
    ce_certification = organisation.get_latest_certificate(CYBER_ESSENTIALS)
    address_response = get_pervade_address_response(ce_certification.id)
    # Map the address's fiels to the contributor's fields
    contributor_address_lookup = {'Address Line 1': 'contributor_firstlineofaddress',
                                  'Address Line 2': 'contributor_secondlineofaddress',
                                  'Town/City': 'contributor_city',
                                  'County': 'contributor_county',
                                  'Postcode': 'contributor_postcode'}
    return remap_dictionary(address_response, contributor_address_lookup)


def remap_dictionary(dictionary, lookup):
    """
    Returns a remapped dictionary based on the lookup.
    :param dictionary: a dictionary to be remapped
    :type dictionary: dict
    :param lookup: a dictionary that map the dictionary keys to the new one
    :type lookup: dict
    :return: same dictionary's values with different keys based on the lookup
    :rtype: dict
    """
    remapped_dictionary = {}
    for field in dictionary:
        remapped_dictionary[lookup[field]] = dictionary[field]
    return remapped_dictionary


class CertificationTools:
    """
    Creates new certification with give topics and questions.
    """
    logger_prefix = "[Certification Tools]:"

    def __init__(self):
        self.topic_map = {}
        self.question_map = {}
        self.choice_map = {}

    def log_error(self, msg):
        """
        Logs errors
        """
        logger.error(f"{self.logger_prefix} {msg}")

    @staticmethod
    def get_by_id(items_list):
        """
        Gets a list of dicts and returns a dict where key is item["id"]
        """
        return {item["id"]: item for item in items_list}

    def add_new_certification(self, data):
        """
        Adds new certification version and questions to the platform.
        """
        version_data = data.copy()
        del version_data["topics"]
        del version_data["questions"]
        new_cert_version = CertificationVersion.objects.create(
            **version_data
        )
        self.add_questions(new_cert_version.pk, data)

    def create_topics(self, new_certification, data):
        """
        Creates topics for new version.
        """
        for topic in data["topics"]:
            data = topic.copy()
            old_pk = data.pop("id")
            data["version"] = new_certification
            new_topic = SurveyQuestionTopic.objects.create(
                **data
            )
            self.topic_map[old_pk] = new_topic.pk

    def create_questions(self, new_certification, data):
        """
        Creates questions for new version
        """

        for question in data["questions"]:
            data = question.copy()
            if question.get("topic"):
                old_pk = data.pop("id")
                choices = data.pop("choices")
                failed_choices = data.pop("failed_choices")

                data["version"] = new_certification
                del data["topic"]
                del data["parent"]
                del data["parent_2"]
                data["topic_id"] = self.topic_map[question["topic"]]
                data.pop("auto_answer", None)
                data.pop("auto_answer_choice", None)
                data.pop("mandatory_for_parent_choices", None)
                data.pop("auto_answer_survey_question", None)
                data.pop("auto_answer_survey_question_choice", None)
                data.pop("auto_answer_current_question_choice", None)
                data.pop("related_check", None)

                new_question = SurveyQuestion.objects.create(**data)
                self.question_map[old_pk] = new_question.pk

                self.create_choices(choices)
                self.create_failed_choices(failed_choices)

    def create_choices(self, choices):
        """
        Creates question choices for new version
        """
        for choice in choices:
            data = choice.copy()
            old_pk = data.pop("id")
            data["question_id"] = self.question_map[data.pop("question")]
            new_choice = SurveyQuestionChoices.objects.create(
                **data
            )
            self.choice_map[old_pk] = new_choice.pk

    def create_failed_choices(self, failed_choices):
        """
        Creates question failed_choices for new version
        """
        for failed_choice in failed_choices:
            data = failed_choice.copy()
            data.pop("id")
            data["question_id"] = self.question_map[data.pop("question")]
            SurveyQuestionFailedChoices.objects.create(
                **data
            )

    def update_questions(self, new_certification, data):
        """
        Updates newly created questions with some additional information.
        """
        for question in SurveyQuestion.objects.filter(version=new_certification):
            # swap question_map, before - old_pk: new_pk, after - new_pk: old_pk
            swapped_question_map = {v: k for k, v in self.question_map.items()}
            # here we retrieve json data for given question
            question_data = self.get_by_id(data["questions"])[swapped_question_map[question.pk]]
            # update mandatory parent choices
            if question_data["mandatory_for_parent_choices"]:
                if isinstance(question_data.get("mandatory_for_parent_choices"), int):
                    parent_choices = [question_data.get("mandatory_for_parent_choices")]
                elif isinstance(question_data.get("mandatory_for_parent_choices"), list):
                    parent_choices = question_data.get("mandatory_for_parent_choices")
                for pk in parent_choices:
                    question.mandatory_for_parent_choices.add(
                        self.choice_map.get(pk)
                    )

            # update parent question
            if question_data["parent"]:
                question.parent_id = self.question_map.get(question_data["parent"])

            # update parent_2 question
            if question_data["parent_2"]:
                question.parent_2_id = self.question_map.get(question_data["parent_2"])

            # update auto answer choice
            if question_data["auto_answer_choice"]:
                question.auto_answer_choice_id = self.choice_map.get(question_data["auto_answer_choice"])

            # update auto answer question
            if question_data["auto_answer_survey_question"]:
                question.auto_answer_survey_question_id = self.question_map.get(
                    question_data["auto_answer_survey_question"]
                )

            # update auto answer choice
            if question_data["auto_answer_survey_question_choice"]:
                question.auto_answer_survey_question_choice_id = self.choice_map.get(
                    question_data["auto_answer_survey_question_choice"]
                )

            # update auto answer current question choice
            if question_data["auto_answer_current_question_choice"]:
                question.auto_answer_current_question_choice_id = self.choice_map.get(
                    question_data["auto_answer_current_question_choice"]
                )

            question.save()

    def add_questions(self, cert_version_pk, data):
        """
        Adds questions to the new certification.
        :param cert_version_pk: new questions will be added to this certification version
        :type cert_version_pk: int
        :param data: full cert data
        :type data: dict
        """
        try:
            new_certification = CertificationVersion.objects.get(pk=cert_version_pk)
        except CertificationVersion.DoesNotExist as error:
            self.log_error(error)
        else:
            self.create_topics(new_certification, data)
            self.create_questions(new_certification, data)
            self.update_questions(new_certification, data)


def divide_list_to_batches(lst, batch_size):
    """Yield successive batch_size-sized chunks from lst."""
    for i in range(0, len(lst), batch_size):
        yield lst[i:i + batch_size]


def merge_responses(responses_list):
    data = {
        'status': 200,
        "RESULT": True,
        "done": [],
        "error": []
    }
    for response in responses_list:
        json_response = response.json()
        status = response.status_code
        if status > data["status"]:
            data["status"] = status
        data["RESULT"] = bool(data["RESULT"] and json_response.get("RESULT", False))
        data["done"] += json_response.get("done", [])
        data["error"] += json_response.get("errors", [])
    return data


def fill_survey_responses_for_debug_mode(certification):
    for question in SurveyQuestion.objects.filter_insurer_options(certification).filter(
        version=certification.version,
    ):
        SurveyResponse.import_debug_answer_value(certification.survey, question)


def import_new_questions_from_pervade(certification_version: CertificationVersion, questions: dict) -> dict:
    """ Simple logic starting point to use the fetched data from pervade and try to add/update the questions. """
    existing_questions = SurveyQuestion.objects.filter(
        version=certification_version
    )
    changes_done = {'created': [], 'updated': [], 'deleted': []}
    order = 0
    with transaction.atomic():
        for pervade_id, data in questions.items():
            # we do not update the pervade title since there is existing logic based on this and can break it
            question_title = data.get('name')
            question_number = question_title.split(' ')[0]
            filters = Q(Q(pervade_id=pervade_id) | Q(pervade_title__startswith=question_number))
            question = existing_questions.filter(filters).first()
            if not question:
                widget = SurveyQuestion.WIDGET_DEFAULT
                response_type = SurveyQuestion.TEXT
                if data.get('answertype') == 'select' and data.get('options', []) == ['Yes', 'No']:
                    widget = SurveyQuestion.WIDGET_CONSCIOUS
                    response_type = SurveyQuestion.BOOLEAN
                question = SurveyQuestion.objects.create(
                    version=certification_version,
                    pervade_id=pervade_id,
                    pervade_title=question_title,
                    widget=widget,
                    response_type=response_type
                )
                changes_done['created'].append(question.id)
            else:
                changes_done['updated'].append(question.id)
            # we get the previous question and assume it will be the same topic if the title starts with the same number
            previous_question = existing_questions.filter(order=order).first()
            if previous_question and previous_question.pervade_title[0] == question_number[0]:
                question.topic = previous_question.topic
            description_split = data.get('description').split('<br><br>')
            question.title = description_split[0]
            question.moreinfo = description_split[1] if len(description_split) > 1 else None
            question.pervade_compliance_fields = {} if not data.get('compliancefields') else dict(data.get('compliancefields'))
            order += 1
            question.order = order
            question.save()
        # now we delete the questions that were not updated nor created
        to_delete = existing_questions.exclude(pk__in=changes_done['created'] + changes_done['updated'])
        changes_done['deleted'] = list(to_delete.values_list('title', flat=True))
        to_delete.delete()
    return changes_done
