from __future__ import unicode_literals

import datetime
import os
from io import Bytes<PERSON>

from PIL import Image
from django.core.files import temp as tempfile, File
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.forms import ValidationError
from django.shortcuts import reverse
from django.template import Context, Template
from django.test import override_settings, TestCase
from django.utils import timezone, translation
from freezegun import freeze_time

from common.base_tests import BaseTestCase
from organisations.factories import OrganisationCertificationFactory
from rulebook.factories import CertificationVersionFactory
from rulebook.models import (
    CertificationVersion, AppCheck, OperatingSystem, OSPlatformRelease, Command, BulkSolution, SurveyQuestion,
    CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, IASME_GOVERNANCE, GDPR, CertificateType, CERTIFICATES,
    CertificationSurvey, SurveyDeclaration, SurveyResponse, IssuedCertification,
    SurveyQuestionTopic, SurveyQuestionChoices, SurveyQuestionFailedChoices, SurveyFailedResponse,
)


def get_temporary_text_file():
    io = BytesIO()
    size = io.write('foo'.encode('utf-8'))
    text_file = InMemoryUploadedFile(io, None, 'foo.txt', 'text', io.len if hasattr(io, 'len') else size, None)
    text_file.seek(0)
    return text_file


def get_temporary_image():
    io = BytesIO()
    size = (200, 200)
    color = (255, 0, 0, 0)
    image = Image.new("RGB", size, color)
    image.save(io, format='JPEG')
    image_file = InMemoryUploadedFile(io, None, 'foo.jpg', 'jpeg', 512, None)
    image_file.seek(0)
    return image_file


class CertificationTypeTestCase(BaseTestCase, TestCase):

    created_certificates = {}

    def test_create(self):
        for certification_type in [CYBER_ESSENTIALS, CYBER_ESSENTIALS_PLUS, IASME_GOVERNANCE, GDPR]:
            self.created_certificates[certification_type] = CertificateType.objects.create(
                type=certification_type
            )

    def test_method___str__(self):
        for cert in self.created_certificates.values():
            self.assertEqual(cert.__str__(), CERTIFICATES[cert.type])

    def test_method___unicode__(self):
        for cert in self.created_certificates.values():
            self.assertEqual(cert.__unicode__(), CERTIFICATES[cert.type])


class CertificationSurveyTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.date_started = timezone.now()
        self.survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.get(version__type__type=CYBER_ESSENTIALS),
            datetime_started=self.date_started,
            passed_percent=70
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        self.assertIsNotNone(self.survey.pk)

    def test_method___str__(self):
        self.assertEqual("Survey: {0}".format(
            self.survey.certificate  # pylint: disable=no-member
        ), self.survey.__str__())

    def test_method___unicode__(self):
        self.assertEqual("Survey: {0}".format(
            self.survey.certificate  # pylint: disable=no-member
        ), self.survey.__unicode__())

    def test_property_datetime_completed(self):
        self.assertEqual(self.survey.datetime_completed, self.survey.modified)
        SurveyDeclaration.objects.create(
            survey=self.survey,
            declaration_date=self.date_started
        )
        self.assertEqual(self.survey.datetime_completed, self.date_started)

    def test_method_calculate_passed_percent(self):
        self.assertEqual(self.survey.passed_percent, 70)
        question = SurveyQuestion.objects.filter(version=self.survey.certificate.version)[0]
        SurveyResponse.objects.create(
            survey=self.survey,
            question=question
        )
        percent = self.survey.calculate_passed_percent()
        self.assertNotEqual(percent, 70)
        self.assertNotEqual(self.survey.passed_percent, 70)


class SurveyDeclarationTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.date_started = timezone.now()
        self.survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.get(version__type__type=CYBER_ESSENTIALS),
            datetime_started=self.date_started,
            passed_percent=70
        )
        self.declaration = SurveyDeclaration.objects.create(
            survey=self.survey,
            declaration_date=self.date_started,
            declaration_name='Declaration name',
            declaration_job='Declaration job',
            declaration_email='<EMAIL>',
            html_report='',
            ready_to_be_signed=True
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        self.assertIsNotNone(self.declaration.pk)

    def test_method___str__(self):
        self.assertEqual("{} survey [{}] declaration".format(
            self.declaration.survey.certificate.organisation.name, self.survey.id  # pylint: disable=no-member
        ), self.declaration.__str__())

    def test_method___unicode__(self):
        self.assertEqual("{} survey [{}] declaration".format(
            self.declaration.survey.certificate.organisation.name, self.survey.id  # pylint: disable=no-member
        ), self.declaration.__unicode__())

    def test_property_html_report_url(self):
        url = self.declaration.html_report_url
        kwargs = {
            'org_id': self.survey.certificate.organisation.secure_id,  # pylint: disable=no-member
            'type': self.survey.certificate.version.type.type  # pylint: disable=no-member
        }
        self.assertEqual(url, reverse('dashboard:show-certification-html-report', kwargs=kwargs))


class IssuedCertificationTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.date = timezone.now()
        self.survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.get(version__type__type=CYBER_ESSENTIALS),
            datetime_started=self.date,
            passed_percent=70
        )
        self.declaration = SurveyDeclaration.objects.create(
            survey=self.survey,
            declaration_date=self.date,
            declaration_name='Declaration name',
            declaration_job='Declaration job',
            declaration_email='<EMAIL>'
        )
        self.issued_certification = IssuedCertification.objects.create(
            certificate=self.organisation.certifications.get(version__type__type=CYBER_ESSENTIALS),
            date=self.date,
            number=1.1
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()
        IssuedCertification.objects.all().delete()

    def create_issued_certification(self, id=100, date="2020-03-15 08:21"):
        IssuedCertification.objects.create(
            id=id,
            certificate=OrganisationCertificationFactory(),
            date=date,
            number=1.1
        )

    def test_create(self):
        self.assertIsNotNone(self.declaration.pk)

    def test_method___str__(self):
        self.assertEqual(
            "{0} Issued Certification".format(self.organisation.name),
            self.issued_certification.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            "{0} Issued Certification".format(self.organisation.name),
            self.issued_certification.__unicode__()
        )

    def test_get_issued_certificates_for_renewal(self):
        # create with exactly 11 month before current date, this should be returned
        # since we start migration 1 month before the renewal date
        self.create_issued_certification(id=100, date="2020-04-15 08:21")
        # create with exactly one year before current date
        self.create_issued_certification(id=200, date="2020-03-15 08:21")
        # create with one year before current date from beginning of the month
        self.create_issued_certification(id=300, date="2020-03-01 08:21")
        # create with more than one year before current date
        self.create_issued_certification(id=400, date="2020-01-15 08:21")
        # create with more than 1 year before current date
        self.create_issued_certification(id=500, date="2020-05-15 08:21")
        # create when renewal time has already passed, 1 year + 1 month
        self.create_issued_certification(id=600, date="2020-02-15 08:21")
        # create with exactly 11 month and a day before current date,
        self.create_issued_certification(id=700, date="2020-04-14 08:21")

        # current date
        with freeze_time("2021-03-15 08:21"):
            cert_ids = IssuedCertification.get_issued_certificates_for_renewal().values_list('id', flat=True)
            # only some certs are inside the interval of time
            self.assertListEqual(list(cert_ids.order_by('id')), [100, 700])


class CertificationVersionTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.temp_file = File(tempfile)
        self.data = {
            'version_number': 11.10,
            'type': self.cert_type,
            'release_date': timezone.now(),
            'badges_file_upload': self.temp_file.name
        }
        self.new_cert_version = CertificationVersion.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            "v{0} - {1}".format(self.new_cert_version.version_number, self.new_cert_version.type),
            self.new_cert_version.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            "v{0} - {1}".format(self.new_cert_version.version_number, self.new_cert_version.type),
            self.new_cert_version.__unicode__()
        )

    def test_create(self):
        self.assertEqual(self.new_cert_version.version_number, self.data['version_number'])
        self.assertEqual(self.new_cert_version.type, self.data['type'])
        self.assertEqual(self.new_cert_version.release_date, self.data['release_date'])
        self.assertEqual(self.new_cert_version.badges_file_upload.name, self.data['badges_file_upload'])


class CertificationVersionSaveConstraintTestCase(TestCase):

    def test_clean_raises_when_no_default_version_active(self):
        cert_type = CertificateType.objects.create(type=CYBER_ESSENTIALS)
        cert_version = CertificationVersionFactory(
            type=cert_type,
            default=True
        )
        cert_version.release_date = (timezone.now() + datetime.timedelta(days=10)).date()
        self.assertEqual(cert_type.type, CYBER_ESSENTIALS)
        with self.assertRaises(ValidationError):
            cert_version.clean()

        cert_version.release_date = (timezone.now() + datetime.timedelta(days=-10)).date()
        cert_version.default=False
        with self.assertRaises(ValidationError):
            cert_version.clean()

    def test_clean_not_raises_when_other_defaults(self):
        cert_type = CertificateType.objects.create(type=CYBER_ESSENTIALS)
        cert_version = CertificationVersionFactory(type=cert_type)
        CertificationVersionFactory(
            type=cert_type, default=True,
            release_date=(timezone.now() + datetime.timedelta(days=-10)).date()
        )
        cert_version.release_date = (timezone.now() + datetime.timedelta(days=10)).date()
        self.assertEqual(cert_type.type, CYBER_ESSENTIALS)

        cert_version.clean()


class QuestionTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.data = {
            'order': 1,
            'code': 'my code',
            'qtype': AppCheck.API,
            'title': 'My title',
            'tooltip': ':D'
        }
        self.new_question = AppCheck.objects.create(
            **self.data
        )
        self.temp_file = File(tempfile)
        self.cv_data = {
            'version_number': 11.10,
            'type': self.cert_type,
            'release_date': timezone.now(),
            'badges_file_upload': self.temp_file.name
        }
        self.cv = CertificationVersion.objects.create(
            **self.cv_data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            self.new_question.title,
            self.new_question.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            self.new_question.title,
            self.new_question.__unicode__()
        )

    def test_create(self):
        self.new_question.version.add(self.cv)
        self.assertEqual(2, AppCheck.objects.all().count())
        self.assertEqual(self.new_question.order, self.data['order'])
        self.assertEqual(self.new_question.code, self.data['code'])
        self.assertEqual(self.new_question.qtype, self.data['qtype'])
        self.assertEqual(self.new_question.title, self.data['title'])
        self.assertEqual(self.new_question.version.count(), 1)
        self.assertEqual(self.new_question.tooltip, self.data['tooltip'])


class OperationSystemTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.data = {
            'os_id': 'it is id',
            'title': 'Windows',
            'is_supported': True,
        }
        self.new_os = OperatingSystem.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            self.new_os.title,
            self.new_os.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            self.new_os.title,
            self.new_os.__unicode__()
        )

    def test_create(self):
        self.assertEqual(2, OperatingSystem.objects.all().count())
        self.assertEqual(self.new_os.os_id, self.data['os_id'])
        self.assertEqual(self.new_os.title, self.data['title'])
        self.assertEqual(self.new_os.is_supported, self.data['is_supported'])


class OSPlatformReleaseTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.os_data = {
            'os_id': 'it is id',
            'title': 'Windows',
            'is_supported': True,
        }
        self.new_os = OperatingSystem.objects.create(
            **self.os_data
        )
        self.data = {
            'os': self.new_os,
            'platform': 'Windows',
            'release': '1.1.1',
            'is_required': True,
        }
        self.new_release = OSPlatformRelease.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            "{0} {1}".format(self.new_release.platform, self.new_release.release),
            self.new_release.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            "{0} {1}".format(self.new_release.platform, self.new_release.release),
            self.new_release.__unicode__()
        )

    def test_create(self):
        self.assertEqual(1, OSPlatformRelease.objects.all().count())
        self.assertEqual(self.new_release.os, self.data['os'])
        self.assertEqual(self.new_release.platform, self.data['platform'])
        self.assertEqual(self.new_release.release, self.data['release'])
        self.assertEqual(self.new_release.is_required, self.data['is_required'])


class CommandTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.temp_file = File(tempfile)
        self.cv_data = {
            'version_number': 11.10,
            'type': self.cert_type,
            'release_date': timezone.now(),
            'badges_file_upload': self.temp_file.name
        }
        self.cv = CertificationVersion.objects.create(
            **self.cv_data
        )
        self.check_data = {
            'order': 1,
            'code': 'my code',
            'qtype': AppCheck.API,
            'title': 'My title',
            'tooltip': ':D'
        }
        self.new_check = AppCheck.objects.create(
            **self.check_data
        )
        self.new_check.version.add(self.cv)
        self.os_data = {
            'os_id': 'it is id',
            'title': 'Windows',
            'is_supported': True,
        }
        self.new_os = OperatingSystem.objects.create(
            **self.os_data
        )
        self.data = {
            'app_check': self.new_check,
            'os': self.new_os,
            'check_command': 'command',
            'expected_response': 'response',
            'fix_command': 'fix command',
            'fix_guide': 'fix guide'
        }
        self.new_command = Command.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            "<command for {0}>".format(self.new_command.app_check.title),
            self.new_command.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            "<command for {0}>".format(self.new_command.app_check.title),
            self.new_command.__unicode__()
        )

    def test_create(self):
        self.assertEqual(1, Command.objects.all().count())
        self.assertEqual(self.new_command.app_check, self.data['app_check'])
        self.assertEqual(self.new_command.os, self.data['os'])
        self.assertEqual(self.new_command.check_command, self.data['check_command'])
        self.assertEqual(self.new_command.expected_response, self.data['expected_response'])
        self.assertEqual(self.new_command.fix_command, self.data['fix_command'])
        self.assertEqual(self.new_command.fix_guide, self.data['fix_guide'])


class BulkSolutionTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.temp_file = File(tempfile)
        cv_data = {
            'version_number': 11.10,
            'type': self.cert_type,
            'release_date': timezone.now(),
            'badges_file_upload': self.temp_file.name
        }
        self.cv = CertificationVersion.objects.create(
            **cv_data
        )
        check_data = {
            'order': 1,
            'code': 'my code',
            'qtype': AppCheck.API,
            'title': 'My title',
            'tooltip': ':D'
        }
        self.new_check = AppCheck.objects.create(
            **check_data
        )
        self.new_check.version.add(self.cv)
        os_data = {
            'os_id': 'it is id',
            'title': 'Windows',
            'is_supported': True,
        }
        self.new_os = OperatingSystem.objects.create(
            **os_data
        )
        self.data = {
            'app_check': self.new_check,
            'fix_choice': '3',
            'fix_guide': 'guide',
        }
        self.new_solution = BulkSolution.objects.create(
            **self.data
        )
        self.new_solution.os.add(self.new_os)

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        self.assertEqual(1, BulkSolution.objects.all().count())
        self.assertEqual(self.new_solution.app_check, self.data['app_check'])
        self.assertEqual(self.new_solution.os.all().count(), 1)
        self.assertEqual(self.new_solution.fix_choice, self.data['fix_choice'])
        self.assertEqual(self.new_solution.fix_guide, self.data['fix_guide'])


class SurveyQuestionTopicTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.data = {
            'order': 1,
            'title': 'Core question',
            'description': 'description',
            'version': self.cert_version
        }
        self.question_topic = SurveyQuestionTopic.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            '{0}) {1} | {2}'.format(self.question_topic.order, self.question_topic.title, self.question_topic.version),
            self.question_topic.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            '{0}) {1} | {2}'.format(self.question_topic.order, self.question_topic.title, self.question_topic.version),
            self.question_topic.__unicode__()
        )

    def test_create(self):
        self.assertEqual(2, SurveyQuestionTopic.objects.all().count())
        self.assertEqual(self.question_topic.order, self.data['order'])
        self.assertEqual(self.question_topic.title, self.data['title'])
        self.assertEqual(self.question_topic.description, self.data['description'])
        self.assertEqual(self.question_topic.version, self.data['version'])


class SurveyQuestionTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.data = {
            'order': 33.5,
            'response_type': SurveyQuestion.TEXT,
            'topic': self.question_topic,
            'required': True,
            'version': self.cert_version,
            'parent': self.survey_question,
            'widget': SurveyQuestion.WIDGET_CHECKBOX,
            'considered_as_valid': True,
            'hide_choices': True,
            'other_form': True,
            'auto_answer': False,
            'auto_answer_choice': None,
            'auto_answer_survey_question': None,
            'auto_answer_survey_question_boolean': False,
            'auto_answer_current_question_boolean': False,
            'auto_answer_survey_question_choice': None,
            'auto_answer_current_question_choice': None,
            'title': 'da',
            'pervade_title': 'Pervade title',
            'pervade_id': 'asdf3463456yhdfg',
            'pervade_reverse_value': False,
            'pervade_save_applicant_notes': True,
            'tooltip': 'asdfasdfa',
            'moreinfo': 'good info',
            'show_children_on': True,
            'ranking': SurveyQuestion.RANKING_MAJOR
        }
        self.new_survey_question = SurveyQuestion.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_create(self):
        self.assertEqual(2, SurveyQuestion.objects.all().count())
        for field in self.data:
            self.assertEqual(getattr(self.new_survey_question, field), self.data.get(field))

    def test_method___str__(self):
        self.assertEqual(
            '33.5. da v1.0 - Cyber Essentials',
            self.new_survey_question.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            '33.5. da v1.0 - Cyber Essentials',
            self.new_survey_question.__unicode__()
        )

    def test_property_failed_custom_choice(self):
        self.assertEqual(self.new_survey_question.failed_custom_choice, False)
        self.assertEqual(self.new_survey_question.failed_choices.all().count(), 0)
        self.assertEqual(self.new_survey_question.failed_responses.all().count(), 0)

        self.new_survey_question.failed_choices.create(
            value_text='Test choice'
        )

        self.assertEqual(self.new_survey_question.failed_custom_choice, False)
        self.assertEqual(self.new_survey_question.failed_choices.all().count(), 1)
        self.assertEqual(self.new_survey_question.failed_responses.all().count(), 0)

        new_survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.all()[0],
            datetime_started=timezone.now()
        )
        self.new_survey_question.failed_responses.create(
            survey=new_survey
        )

        self.assertEqual(self.new_survey_question.failed_custom_choice, True)
        self.assertEqual(self.new_survey_question.failed_choices.all().count(), 1)
        self.assertEqual(self.new_survey_question.failed_responses.all().count(), 1)

    @override_settings(USE_L10N=True)
    def test_order_str(self):
        self.assertEqual(self.new_survey_question.order_str, f'{self.data["order"]}')
        self.new_survey_question.order = 45.3
        self.new_survey_question.save()
        self.assertEqual(self.new_survey_question.order_str, '45.3')
        template = Template('{{ question.order_str }}')

        with translation.override('fr'):
            rendered = template.render(Context({'question': self.new_survey_question}))
            self.assertEqual(rendered, '45.3')

        with translation.override('en-gb'):
            rendered = template.render(Context({'question': self.new_survey_question}))
            self.assertEqual(rendered, '45.3')

    def test_show_question_with_parent_show_on_true(self):
        """Test show_question() behavior when question has a parent with show_on_true"""
        survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.first(),
            datetime_started=timezone.now()
        )
        # Create a parent question
        parent_question = SurveyQuestion.objects.create(
            order=1.0,
            title="Parent Question",
            version=self.cert_version,
            show_children_on=True  # Show children when parent response is True
        )
        # Create child question with parent
        child_question = SurveyQuestion.objects.create(
            order=2.0,
            title="Child Question",
            version=self.cert_version,
            parent=parent_question
        )

        # Case 1: No parent response exists yet
        # Child should not be shown since parent show_children_on=True but no response
        self.assertFalse(child_question.show_question(survey.id))

        # Case 2: Parent response is None
        # Child should not be shown since parent response is None
        parent_response = SurveyResponse.objects.create(
            question=parent_question,
            survey=survey,
            not_applicable=True,
            value_boolean=None
        )
        self.assertFalse(child_question.show_question(survey.id))

        # Case 3: Parent response matches show_children_on (True)
        # Child should be shown since parent response matches show_children_on
        parent_response.not_applicable = False
        parent_response.value_boolean = True
        parent_response.save()
        self.assertTrue(child_question.show_question(survey.id))

        # Case 4: Parent response does not match show_children_on
        parent_response.value_boolean = False
        parent_response.save()
        # Child should not be shown since parent response does not match show_children_on
        self.assertFalse(child_question.show_question(survey.id))

    def test_show_question_with_parent_show_on_false(self):
        """Test show_question() behavior when question has a parent with show_on_false"""
        survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.first(),
            datetime_started=timezone.now()
        )
        # Create a parent question
        parent_question = SurveyQuestion.objects.create(
            order=1.0,
            title="Parent Question",
            version=self.cert_version,
            show_children_on=False  # Show children when parent response is False
        )
        # Create child question with parent
        child_question = SurveyQuestion.objects.create(
            order=2.0,
            title="Child Question",
            version=self.cert_version,
            parent=parent_question
        )

        # Case 1: No parent response exists yet
        # Child should not be shown since parent show_children_on=False but no response
        self.assertFalse(child_question.show_question(survey.id))

        # Case 2: Parent response is None
        # Child should not be shown since parent response is None
        parent_response = SurveyResponse.objects.create(
            question=parent_question,
            survey=survey,
            not_applicable=True,
            value_boolean=None
        )
        self.assertFalse(child_question.show_question(survey.id))

        # Case 3: Parent response matches show_children_on (False)
        # Child should be shown since parent response matches show_children_on
        parent_response.not_applicable = False
        parent_response.value_boolean = False
        parent_response.save()
        self.assertTrue(child_question.show_question(survey.id))

        # Case 4: Parent response does not match show_children_on
        # Child should not be shown since parent response does not match show_children_on
        parent_response.value_boolean = True
        parent_response.save()
        self.assertFalse(child_question.show_question(survey.id))

    def test_show_question_with_parent_2(self):
        """Test show_question() behavior when question has a parent with show_on_true and show_on_false"""
        survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.first(),
            datetime_started=timezone.now()
        )
        # Create a parent question
        parent_question = SurveyQuestion.objects.create(
            order=1.0,
            title="Parent Question",
            version=self.cert_version,
            show_children_on_2=True  # Show children when parent response is True
        )
        # Create child question with parent
        child_question = SurveyQuestion.objects.create(
            order=2.0,
            title="Child Question",
            version=self.cert_version,
            parent_2=parent_question
        )

        # Case 1: No parent response exists yet
        # Child should not be shown since parent show_children_on=True but no response
        self.assertFalse(child_question.show_question(survey.id))

        # Case 2: Parent response is None
        # Child should not be shown since parent response is None
        parent_response = SurveyResponse.objects.create(
            question=parent_question,
            survey=survey,
            not_applicable=True,
            value_boolean=None
        )
        self.assertFalse(child_question.show_question(survey.id))

        # Case 3: Parent response matches show_children_on (True)
        # Child should be shown since parent response matches show_children_on
        parent_response.not_applicable = False
        parent_response.value_boolean = True
        parent_response.save()
        self.assertTrue(child_question.show_question(survey.id))

        # Case 4: Parent response does not match show_children_on
        # Child should not be shown since parent response does not match show_children_on
        parent_response.value_boolean = False
        parent_response.save()
        self.assertFalse(child_question.show_question(survey.id))


class SurveyQuestionChoicesTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.question_data = {
            'order': 33.5,
            'response_type': SurveyQuestion.TEXT,
            'topic': self.question_topic,
            'required': True,
            'version': self.cert_version,
            'parent': self.survey_question,
            'widget': SurveyQuestion.WIDGET_CHECKBOX,
            'considered_as_valid': True,
            'hide_choices': True,
            'other_form': True,
            'auto_answer': False,
            'auto_answer_choice': None,
            'auto_answer_survey_question': None,
            'auto_answer_survey_question_boolean': False,
            'auto_answer_current_question_boolean': False,
            'auto_answer_survey_question_choice': None,
            'auto_answer_current_question_choice': None,
            'title': 'da',
            'pervade_title': 'Pervade title',
            'pervade_id': 'asdf3463456yhdfg',
            'pervade_reverse_value': False,
            'pervade_save_applicant_notes': True,
            'tooltip': 'asdfasdfa',
            'moreinfo': 'good info',
            'show_children_on': True,
            'ranking': SurveyQuestion.RANKING_MAJOR
        }
        self.new_survey_question = SurveyQuestion.objects.create(
            **self.question_data
        )
        self.data = {
            'question': self.new_survey_question,
            'value_text': 'Choice text',
            'value_integer': 8,
            'value_boolean': True,
            'value_float': 8.8,
            'value_date': timezone.now().date(),
            'value_datetime': timezone.now(),
            'value_file': get_temporary_text_file(),
            'value_image': get_temporary_image(),
        }
        self.question_choice = SurveyQuestionChoices.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            'Question {0}) {1} | {2}'.format(
                self.question_choice.question.order_str,  # pylint: disable=no-member
                self.question_choice.value,  # pylint: disable=no-member
                self.question_choice.question.version  # pylint: disable=no-member
            ),
            self.question_choice.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            'Question {0}) {1} | {2}'.format(
                self.question_choice.question.order_str,  # pylint: disable=no-member
                self.question_choice.value,  # pylint: disable=no-member
                self.question_choice.question.version  # pylint: disable=no-member
            ),
            self.question_choice.__unicode__()
        )

    def test_create(self):
        self.assertEqual(1, SurveyQuestionChoices.objects.all().count())
        self.assertEqual(self.question_choice.question, self.data['question'])

    def test_property_value(self):
        self.assertEqual(self.question_choice.value, self.data['value_text'])
        self.question_choice.question.response_type = SurveyQuestion.INTEGER
        self.question_choice.save()
        self.assertEqual(self.question_choice.value, self.data['value_integer'])
        self.question_choice.question.response_type = SurveyQuestion.BOOLEAN
        self.question_choice.save()
        self.assertEqual(self.question_choice.value, self.data['value_boolean'])
        self.question_choice.question.response_type = SurveyQuestion.FLOAT
        self.question_choice.save()
        self.assertEqual(self.question_choice.value, self.data['value_float'])
        self.question_choice.question.response_type = SurveyQuestion.DATE
        self.question_choice.save()
        self.assertEqual(self.question_choice.value, self.data['value_date'])
        self.question_choice.question.response_type = SurveyQuestion.DATETIME
        self.question_choice.save()
        self.assertEqual(self.question_choice.value, self.data['value_datetime'])
        self.question_choice.question.response_type = SurveyQuestion.FILE
        self.question_choice.save()
        self.assertEqual(os.path.basename(self.question_choice.value.path), self.data['value_file'].name)
        self.question_choice.question.response_type = SurveyQuestion.IMAGE
        self.question_choice.save()
        self.assertEqual(os.path.basename(self.question_choice.value.path), self.data['value_image'].name)


class SurveyQuestionFailedChoicesTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.question_data = {
            'order': 33.5,
            'response_type': SurveyQuestion.TEXT,
            'topic': self.question_topic,
            'required': True,
            'version': self.cert_version,
            'parent': self.survey_question,
            'widget': SurveyQuestion.WIDGET_CHECKBOX,
            'considered_as_valid': True,
            'hide_choices': True,
            'other_form': True,
            'auto_answer': False,
            'auto_answer_choice': None,
            'auto_answer_survey_question': None,
            'auto_answer_survey_question_boolean': False,
            'auto_answer_current_question_boolean': False,
            'auto_answer_survey_question_choice': None,
            'auto_answer_current_question_choice': None,
            'title': 'da',
            'pervade_title': 'Pervade title',
            'pervade_id': 'asdf3463456yhdfg',
            'pervade_reverse_value': False,
            'pervade_save_applicant_notes': True,
            'tooltip': 'asdfasdfa',
            'moreinfo': 'good info',
            'show_children_on': True,
            'ranking': SurveyQuestion.RANKING_MAJOR
        }
        self.new_survey_question = SurveyQuestion.objects.create(
            **self.question_data
        )
        self.data = {
            'question': self.new_survey_question,
            'value_text': 'Choice text',
            'value_integer': 8,
            'value_boolean': True,
            'value_float': 8.8,
            'value_date': timezone.now().date(),
            'value_datetime': timezone.now(),
            'value_file': get_temporary_text_file(),
            'value_image': get_temporary_image(),
        }
        self.question_failed_choice = SurveyQuestionFailedChoices.objects.create(
            **self.data
        )

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            'Question {0}) {1} | {2}'.format(
                self.question_failed_choice.question.order_str,  # pylint: disable=no-member
                self.question_failed_choice.value,  # pylint: disable=no-member
                self.question_failed_choice.question.version  # pylint: disable=no-member
            ),
            self.question_failed_choice.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            'Question {0}) {1} | {2}'.format(
                self.question_failed_choice.question.order_str,  # pylint: disable=no-member
                self.question_failed_choice.value,  # pylint: disable=no-member
                self.question_failed_choice.question.version  # pylint: disable=no-member
            ),
            self.question_failed_choice.__unicode__()
        )

    def test_create(self):
        self.assertEqual(1, SurveyQuestionFailedChoices.objects.all().count())
        self.assertEqual(self.question_failed_choice.question, self.data['question'])

    def test_property_value(self):
        self.assertEqual(self.question_failed_choice.value, self.data['value_text'])
        self.question_failed_choice.question.response_type = SurveyQuestion.INTEGER
        self.question_failed_choice.save()
        self.assertEqual(self.question_failed_choice.value, self.data['value_integer'])
        self.question_failed_choice.question.response_type = SurveyQuestion.BOOLEAN
        self.question_failed_choice.save()
        self.assertEqual(self.question_failed_choice.value, self.data['value_boolean'])
        self.question_failed_choice.question.response_type = SurveyQuestion.FLOAT
        self.question_failed_choice.save()
        self.assertEqual(self.question_failed_choice.value, self.data['value_float'])
        self.question_failed_choice.question.response_type = SurveyQuestion.DATE
        self.question_failed_choice.save()
        self.assertEqual(self.question_failed_choice.value, self.data['value_date'])
        self.question_failed_choice.question.response_type = SurveyQuestion.DATETIME
        self.question_failed_choice.save()
        self.assertEqual(self.question_failed_choice.value, self.data['value_datetime'])
        self.question_failed_choice.question.response_type = SurveyQuestion.FILE
        self.question_failed_choice.save()
        self.assertEqual(os.path.basename(self.question_failed_choice.value.path), self.data['value_file'].name)
        self.question_failed_choice.question.response_type = SurveyQuestion.IMAGE
        self.question_failed_choice.save()
        self.assertEqual(os.path.basename(self.question_failed_choice.value.path), self.data['value_image'].name)


class SurveyResponseTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.question_data = {
            'order': 33.5,
            'response_type': SurveyQuestion.TEXT,
            'topic': self.question_topic,
            'required': True,
            'version': self.cert_version,
            'parent': self.survey_question,
            'widget': SurveyQuestion.WIDGET_CHECKBOX,
            'considered_as_valid': True,
            'hide_choices': True,
            'other_form': True,
            'auto_answer': False,
            'auto_answer_choice': None,
            'auto_answer_survey_question': None,
            'auto_answer_survey_question_boolean': False,
            'auto_answer_current_question_boolean': False,
            'auto_answer_survey_question_choice': None,
            'auto_answer_current_question_choice': None,
            'title': 'da',
            'pervade_title': 'Pervade title',
            'pervade_id': 'asdf3463456yhdfg',
            'pervade_reverse_value': False,
            'pervade_save_applicant_notes': True,
            'tooltip': 'asdfasdfa',
            'moreinfo': 'good info',
            'show_children_on': True,
            'ranking': SurveyQuestion.RANKING_MAJOR
        }
        self.new_survey_question = SurveyQuestion.objects.create(
            **self.question_data
        )
        self.choice_data = {
            'question': self.new_survey_question,
            'value_text': 'Choice text',
            'value_integer': 8,
            'value_boolean': True,
            'value_float': 8.8,
            'value_date': timezone.now().date(),
            'value_datetime': timezone.now(),
            'value_file': get_temporary_text_file(),
            'value_image': get_temporary_image(),
        }
        self.question_choice = SurveyQuestionChoices.objects.create(
            **self.choice_data
        )
        self.new_survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.all()[0],
            datetime_started=timezone.now()
        )
        self.data = {
            'not_applicable': False,
            'survey': self.new_survey,
            'question': self.new_survey_question,
            'choice': None,
            'value_text': 'Response text',
            'value_integer': 8,
            'value_boolean': True,
            'value_float': 8.8,
            'value_date': timezone.now().date(),
            'value_datetime': timezone.now(),
            'value_file': get_temporary_text_file(),
            'value_image': get_temporary_image(),
        }
        self.survey_response = SurveyResponse.objects.create(**self.data)

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            '{0} survey [{1}] {2}'.format(
                self.new_survey.certificate.organisation.name,  # pylint: disable=no-member
                self.new_survey.id,  # pylint: disable=no-member
                self.survey_response.question  # pylint: disable=no-member
            ),
            self.survey_response.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            '{0} survey [{1}] {2}'.format(
                self.new_survey.certificate.organisation.name,  # pylint: disable=no-member
                self.new_survey.id,  # pylint: disable=no-member
                self.survey_response.question  # pylint: disable=no-member
            ),
            self.survey_response.__unicode__()
        )

    def test_create(self):
        self.assertEqual(1, SurveyResponse.objects.all().count())
        self.assertEqual(self.survey_response.question, self.data['question'])
        self.assertEqual(self.survey_response.value, self.data['value_text'])
        self.assertEqual(self.survey_response.not_applicable, self.data['not_applicable'])
        self.assertEqual(self.survey_response.survey, self.data['survey'])
        self.assertEqual(self.survey_response.choice, self.data['choice'])

    def test_property_value(self):
        self.assertEqual(self.survey_response.value, self.data['value_text'])
        self.survey_response.question.response_type = SurveyQuestion.INTEGER
        self.survey_response.save()
        self.assertEqual(self.survey_response.value, self.data['value_integer'])
        self.survey_response.question.response_type = SurveyQuestion.BOOLEAN
        self.survey_response.save()
        self.assertEqual(self.survey_response.value, self.data['value_boolean'])
        self.survey_response.question.response_type = SurveyQuestion.FLOAT
        self.survey_response.save()
        self.assertEqual(self.survey_response.value, self.data['value_float'])
        self.survey_response.question.response_type = SurveyQuestion.DATE
        self.survey_response.save()
        self.assertEqual(self.survey_response.value, self.data['value_date'])
        self.survey_response.question.response_type = SurveyQuestion.DATETIME
        self.survey_response.save()
        self.assertEqual(self.survey_response.value, self.data['value_datetime'])
        self.survey_response.question.response_type = SurveyQuestion.FILE
        self.survey_response.save()
        self.assertEqual(os.path.basename(self.survey_response.value.path), self.data['value_file'].name)
        self.survey_response.question.response_type = SurveyQuestion.IMAGE
        self.survey_response.save()
        self.assertEqual(os.path.basename(self.survey_response.value.path), self.data['value_image'].name)

    def test_property_regular_value(self):
        self.assertEqual(self.survey_response.regular_value, self.data['value_text'])
        self.survey_response.choice = self.question_choice
        self.survey_response.save()
        self.assertEqual(self.survey_response.regular_value, self.choice_data['value_text'])

    def test_property_get_value(self):
        self.assertEqual(self.survey_response.get_value, self.data['value_text'])
        self.survey_response.choice = self.question_choice
        self.survey_response.save()
        self.assertEqual(self.survey_response.get_value, self.choice_data['value_text'])
        self.survey_response.not_applicable = True
        self.survey_response.value_boolean = None
        self.survey_response.save()
        self.assertEqual(self.survey_response.get_value, 'N/A')
        self.survey_response.value_boolean = True
        self.survey_response.save()
        self.assertEqual(self.survey_response.get_value, False)
        SurveyFailedResponse.objects.create(
            survey=self.new_survey,
            question=self.new_survey_question,
            choice=None,
            value_text='Failed Response text',
        )
        self.assertEqual(self.survey_response.get_value, 'Failed Response text')

    def test_property_get_choice(self):
        self.assertEqual(self.survey_response.get_choice, None)
        self.survey_response.choice = self.question_choice
        self.survey_response.save()
        self.assertEqual(self.survey_response.get_choice, self.question_choice)

    def test_property_pervade_answer_value(self):
        self.survey_response.question.pervade_save_applicant_notes = True
        self.survey_response.not_applicable = False
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'Yes')
        self.survey_response.question.pervade_reverse_value = True
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'No')

        self.survey_response.value = ''
        self.survey_response.save()
        self.survey_response.question.pervade_reverse_value = False
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'No')
        self.survey_response.question.pervade_reverse_value = True
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'Yes')

        self.survey_response.value = 'invalid'
        self.survey_response.save()
        self.survey_response.question.pervade_save_applicant_notes = False
        self.survey_response.question.widget = self.survey_response.question.WIDGET_ORGANISATION_TYPE_SELECT
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_answer_value, None)
        self.survey_response.value = self.organisation.ORG_CHOICES[7][0]  # valid value
        self.assertEqual(self.survey_response.pervade_answer_value, self.organisation.ORG_CHOICES[7][1])

        self.survey_response.question.widget = self.survey_response.question.WIDGET_DEFAULT
        self.survey_response.question.response_type = self.survey_response.question.BOOLEAN
        self.survey_response.not_applicable = True
        self.survey_response.value_boolean = None
        self.survey_response.question.save()
        self.survey_response.value_text = 'N/A'
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'N/A')
        self.survey_response.value_boolean = False
        self.survey_response.question.pervade_reverse_value = False
        self.survey_response.question.save()
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'No')
        fr = SurveyFailedResponse.objects.create(
            survey=self.new_survey,
            question=self.new_survey_question,
            choice=None,
            value_boolean=True,
        )
        self.assertEqual(self.survey_response.pervade_answer_value, 'Yes')
        self.survey_response.question.pervade_reverse_value = True
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'No')
        fr.delete()
        self.assertEqual(self.survey_response.pervade_answer_value, 'Yes')

        self.survey_response.question.widget = self.survey_response.question.WIDGET_DEFAULT
        self.survey_response.question.response_type = self.survey_response.question.FILE
        self.survey_response.question.save()
        self.survey_response.not_applicable = False
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, self.data['value_file'].name)
        self.survey_response.question.response_type = self.survey_response.question.IMAGE
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_answer_value, self.data['value_image'].name)

        self.survey_response.value_image = None
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, None)
        self.survey_response.value_image = self.data['value_image']
        self.survey_response.save()

        self.survey_response.question.response_type = self.survey_response.question.TEXT
        self.survey_response.question.save()
        self.survey_response.value = 'Privet'
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'Privet')

        self.survey_response.question.response_type = self.survey_response.question.INTEGER
        self.survey_response.question.pervade_reverse_value = False
        self.survey_response.question.save()
        self.survey_response.value = 5
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 5)
        self.survey_response.value = False
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_answer_value, 'No')

    def test_property_pervade_applicant_notes_value(self):
        self.survey_response.question.pervade_save_applicant_notes = True
        self.survey_response.question.widget = self.survey_response.question.WIDGET_DEFAULT
        self.survey_response.question.response_type = self.survey_response.question.BOOLEAN
        self.survey_response.value_boolean = None
        self.survey_response.question.save()
        self.survey_response.value_text = 'N/A'
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, 'N/A')
        self.survey_response.value_boolean = False
        self.survey_response.question.pervade_reverse_value = False
        self.survey_response.question.save()
        self.survey_response.not_applicable = True
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, 'No')
        fr = SurveyFailedResponse.objects.create(
            survey=self.new_survey,
            question=self.new_survey_question,
            choice=None,
            value_boolean=True,
        )
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, 'Yes')
        self.survey_response.question.pervade_reverse_value = True
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, 'No')
        fr.delete()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, 'Yes')

        self.survey_response.question.widget = self.survey_response.question.WIDGET_DEFAULT
        self.survey_response.question.response_type = self.survey_response.question.FILE
        self.survey_response.question.save()
        self.survey_response.not_applicable = False
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, self.data['value_file'].name)
        self.survey_response.question.response_type = self.survey_response.question.IMAGE
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, self.data['value_image'].name)

        self.survey_response.value_image = None
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, None)
        self.survey_response.value_image = self.data['value_image']
        self.survey_response.save()

        self.survey_response.question.response_type = self.survey_response.question.INTEGER
        self.survey_response.question.pervade_reverse_value = False
        self.survey_response.question.save()
        self.survey_response.value = 5
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, 5)
        self.survey_response.value = False
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, False)

        self.survey_response.question.pervade_save_applicant_notes = False
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_applicant_notes_value, '')

    def test_property_pervade_assessor_score(self):
        self.survey_response.question.ranking = SurveyQuestion.RANKING_NONE
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_assessor_score, 'pass')

        self.survey_response.question.ranking = SurveyQuestion.RANKING_MINOR
        self.survey_response.question.response_type = SurveyQuestion.BOOLEAN
        self.survey_response.question.save()
        self.survey_response.value = True
        self.survey_response.not_applicable = False
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_assessor_score, 'pass')

        self.survey_response.question.ranking = SurveyQuestion.RANKING_MINOR
        self.survey_response.question.save()
        self.survey_response.not_applicable = True
        self.survey_response.save()
        self.assertEqual(self.survey_response.pervade_assessor_score, 'Minor Non-Compliance')

        self.survey_response.question.ranking = SurveyQuestion.RANKING_MAJOR
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_assessor_score, 'Major Non-Compliance')

        self.survey_response.question.ranking = SurveyQuestion.RANKING_FAIL
        self.survey_response.question.save()
        self.assertEqual(self.survey_response.pervade_assessor_score, 'auto')


class SurveyFailedResponseTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.question_data = {
            'order': 33.5,
            'response_type': SurveyQuestion.TEXT,
            'topic': self.question_topic,
            'required': True,
            'version': self.cert_version,
            'parent': self.survey_question,
            'widget': SurveyQuestion.WIDGET_CHECKBOX,
            'considered_as_valid': True,
            'hide_choices': True,
            'other_form': True,
            'auto_answer': False,
            'auto_answer_choice': None,
            'auto_answer_survey_question': None,
            'auto_answer_survey_question_boolean': False,
            'auto_answer_current_question_boolean': False,
            'auto_answer_survey_question_choice': None,
            'auto_answer_current_question_choice': None,
            'title': 'da',
            'pervade_title': 'Pervade title',
            'pervade_id': 'asdf3463456yhdfg',
            'pervade_reverse_value': False,
            'pervade_save_applicant_notes': True,
            'tooltip': 'asdfasdfa',
            'moreinfo': 'good info',
            'show_children_on': True,
            'ranking': SurveyQuestion.RANKING_MAJOR
        }
        self.new_survey_question = SurveyQuestion.objects.create(
            **self.question_data
        )
        self.choice_data = {
            'question': self.new_survey_question,
            'value_text': 'Choice text',
            'value_integer': 8,
            'value_boolean': True,
            'value_float': 8.8,
            'value_date': timezone.now().date(),
            'value_datetime': timezone.now(),
            'value_file': get_temporary_text_file(),
            'value_image': get_temporary_image(),
        }
        self.question_choice = SurveyQuestionChoices.objects.create(
            **self.choice_data
        )
        self.new_survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.all()[0],
            datetime_started=timezone.now()
        )
        self.data = {
            'survey': self.new_survey,
            'question': self.new_survey_question,
            'choice': None,
            'value_text': 'Response text',
            'value_integer': 8,
            'value_boolean': True,
            'value_float': 8.8,
            'value_date': timezone.now().date(),
            'value_datetime': timezone.now(),
            'value_file': get_temporary_text_file(),
            'value_image': get_temporary_image(),
        }
        self.survey_failed_response = SurveyFailedResponse.objects.create(**self.data)

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_method___str__(self):
        self.assertEqual(
            '{0} survey [{1}] {2}'.format(
                self.new_survey.certificate.organisation.name,  # pylint: disable=no-member
                self.new_survey.id,  # pylint: disable=no-member
                self.survey_failed_response.question  # pylint: disable=no-member
            ),
            self.survey_failed_response.__str__()
        )

    def test_method___unicode__(self):
        self.assertEqual(
            '{0} survey [{1}] {2}'.format(
                self.new_survey.certificate.organisation.name,  # pylint: disable=no-member
                self.new_survey.id,  # pylint: disable=no-member
                self.survey_failed_response.question  # pylint: disable=no-member
            ),
            self.survey_failed_response.__unicode__()
        )

    def test_create(self):
        self.assertEqual(1, SurveyFailedResponse.objects.all().count())
        self.assertEqual(self.survey_failed_response.question, self.data['question'])
        self.assertEqual(self.survey_failed_response.value, self.data['value_text'])
        self.assertEqual(self.survey_failed_response.survey, self.data['survey'])
        self.assertEqual(self.survey_failed_response.choice, self.data['choice'])

    def test_property_value(self):
        self.assertEqual(self.survey_failed_response.value, self.data['value_text'])
        self.survey_failed_response.question.response_type = SurveyQuestion.INTEGER
        self.survey_failed_response.save()
        self.assertEqual(self.survey_failed_response.value, self.data['value_integer'])
        self.survey_failed_response.question.response_type = SurveyQuestion.BOOLEAN
        self.survey_failed_response.save()
        self.assertEqual(self.survey_failed_response.value, self.data['value_boolean'])
        self.survey_failed_response.question.response_type = SurveyQuestion.FLOAT
        self.survey_failed_response.save()
        self.assertEqual(self.survey_failed_response.value, self.data['value_float'])
        self.survey_failed_response.question.response_type = SurveyQuestion.DATE
        self.survey_failed_response.save()
        self.assertEqual(self.survey_failed_response.value, self.data['value_date'])
        self.survey_failed_response.question.response_type = SurveyQuestion.DATETIME
        self.survey_failed_response.save()
        self.assertEqual(self.survey_failed_response.value, self.data['value_datetime'])
        self.survey_failed_response.question.response_type = SurveyQuestion.FILE
        self.survey_failed_response.save()
        self.assertEqual(os.path.basename(self.survey_failed_response.value.path), self.data['value_file'].name)
        self.survey_failed_response.question.response_type = SurveyQuestion.IMAGE
        self.survey_failed_response.save()
        self.assertEqual(os.path.basename(self.survey_failed_response.value.path), self.data['value_image'].name)


class CertificationUploadedSignalTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.core_setting_up()
        self.creating_user(u='different_user', e='<EMAIL>')
        self.creating_app_user()
        self.new_survey = CertificationSurvey.objects.create(
            certificate=self.organisation.certifications.all()[0],
            datetime_started=timezone.now()
        )
        self.date = timezone.now()

    def tearDown(self):
        self.deleting_user()
        self.core_tearing_down()
        self.app_user.delete()

    def test_signal(self):
        self.assertEqual(self.organisation.event_logs.filter(
            type=self.organisation.event_logs.model.GOT_CERTIFIED,
            survey=self.new_survey,
            organisation=self.organisation
        ).count(), 0)
        IssuedCertification.objects.create(
            certificate=self.new_survey.certificate,
            date=self.date,
            number=1,
            certificate_file=get_temporary_text_file(),
            report_file=get_temporary_text_file(),

        )
        self.assertEqual(self.organisation.event_logs.filter(
            type=self.organisation.event_logs.model.GOT_CERTIFIED,
            survey=self.new_survey,
            organisation=self.organisation
        ).count(), 1)
