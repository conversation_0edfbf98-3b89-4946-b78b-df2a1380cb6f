from datetime import datetime, timedelta

from django.test import TestCase
from django.http import HttpRequest, QueryDict
from django.utils import timezone

from common.base_tests import BaseTestCase
from distributors.models import Distributor
from organisations.models import Organisation, OrganisationCertification, CertificationVersion
from partners.models import Partner
from rulebook.models import CertificationSurvey, SurveyResponse, SurveyQuestion, CertificateType, CYBER_ESSENTIALS
from rulebook.questions import QUESTION_CODE_ORGANISATION_TYPE, QUESTION_CODE_REGISTRATION_NUMBER
from rulebook.utils import (
    get_pervade_address_response,
    get_pervade_contributor_address,
    SurveyTopicStat,
    handle_questions_with_parent_yes_no_radio_button,
    get_questionnaire_status_structure,
    get_topics_status_structure,
    handle_conscious_widget,
    organisation_number_validation, import_new_questions_from_pervade,
)

from rulebook.factories import SurveyQuestionFactory, SurveyQuestionTopicFactory, SurveyResponseFactory


class UtilsTestCase(BaseTestCase, TestCase):

    def setUp(self):
        self.creating_user()
        self.create_ce_certificate_version()
        distributor = Distributor.objects.create()
        partner = Partner.objects.create(distributor=distributor)
        self.organisation = Organisation.objects.create(partner=partner)
        self.ce_certificate = OrganisationCertification.objects.create(organisation=self.organisation, version=self.cert_version)
        self.ce_certificate_survey = CertificationSurvey.objects.create(
            certificate=self.ce_certificate, datetime_started=datetime.now())
        self.create_ce_question()
        self._create_address_responses(self.survey_question.id)
        self.pervade_data = {'c58da13eee944c50a848aa1e4dd725f64f049244ad5bfa9ec31e5b3bad54e940': {'id': 'c58da13eee944c50a848aa1e4dd725f64f049244ad5bfa9ec31e5b3bad54e940', 'name': '0.1 Assessment Scope', 'description': "Have you verified that the scope for this CE+ assessment is the same as the scope for the applicant's CE verified self assessment (VSA)?", 'options': ['Yes', 'No'], 'answertype': 'select', 'compliancefields': [], 'markingoptions': {'pass': 'Compliant', 'auto': 'Automatic Failure'}, 'evidence': 'none'}, 'fb2ebffdfa18a8ee3b35bf64461ab9592f51c7ee668da74b697d9a91ffa06341': {'id': 'fb2ebffdfa18a8ee3b35bf64461ab9592f51c7ee668da74b697d9a91ffa06341', 'name': '0.2 Networks and Locations in Scope', 'description': 'Provide a brief summary of the networks and locations in scope for this CE+ assessment.', 'answertype': 'textarea', 'compliancefields': [], 'markingoptions': {'pass': 'Compliant', 'auto': 'Automatic Failure'}, 'evidence': 'none'}, 'ced74fc6ddaae6be27c18bd1a99bd8ee360e0d0b49b650f5bba92535838cba34': {'id': 'ced74fc6ddaae6be27c18bd1a99bd8ee360e0d0b49b650f5bba92535838cba34', 'name': '0.3 Excluded Items', 'description': 'If you have chosen to exclude any items, please provide a summary.<br><br>For example: "Company Website is excluded because it is located with the cloud hosting provider, and as such not required, as per the Cyber Essentials Plus scoping requirements". If anything is excluded from the verified self assessment (VSA) in this CE+ assessment, then the VSA will need to be assessed again or the issue remediated within the prescribed 30 day window.', 'answertype': 'textarea', 'compliancefields': [], 'markingoptions': {'pass': 'Compliant', 'auto': 'Automatic Failure'}, 'evidence': 'none'}, 'b9afc75dc6294f59b1b7ef31642b4196a05d3018f9811f988d6b26a26a4e94d3': {'id': 'b9afc75dc6294f59b1b7ef31642b4196a05d3018f9811f988d6b26a26a4e94d3', 'name': '0.4 Organisation Name', 'description': "What is the name of the applicant?<br><br>Please provide the organisation's full name, to match that provided for their CE verified self assessment.", 'answertype': 'textarea', 'compliancefields': [], 'markingoptions': {'pass': 'Compliant', 'auto': 'Automatic Failure'}, 'evidence': 'none'}, '410d3eb6c1c3096212a0fe5f8899433afe47f95592b3a7f14f00558a73620007': {'id': '410d3eb6c1c3096212a0fe5f8899433afe47f95592b3a7f14f00558a73620007', 'name': '0.5 CE VSA Certification Number', 'description': "What is the CE verified self assessment (VSA) Blockmark certificate number for the applicant?<br><br>Please provide the certificate number for the client's CE self assessment questionnaire.", 'answertype': 'textarea', 'compliancefields': [], 'markingoptions': {'pass': 'Compliant', 'auto': 'Automatic Failure'}, 'evidence': 'none'}, 'e4cc81229381abdb028887450c3065dac82dc222bffe54db6490a57a81f94a1e': {'id': 'e4cc81229381abdb028887450c3065dac82dc222bffe54db6490a57a81f94a1e', 'name': '0.6 CE VSA Date', 'description': 'What date did the client pass their verified self assessment (VSA)?<br><br>Provide the date that the client passed their verified self assessment. Cyber Essentials Plus must be completed within 90 days of the date of certifying for Cyber Essentials. The 30 day remediation period is inclusive of the 90 days. The CE+ assessment should be completed as close as possible to the date of the Cyber Essentials verified self assessment certification date, and sufficient time must be allowed for the remediation period within the 90 days. Extensions will only be granted in extreme circumstances. This does not include Christmas, Easter or other publicly notified holidays, the dates of which are static or known in advance.', 'options': ['Yes', 'No'], 'answertype': 'select', 'compliancefields': {"b714f324142c5e3376c54134925c9db787405ecd8078bb84c88c0acea9f3b9bb": {"name": "Applicant Notes","type": "textarea"}}, 'markingoptions': {'pass': 'Compliant', 'auto': 'Automatic Failure'}, 'evidence': 'none'}, 'dd54c218fbdc84d03b671cd54a077b2b919ecb468d66e23726809b70b4bcfc27': {'id': 'dd54c218fbdc84d03b671cd54a077b2b919ecb468d66e23726809b70b4bcfc27', 'name': '0.7 CE+ Certification Date', 'description': 'On what date/s was the CE+ assessment carried out?<br><br>Please enter the date that the CE+ testing was carried out.', 'answertype': 'textarea', 'compliancefields': [], 'markingoptions': {'pass': 'Compliant', 'auto': 'Automatic Failure'}, 'evidence': 'none'}}
        # CE+ certificate
        self.create_cep_certificate_version()
        self.question_topic_cep = SurveyQuestionTopicFactory(
            order=1,
            title='Scoping',
            version=self.cert_version_cep,
            questions=None
        )
        self.survey_question_cep = SurveyQuestionFactory(
            response_type=SurveyQuestion.TEXT,
            topic=self.question_topic_cep,
            required=True,
            version=self.cert_version_cep,
            title="Some question?",
            pervade_title="0.1 Scope",
            pervade_id='c58da13eee944c50a848aa1e4dd725f64f049244ad5bfa9ec31e5b3bad54e940',
            order=1
        )
        self.survey_question_cep_extra_details = SurveyQuestionFactory(
            response_type=SurveyQuestion.BOOLEAN,
            widget=SurveyQuestion.WIDGET_CONSCIOUS,
            topic=self.question_topic_cep,
            required=True,
            version=self.cert_version_cep,
            title="If you have chosen to exclude any items, please provide a summary",
            pervade_title="0.3 Excluded Items",
            pervade_id='ced74fc6ddaae6be27c18bd1a99bd8ee360e0d0b49b650f5bba92535838cba34',
            order=2
        )
        self.survey_question_cep_to_be_deleted = SurveyQuestionFactory(
            response_type=SurveyQuestion.BOOLEAN,
            widget=SurveyQuestion.WIDGET_CONSCIOUS,
            topic=self.question_topic_cep,
            required=True,
            version=self.cert_version_cep,
            title="This question no longer exists",
            pervade_title="1 Out of scope",
            pervade_id='some_id',
            order=10
        )

    def tearDown(self):
        SurveyResponse.objects.all().delete()
        self.delete_ce_question()
        self.ce_certificate_survey.delete()
        self.ce_certificate.delete()
        self.organisation.delete()
        Partner.objects.all().delete()
        Distributor.objects.all().delete()
        self.deleting_user()

    def _create_address_responses(self, question_id):
        SurveyResponse.objects.create(
            survey=self.ce_certificate_survey,
            question_id=question_id,
            value_text='address1',
            code='address1'
        )
        SurveyResponse.objects.create(
            survey=self.ce_certificate_survey,
            question_id=question_id,
            value_text='address2',
            code='address2'
        )
        SurveyResponse.objects.create(
            survey=self.ce_certificate_survey,
            question_id=question_id,
            value_text='city',
            code='city'
        )
        SurveyResponse.objects.create(
            survey=self.ce_certificate_survey,
            question_id=question_id,
            value_text='county',
            code='county'
        )
        SurveyResponse.objects.create(
            survey=self.ce_certificate_survey,
            question_id=question_id,
            value_text='postcode',
            code='postcode'
        )

    def test_get_pervade_address_response(self):
        address_response = get_pervade_address_response(self.ce_certificate.id)
        self.assertEqual(address_response, {
            'Address Line 1': 'address1', 'Address Line 2': 'address2', 'Town/City': 'city',
            'County': 'county', 'Postcode': 'postcode'})

    def test_get_pervade_contributor_address(self):
        contributor_address = get_pervade_contributor_address(self.organisation)
        self.assertEqual(contributor_address, {
            'contributor_firstlineofaddress': 'address1', 'contributor_secondlineofaddress': 'address2',
            'contributor_city': 'city', 'contributor_county': 'county', 'contributor_postcode': 'postcode'})


    def test_parent_2_yes_or_no_is_not_applicable(self):
        certification = self.ce_certificate
        _, parent_2_response, child_2_question = create_parent_2_parent_child(certification)

        survey_topic_stat = SurveyTopicStat(certification, main_key_as_title=True)
        self.assertTrue(survey_topic_stat._parent_2_yes_or_no_is_not_applicable(child_2_question))
        topics_map = survey_topic_stat.get()
        self.assertDictEqual(topics_map['Insurance'], {'total': 1,  'answered': 1, 'left': 0, 'order': 2})

        parent_2_response.value_boolean = False
        parent_2_response.save()

        survey_topic_stat = SurveyTopicStat(certification, main_key_as_title=True)
        self.assertFalse(survey_topic_stat._parent_2_yes_or_no_is_not_applicable(child_2_question))
        topics_map = survey_topic_stat.get()

        # If the parent logic is not applicable (because value is different), the child_question must be answered.
        self.assertDictEqual(topics_map['Insurance'], {'total': 2, 'answered': 1, 'left': 1, 'order': 2})


    def test_handle_questions_with_parent_yes_no_radio_button_for_parent_2(self):
        certification = self.ce_certificate

        _, parent_2_response, child_2_question = create_parent_2_parent_child(certification)
        child_2_response = SurveyResponse.objects.create(
            survey=certification.survey,
            question=child_2_question,
            value_boolean=True,
        )

        ret = handle_questions_with_parent_yes_no_radio_button(child_2_question, certification, is_parent_2=True)
        self.assertTrue(ret)
        child_2_response.refresh_from_db()
        # Show that the value gets reset
        self.assertEqual(child_2_response.not_applicable, True)
        self.assertEqual(child_2_response.value_boolean, None)

        parent_2_response.value_boolean = False
        parent_2_response.save()

        ret = handle_questions_with_parent_yes_no_radio_button(child_2_question, certification, is_parent_2=True)
        self.assertFalse(ret)

    def test_get_questionnaire_queryset(self):
        certification = self.ce_certificate
        parent_2_question, parent_2_response, child_2_question = create_parent_2_parent_child(certification)
        structure = get_questionnaire_status_structure(certification, key_as_pk=True)
        self.assertNotIn(child_2_question.pk, structure)

        parent_2_response.value_boolean = False
        parent_2_response.save()
        structure = get_questionnaire_status_structure(certification, key_as_pk=True)
        # Because parent_2 is matching show_children_on_2, the child answer is included
        self.assertIn(child_2_question.pk, structure)

    def test_get_topics_status_structure(self):
        """Test get_topics_status_structure returns correct topic status structure"""
        certification = self.ce_certificate
        topic1 = SurveyQuestionTopicFactory(version=certification.version, order=1)
        topic2 = SurveyQuestionTopicFactory(version=certification.version, order=2)
        topic3 = SurveyQuestionTopicFactory(version=certification.version, order=3)
        topic1.questions.all().delete()
        topic2.questions.all().delete()
        topic3.questions.all().delete()

        # Create questions with ranking
        topic1_question1 = SurveyQuestionFactory(version=certification.version, topic=topic1, ranking=1)
        topic1_question2 = SurveyQuestionFactory(version=certification.version, topic=topic1, ranking=1)
        # create nested parent questions
        topic1_parent_question = SurveyQuestionFactory(version=certification.version, topic=topic1, ranking=1)
        topic1_child_question = SurveyQuestionFactory(version=certification.version, topic=topic1, ranking=1, parent=topic1_parent_question)
        topic1_child_child_question = SurveyQuestionFactory(version=certification.version, topic=topic1, ranking=1, parent=topic1_child_question, show_children_on=True)
        SurveyQuestionFactory(version=certification.version, topic=topic2, ranking=1)  # topic2_question

        # Create questions without ranking
        topic3_question1 = SurveyQuestionFactory(version=certification.version, topic=topic3, ranking=SurveyQuestion.RANKING_NONE, required=False)
        SurveyQuestionFactory(version=certification.version, topic=topic3, ranking=SurveyQuestion.RANKING_NONE, required=False) # topic3_question2

        # Create responses - topic1 all passed, topic2 has failures
        SurveyResponseFactory(question=topic1_question1, survey=certification.survey)
        SurveyResponseFactory(question=topic1_question2, survey=certification.survey)
        SurveyResponseFactory(question=topic1_parent_question, survey=certification.survey, value_text="a choice")
        # this simulates a non-mandatory checkbox response so the response is not applicable and empty
        SurveyResponseFactory(question=topic1_child_question, survey=certification.survey, not_applicable=True)
        SurveyResponseFactory(question=topic1_child_child_question, survey=certification.survey, not_applicable=True)
        SurveyResponseFactory(question=topic3_question1, survey=certification.survey)

        # Create structure dict simulating get_questionnaire_status_structure response
        structure = get_questionnaire_status_structure(certification, key_as_pk=True, no_response_as_none=True)

        # Test with include_required=False
        result = get_topics_status_structure(certification, structure, include_required=False)

        self.assertEqual(result['passed'], [topic1.pk])
        self.assertEqual(result['failed'], [topic2.pk])
        self.assertEqual(set(result['without_ranking']), {topic3.pk})

        # Test with include_required=True
        result = get_topics_status_structure(certification, structure, include_required=True)

        self.assertEqual(result['passed'], [topic1.pk])
        self.assertEqual(result['failed'], [topic2.pk])
        self.assertEqual(set(result['without_ranking_and_not_required']), {topic3.pk})

        # Test with recognize_incomplete=True & no_response_as_none=True
        structure = get_questionnaire_status_structure(certification, key_as_pk=True, no_response_as_none=True)
        result = get_topics_status_structure(certification, structure, recognize_incomplete=True)
        self.assertEqual(result['passed'], [topic1.pk])
        self.assertEqual(result['failed'], [])
        self.assertEqual(result['incomplete'], [topic2.pk])
        self.assertEqual(set(result['without_ranking']), {topic3.pk})

    def test_handle_conscious_widget_lack_of_response_does_not_create_duplicate_responses(self):
        question = SurveyQuestionFactory(
            version=self.ce_certificate.version,
            response_type='boolean',
            widget=SurveyQuestion.WIDGET_CONSCIOUS,
            required=True,
        )

        SurveyResponseFactory(
            survey=self.ce_certificate_survey,
            question=question,
            value_boolean=True,
            not_applicable=False,
        )

        key = f"response_{question.pk}_{question.order_str}"
        request = HttpRequest()
        request.POST = QueryDict(f"{key}=")

        handle_conscious_widget(question, request, self.ce_certificate)

        responses = SurveyResponse.objects.filter(question=question, survey=self.ce_certificate_survey)
        self.assertEqual(responses.count(), 1)
        self.assertTrue(responses.first().not_applicable)

    def create_number_validation_data(self, org_type_answer, org_number_answer):
        self.ce_certificate.version.version_number = 2025.0
        self.ce_certificate.version.save()

        self.org_type_question = SurveyQuestionFactory(
            version=self.ce_certificate.version,
            code=QUESTION_CODE_ORGANISATION_TYPE
        )
        self.org_number_question = SurveyQuestionFactory(
            version=self.ce_certificate.version,
            code=QUESTION_CODE_REGISTRATION_NUMBER,
            widget=SurveyQuestion.WIDGET_ORGANISATION_REGISTRATION,
        )
        self.org_type_response = SurveyResponseFactory(
            survey=self.ce_certificate_survey,
            question=self.org_type_question,
            value_text=org_type_answer,
        )

    def test_organisation_number_validation_simple(self):
        org_number_answer = '12345678'
        self.create_number_validation_data(org_type_answer='LTD  - Limited Company (Ltd or PLC)', org_number_answer=org_number_answer)
        self.assertTrue(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))
        SurveyResponseFactory(
            survey=self.ce_certificate_survey,
            question=self.org_number_question,
            value_text='someothernumber',
        )
        self.assertTrue(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))

    def test_organisation_number_validation_valid_none(self):
        org_number_answer = 'None'
        self.create_number_validation_data(org_type_answer='SOC - Other Club/Society', org_number_answer=org_number_answer)
        self.assertTrue(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))
        SurveyResponseFactory(
            survey=self.ce_certificate_survey,
            question=self.org_number_question,
            value_text='someothernumber',
        )
        self.assertTrue(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))

    def test_organisation_number_validation_valid_none_2(self):
        org_number_answer = 'none'
        self.create_number_validation_data(org_type_answer='GOV - Government Agency or Public Body', org_number_answer=org_number_answer)
        self.assertTrue(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))

    def test_organisation_number_validation_number_also_valid(self):
        org_number_answer = '123qwerty456'
        self.create_number_validation_data(org_type_answer='GOV - Government Agency or Public Body', org_number_answer=org_number_answer)
        self.assertTrue(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))

    def test_organisation_number_validation_invalid(self):
        org_number_answer = 'none'
        self.create_number_validation_data(org_type_answer='LTD  - Limited Company (Ltd or PLC)', org_number_answer=org_number_answer)
        self.assertFalse(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))

    def test_organisation_number_validation_other_question(self):
        org_number_answer = 'none'
        self.create_number_validation_data(org_type_answer='LTD  - Limited Company (Ltd or PLC)', org_number_answer=org_number_answer)
        self.assertTrue(organisation_number_validation(self.ce_certificate, self.org_type_question, {'response': org_number_answer}))

    def test_organisation_number_validation_no_response(self):
        org_number_answer = 'none'
        self.create_number_validation_data(org_type_answer='LTD  - Limited Company (Ltd or PLC)', org_number_answer=org_number_answer)
        self.org_type_response.delete()
        self.assertFalse(organisation_number_validation(self.ce_certificate, self.org_number_question, {'response': org_number_answer}))

    def test_import_new_questions_from_pervade(self):
        deleted_pervade_id = self.survey_question_cep_to_be_deleted.pervade_id
        changes_done = import_new_questions_from_pervade(self.cert_version_cep, self.pervade_data)
        self.survey_question_cep.refresh_from_db()
        self.survey_question_cep_extra_details.refresh_from_db()
        # check questions were updated
        data_1 = self.pervade_data.get(self.survey_question_cep.pervade_id)
        self.assertEqual(self.survey_question_cep.title, data_1.get('description'))
        self.assertIsNone(self.survey_question_cep.moreinfo)
        # pervade title is not updated
        self.assertNotEqual(self.survey_question_cep.pervade_title, data_1.get('name'))
        # now check the second existing question
        data_2 = self.pervade_data.get(self.survey_question_cep_extra_details.pervade_id)
        self.assertEqual(self.survey_question_cep_extra_details.title, data_2.get('description').split('<br><br>')[0])
        self.assertEqual(self.survey_question_cep_extra_details.response_type, SurveyQuestion.BOOLEAN)
        self.assertEqual(self.survey_question_cep_extra_details.widget, SurveyQuestion.WIDGET_CONSCIOUS)
        self.assertEqual(self.survey_question_cep_extra_details.order, 3)
        self.assertEqual(self.survey_question_cep_extra_details.pervade_compliance_fields, {})
        self.assertEqual(self.survey_question_cep_extra_details.moreinfo, data_2.get('description').split('<br><br>')[1])
        # check count of questions updated
        self.assertSequenceEqual(changes_done['updated'], [self.survey_question_cep.pk, self.survey_question_cep_extra_details.pk])
        # now check created questions
        self.assertEqual(len(changes_done['created']), len(self.pervade_data.keys()) - 2)
        # check that the questions created are the same as the pervade data
        for question_id in changes_done['created']:
            question = SurveyQuestion.objects.get(pk=question_id)
            data = self.pervade_data.get(question.pervade_id)
            self.assertEqual(question.title, data.get('description').split('<br>')[0])
            if not question.pervade_id == 'e4cc81229381abdb028887450c3065dac82dc222bffe54db6490a57a81f94a1e':
                self.assertEqual(question.response_type, SurveyQuestion.TEXT)
                self.assertEqual(question.widget, SurveyQuestion.WIDGET_DEFAULT)
                self.assertEqual(question.pervade_compliance_fields, {})
            else:
                self.assertEqual(question.response_type, SurveyQuestion.BOOLEAN)
                self.assertEqual(question.widget, SurveyQuestion.WIDGET_CONSCIOUS)
                self.assertEqual(
                    question.pervade_compliance_fields,
                    {"b714f324142c5e3376c54134925c9db787405ecd8078bb84c88c0acea9f3b9bb": {"name": "Applicant Notes","type": "textarea"}}
                )
            self.assertEqual(question.pervade_title, data.get('name'))
            if '<br>' in data.get('description'):
                self.assertEqual(question.moreinfo, data.get('description').split('<br><br>')[1])
        self.assertIsNone(SurveyQuestion.objects.filter(pervade_id=deleted_pervade_id).first())


    def test_handle_questions_with_parent_yes_no_radio_button_multiple_certifications(self):
        """
        Regression test case to prove that the way a question was answered previously
        on a separate survey does not influence the answers on another survey.
        What the organisation has answered on Response Set 1 should in no way
        influence the answers on Response Set 2.

        +----------------+
        |  Organisation  |
        +----------------+
               |
               |  (has multiple certifications)
               |
        +------v---------+     +------v---------+
        | Certification 1 |     | Certification 2 |
        +----------------+     +----------------+
               |                       |
               |                       |
        +------v---------+     +------v---------+
        |    Survey 1    |     |    Survey 2    |
        +----------------+     +----------------+
               |                       |
               |                       |
               |         +-------------+
               |         |
               v         v
              +------------------------+
              |       Questions        |
              |------------------------|
              | - Parent Question      |
              | - Child Question       |
              +------------------------+
               |                      |
               |                      |
        +------v---------+     +------v---------+
        | Response Set 1 |     | Response Set 2 |
        |----------------|     |----------------|
        | Parent: TRUE   |     | Parent: FALSE  |
        | Child: FALSE   |     | Child: N/A     |
        +----------------+     +----------------+
        """
        other_cert_type = CertificateType.objects.create(
            type=CYBER_ESSENTIALS
        )
        other_cert_version = CertificationVersion.objects.create(
            type=other_cert_type,
            version_number=1.0,
            default=True,
            release_date=timezone.now().date() - timedelta(weeks=1)
        )
        second_certification = OrganisationCertification.objects.create(
            organisation=self.organisation,
            version=other_cert_version,
        )
        second_survey = CertificationSurvey.objects.create(
            certificate=second_certification,
            datetime_started=timezone.now() - timedelta(days=30)
        )

        # Create a common parent question
        parent_question = SurveyQuestionFactory(
            response_type=SurveyQuestion.BOOLEAN,
            topic=self.question_topic,
            required=True,
            version=self.cert_version,
            title="Parent question",
            order=3,
            widget=SurveyQuestion.WIDGET_CONSCIOUS,
            show_children_on=True,
        )

        # Create a common child question
        child_question = SurveyQuestionFactory(
            response_type=SurveyQuestion.BOOLEAN,
            topic=self.question_topic,
            required=True,
            version=self.cert_version,
            title="Child question",
            order=4,
            parent=parent_question,
            widget=SurveyQuestion.WIDGET_CONSCIOUS,
        )

        # Create responses for the first certification
        # Original certification - parent response is True
        original_parent_response = SurveyResponse.objects.create(
            survey=self.ce_certificate_survey,
            question=parent_question,
            value_boolean=True,
            not_applicable=False
        )
        # Original certification - child response
        original_child_response = SurveyResponse.objects.create(
            survey=self.ce_certificate_survey,
            question=child_question,
            value_boolean=False,
            not_applicable=False
        )

        # Create responses for the second certification
        # Second certification - parent response is True
        second_parent_response = SurveyResponse.objects.create(
            survey=second_survey,
            question=parent_question,
            value_boolean=False,
            not_applicable=False
        )
        # Second certification - child response
        second_child_response = SurveyResponse.objects.create(
            survey=second_survey,
            question=child_question,
            value_boolean=True,
            not_applicable=False
        )

        # Call the method that is being tested.
        ret = handle_questions_with_parent_yes_no_radio_button(child_question, second_certification)

        self.assertTrue(ret)

        # Refresh to get updated values
        second_parent_response.refresh_from_db()
        second_child_response.refresh_from_db()

        # This is the most important part:
        # the child response should become not_applicable,
        # and the value turn to None.
        # as its parent response is False.
        self.assertIsNone(second_child_response.value_boolean)
        self.assertTrue(second_child_response.not_applicable)

        # This is the other certification that should not be changed at all.
        self.assertTrue(original_parent_response.value_boolean)
        self.assertFalse(original_child_response.value_boolean)


def create_parent_2_parent_child(certification):
    """
    Simulate a parent_2 - child_2 question pair.
    """
    topic = SurveyQuestionTopicFactory(order=2, title='Insurance')
    parent_2_question = SurveyQuestionFactory(
        version=certification.version,
        order=1,
        response_type='boolean',
        show_children_on_2=False,
        widget=SurveyQuestion.WIDGET_CONSCIOUS,
        required=True,
        code='iasme_insurance_opt_in',
        topic=topic,
    )
    child_2_question = SurveyQuestionFactory(
        order=2,
        parent=None,
        parent_2=parent_2_question,
        show_children_on_2=False,
        widget=SurveyQuestion.WIDGET_RADIO,
        required=True,
        code='iasme_insurance_why_not',
        topic=topic,
        version=certification.version,
    )

    parent_2_response = SurveyResponseFactory(
        question=parent_2_question,
        survey=certification.survey,
        value_boolean=True,
    )
    return parent_2_question, parent_2_response, child_2_question
